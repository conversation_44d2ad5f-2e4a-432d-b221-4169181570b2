#!/usr/bin/env node

/**
 * Bundle 分析脚本
 * 用于分析打包后的文件大小和依赖关系
 */

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

console.log('📊 开始分析 Bundle 大小...')

// 检查是否安装了 webpack-bundle-analyzer
try {
  require.resolve('webpack-bundle-analyzer')
} catch (error) {
  console.log('📦 安装 webpack-bundle-analyzer...')
  execSync('npm install --save-dev webpack-bundle-analyzer', { stdio: 'inherit' })
}

// 构建生产版本
console.log('🔨 构建生产版本...')
try {
  execSync('npm run build:prod', { stdio: 'inherit' })
} catch (error) {
  console.error('❌ 构建失败:', error.message)
  process.exit(1)
}

// 分析 bundle
console.log('🔍 启动 Bundle 分析器...')
try {
  execSync('npx webpack-bundle-analyzer dist/static/js/*.js', { stdio: 'inherit' })
} catch (error) {
  console.warn('⚠️  Bundle 分析器启动失败，尝试手动分析...')
  
  // 手动分析文件大小
  const distPath = path.join(process.cwd(), 'dist')
  if (fs.existsSync(distPath)) {
    console.log('\n📈 文件大小分析:')
    analyzeFiles(distPath)
  }
}

function analyzeFiles(dir, prefix = '') {
  const files = fs.readdirSync(dir)
  
  files.forEach(file => {
    const filePath = path.join(dir, file)
    const stat = fs.statSync(filePath)
    
    if (stat.isDirectory()) {
      console.log(`${prefix}📁 ${file}/`)
      analyzeFiles(filePath, prefix + '  ')
    } else {
      const size = formatFileSize(stat.size)
      const ext = path.extname(file)
      const icon = getFileIcon(ext)
      console.log(`${prefix}${icon} ${file} (${size})`)
    }
  })
}

function formatFileSize(bytes) {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

function getFileIcon(ext) {
  const icons = {
    '.js': '📜',
    '.css': '🎨',
    '.html': '📄',
    '.png': '🖼️',
    '.jpg': '🖼️',
    '.jpeg': '🖼️',
    '.gif': '🖼️',
    '.svg': '🎭',
    '.ico': '🔖',
    '.map': '🗺️',
    '.gz': '📦'
  }
  return icons[ext] || '📄'
}

console.log('\n💡 优化建议:')
console.log('1. 使用 npm run dev:fast 跳过 ESLint 检查')
console.log('2. 考虑使用 CDN 加载大型第三方库')
console.log('3. 启用 gzip 压缩')
console.log('4. 使用图片压缩工具优化图片资源')
console.log('5. 考虑使用 Tree Shaking 移除未使用的代码')
