#!/usr/bin/env node

/**
 * 开发环境优化脚本
 * 用于清理缓存、优化编译速度
 */

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

console.log('🚀 开始优化开发环境...')

// 清理缓存目录
const cacheDirs = [
  'node_modules/.cache',
  '.cache',
  'dist',
  '.temp'
]

cacheDirs.forEach(dir => {
  const fullPath = path.join(process.cwd(), dir)
  if (fs.existsSync(fullPath)) {
    console.log(`🗑️  清理缓存目录: ${dir}`)
    try {
      execSync(`rm -rf "${fullPath}"`, { stdio: 'inherit' })
    } catch (error) {
      console.warn(`⚠️  清理 ${dir} 失败:`, error.message)
    }
  }
})

// 检查并优化 package.json 脚本
const packageJsonPath = path.join(process.cwd(), 'package.json')
if (fs.existsSync(packageJsonPath)) {
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'))
  
  // 添加优化的开发脚本
  if (!packageJson.scripts['dev:fast']) {
    packageJson.scripts['dev:fast'] = 'vue-cli-service serve --skip-plugins @vue/cli-plugin-eslint'
    console.log('✅ 添加快速开发脚本: npm run dev:fast')
  }
  
  if (!packageJson.scripts['dev:clean']) {
    packageJson.scripts['dev:clean'] = 'npm run clear-cache && npm run dev'
    console.log('✅ 添加清理缓存开发脚本: npm run dev:clean')
  }
  
  fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2))
}

console.log('✅ 开发环境优化完成!')
console.log('')
console.log('💡 建议使用以下命令启动开发服务器:')
console.log('   npm run dev:fast    # 跳过ESLint检查，更快启动')
console.log('   npm run dev:clean   # 清理缓存后启动')
console.log('   npm run dev         # 正常启动')
