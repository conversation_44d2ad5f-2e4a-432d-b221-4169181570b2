# 页面标题
VUE_APP_TITLE = develop通州管理系统

# 开发环境配置
ENV = 'development'

# 若依管理系统/开发环境
VUE_APP_BASE_API = '/dev-api'

# 路由懒加载
VUE_CLI_BABEL_TRANSPILE_MODULES = true

# Mock配置 - 是否启用Mock拦截器
VUE_APP_ENABLE_MOCK = true

# 现场服务部署
VUE_APP_TRAGET_IP = 'http://localhost:1025'

# maxKB AI助手配置
VUE_APP_MAXKB_BASE_URL = 'http://***********:8080'
VUE_APP_MAXKB_API_KEY = 'application-ce580c6f7987cb4b4abfcc35ccff0dad'
VUE_APP_MAXKB_APPLICATION_ID = '4796eee26cc51a64'

# 开发环境性能优化
# 禁用 source map 以提高构建速度
VUE_APP_GENERATE_SOURCEMAP = false

# 启用文件系统缓存
VUE_APP_CACHE_TYPE = filesystem

# 减少编译时的内存使用
VUE_APP_PARALLEL = false
