# Mock拦截器控制文档

## 概述

本项目使用环境变量来控制Mock拦截器的启用/禁用状态，可以根据不同的运行模式灵活配置是否使用模拟数据。

## 配置说明

### 环境变量

通过 `VUE_APP_ENABLE_MOCK` 环境变量控制Mock拦截器的启用状态：

- `VUE_APP_ENABLE_MOCK = true` - 启用Mock拦截器
- `VUE_APP_ENABLE_MOCK = false` - 禁用Mock拦截器

### 配置文件

#### 开发环境 (`.env.development`)
```bash
# Mock配置 - 是否启用Mock拦截器
VUE_APP_ENABLE_MOCK = true
```

#### 生产环境 (`.env.production`)
```bash
# Mock配置 - 生产环境禁用Mock拦截器
VUE_APP_ENABLE_MOCK = false
```

## 运行模式

### 1. 开发模式 (`npm run dev`)
- 使用 `.env.development` 配置
- `VUE_APP_ENABLE_MOCK = true`
- **Mock拦截器启用** ✅
- 所有API请求返回模拟数据

### 2. 生产配置开发模式 (`npm run dev:prod`)
- 使用 `.env.production` 配置
- `VUE_APP_ENABLE_MOCK = false`
- **Mock拦截器禁用** ❌
- API请求发送到真实后端服务器

### 3. 生产构建 (`npm run build:prod`)
- 使用 `.env.production` 配置
- `VUE_APP_ENABLE_MOCK = false`
- **Mock拦截器禁用** ❌
- 构建用于生产部署的代码

## 控制逻辑

### main.js 中的实现
```javascript
// 引入mock拦截器 - 根据环境变量控制是否启用
// VUE_APP_ENABLE_MOCK 为 true 时启用mock拦截器
if (process.env.VUE_APP_ENABLE_MOCK === 'true') {
  import('@/utils/mockInterceptor')
  console.log('%c[Mock] Mock拦截器已启用 - 当前模式:', 'color: #44C991; font-weight: bold;', process.env.NODE_ENV)
} else {
  console.log('%c[Mock] Mock拦截器已禁用 - 当前模式:', 'color: #FF6B6B; font-weight: bold;', process.env.NODE_ENV)
}
```

### 控制台输出

#### Mock启用时
```
[Mock] Mock拦截器已启用 - 当前模式: development
[Mock] Mock拦截器已启用，API请求将返回模拟数据
[Mock] 可用的mock方法 (enterprise): [...]
[Mock] 可用的mock方法 (user): [...]
```

#### Mock禁用时
```
[Mock] Mock拦截器已禁用 - 当前模式: development
```

## 使用场景

### 开发阶段
- **前端独立开发** - 启用Mock，使用模拟数据
- **后端联调测试** - 禁用Mock，连接真实API
- **功能演示** - 启用Mock，展示完整功能

### 测试阶段
- **单元测试** - 启用Mock，隔离外部依赖
- **集成测试** - 禁用Mock，测试真实集成
- **E2E测试** - 根据需要选择启用/禁用

### 部署阶段
- **开发环境部署** - 可选择启用/禁用
- **测试环境部署** - 通常禁用Mock
- **生产环境部署** - 必须禁用Mock

## 自定义配置

### 临时启用/禁用Mock

#### 方法1：修改环境变量文件
```bash
# 在 .env.development 或 .env.production 中修改
VUE_APP_ENABLE_MOCK = false
```

#### 方法2：命令行覆盖
```bash
# 临时禁用Mock
VUE_APP_ENABLE_MOCK=false npm run dev

# 临时启用Mock
VUE_APP_ENABLE_MOCK=true npm run dev:prod
```

### 创建自定义环境

#### 创建 `.env.local` 文件
```bash
# 本地开发专用配置，不会被git跟踪
VUE_APP_ENABLE_MOCK = false
VUE_APP_BASE_API = 'http://localhost:8080'
```

## 故障排除

### 问题1：Mock没有生效
**检查项：**
1. 确认 `VUE_APP_ENABLE_MOCK = true`
2. 检查控制台是否有Mock启用日志
3. 确认mockInterceptor.js文件存在
4. 检查网络面板，API请求是否被拦截

### 问题2：API请求失败
**检查项：**
1. 确认 `VUE_APP_ENABLE_MOCK = false`
2. 检查后端服务是否启动
3. 确认API地址配置正确
4. 检查跨域配置

### 问题3：环境变量不生效
**解决方案：**
1. 重启开发服务器
2. 清除浏览器缓存
3. 检查环境变量文件语法
4. 确认变量名拼写正确

## 最佳实践

### 1. 开发流程
```bash
# 1. 前端开发阶段 - 使用Mock
npm run dev

# 2. 后端联调阶段 - 禁用Mock
npm run dev:prod

# 3. 生产部署 - 自动禁用Mock
npm run build:prod
```

### 2. 团队协作
- 在README中明确说明Mock控制方法
- 统一团队的Mock使用规范
- 在代码审查中检查Mock配置

### 3. 部署配置
- 生产环境必须禁用Mock
- 测试环境根据需要配置
- 使用CI/CD自动检查Mock状态

## 相关文件

- `src/main.js` - Mock控制逻辑
- `src/utils/mockInterceptor.js` - Mock拦截器实现
- `mock/` - Mock数据定义
- `.env.development` - 开发环境配置
- `.env.production` - 生产环境配置

## 更新日志

### v1.0.0 (2024-12-19)
- ✅ 实现基于环境变量的Mock控制
- ✅ 支持开发/生产模式切换
- ✅ 添加控制台日志提示
- ✅ 完善文档说明
