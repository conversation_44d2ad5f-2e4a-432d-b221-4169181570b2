# maxKB API调用流程修正文档

## 概述

本文档详细说明了根据maxKB官方API规范修正AI演示页面的API调用流程，确保严格按照三步骤顺序执行。

## 正确的API调用流程

### 📋 **官方规范的三步骤**

#### **步骤1: 获取应用信息** ✅
```
GET /api/application/profile
```
- **调用时机**: 页面进入时立即调用
- **目的**: 获取应用详细信息（应用ID、name等）
- **返回**: 应用配置信息，其中id字段是步骤2的必需参数application_id

#### **步骤2: 打开会话** ✅
```
GET /api/application/{application_id}/chat/open
```
- **调用时机**: 用户点击新建对话或首次发送消息时
- **参数**: application_id (来自步骤1的response.id)
- **返回格式**: 
```json
{
  "code": 200,
  "message": "成功",
  "data": "f2756af8-41dd-11f0-b546-0242ac120003"
}
```
- **说明**: 返回结果中的data字段是步骤3的必传参数chat_id

#### **步骤3: 发送消息** ✅
```
POST /api/application/chat_message/${chat_id}
```
- **调用时机**: 用户在输入框输入消息并发送时
- **参数**: chat_id (来自步骤2的response.data)
- **请求体**:
```json
{
  "message": "DataEase 支持哪些图表?",
  "re_chat": false,
  "stream": true
}
```

## 代码实现修正

### 🔧 **API函数修正**

#### **步骤1: 获取应用信息** ✅
```javascript
/**
 * 步骤1: 获取应用配置信息 (根据maxKB官方文档)
 * 接口: GET /api/application/profile
 * @returns {Promise}
 */
export function getApplicationProfile() {
  return maxkbRequest({
    url: '/api/application/profile',
    method: 'get'
  })
}
```

#### **步骤2: 打开会话** ✅
```javascript
/**
 * 步骤2: 打开会话，获取会话ID (根据maxKB官方文档)
 * @param {string} applicationId - 应用ID
 * @returns {Promise}
 */
export function openChat(applicationId) {
  return maxkbRequest({
    url: `/api/application/${applicationId}/chat/open`,
    method: 'get'
  })
}
```

#### **步骤3: 发送消息** ✅
```javascript
/**
 * 步骤3: 发送消息到maxKB AI助手 (根据官方文档)
 * @param {Object} params
 * @param {string} params.chat_id - 会话ID (必填)
 * @param {string} params.message - 用户消息
 * @param {boolean} params.re_chat - 是否重新对话
 * @param {boolean} params.stream - 是否流式输出
 * @returns {Promise}
 */
export function sendMessage(params) {
  if (!params.chat_id) {
    const error = new Error('chat_id is required')
    console.error('[maxKB API] sendMessage 参数错误:', error.message)
    throw error
  }
  
  const requestData = {
    message: params.message || '',
    re_chat: params.re_chat || false,
    stream: params.stream || true
  }
  
  return maxkbRequest({
    url: `/api/application/chat_message/${params.chat_id}`,
    method: 'post',
    data: requestData
  })
}
```

### 🔧 **前端调用流程修正**

#### **页面初始化 (步骤1)** ✅
```javascript
// 在mounted()中调用
async checkConnection() {
  try {
    console.log('[demoAI] 步骤1: 调用 /api/application/profile 获取应用信息...')
    const response = await getApplicationProfile()
    
    // 存储应用信息供后续使用
    this.applicationInfo = response
    this.connectionStatus = '已连接'
    
    console.log('[demoAI] 步骤1 - 获取应用信息成功:', response)
    console.log('[demoAI] 应用ID:', response?.id)
    console.log('[demoAI] 应用名称:', response?.name)
    
  } catch (error) {
    this.connectionStatus = '连接失败'
    this.applicationInfo = null
    console.error('[demoAI] 步骤1 失败 - 连接maxKB失败:', error)
  }
}
```

#### **创建会话 (步骤2)** ✅
```javascript
// 在用户首次发送消息或点击新建对话时调用
async createMaxKBConversation() {
  try {
    // 检查是否已获取应用信息
    if (!this.applicationInfo || !this.applicationInfo.id) {
      throw new Error('无法获取应用信息，请检查网络连接')
    }
    
    console.log('[demoAI] 步骤2: 调用 openChat 创建会话...')
    console.log('[demoAI] 使用应用ID:', this.applicationInfo.id)
    
    // 直接调用步骤2的API
    const chatResponse = await openChat(this.applicationInfo.id)
    console.log('[demoAI] 步骤2 - 打开会话响应:', chatResponse)
    
    // 根据官方响应格式处理：{ "code": 200, "message": "成功", "data": "chat_id" }
    let chatId = null
    if (chatResponse && chatResponse.data) {
      chatId = chatResponse.data
    } else if (chatResponse && typeof chatResponse === 'string') {
      chatId = chatResponse
    } else if (chatResponse && chatResponse.id) {
      chatId = chatResponse.id
    }
    
    if (!chatId) {
      throw new Error('会话响应中缺少 chat_id')
    }
    
    this.conversationId = chatId
    console.log('[demoAI] 步骤2 - 会话创建成功:', {
      chatId: chatId,
      applicationId: this.applicationInfo.id,
      applicationName: this.applicationInfo.name
    })
    
  } catch (error) {
    console.error('[demoAI] 步骤2 失败 - 创建会话失败:', error)
    // 降级处理
    this.conversationId = 'local_' + Date.now()
  }
}
```

#### **发送消息 (步骤3)** ✅
```javascript
// 在用户发送消息时调用
async simulateAIResponse(userMessage) {
  try {
    // 如果没有会话ID，先创建会话
    if (!this.conversationId) {
      await this.createMaxKBConversation()
    }
    
    console.log('[demoAI] 步骤3: 发送消息...')
    console.log('[demoAI] 使用chat_id:', this.conversationId)
    
    const response = await sendMessage({
      message: userMessage,
      chat_id: this.conversationId,
      re_chat: false,
      stream: true
    })
    
    // 处理响应...
    
  } catch (error) {
    console.error('[demoAI] 步骤3 失败 - 发送消息失败:', error)
    // 降级处理
  }
}
```

## 错误处理增强

### 🛡️ **详细的错误日志**

#### **请求拦截器** ✅
```javascript
maxkbRequest.interceptors.request.use(config => {
  // 添加Bearer前缀
  if (MAXKB_CONFIG.apiKey) {
    config.headers['Authorization'] = `Bearer ${MAXKB_CONFIG.apiKey}`
  }
  
  const fullUrl = `${config.baseURL}${config.url}`
  console.log(`[maxKB API] 请求: ${config.method?.toUpperCase()} ${fullUrl}`)
  console.log(`[maxKB API] Authorization: Bearer ${MAXKB_CONFIG.apiKey}`)
  console.log(`[maxKB API] 请求数据:`, config.data)
  
  return config
})
```

#### **响应拦截器** ✅
```javascript
maxkbRequest.interceptors.response.use(response => {
  const fullUrl = `${response.config.baseURL}${response.config.url}`
  console.log(`[maxKB API] 响应成功: ${response.status} ${fullUrl}`)
  console.log(`[maxKB API] 响应数据:`, response.data)
  return response.data
}, error => {
  const fullUrl = error.config ? `${error.config.baseURL}${error.config.url}` : '未知地址'
  console.error(`[maxKB API] 响应错误: ${fullUrl}`)
  console.error(`[maxKB API] 接口地址: ${error.config?.baseURL}${error.config?.url}`)
  console.error(`[maxKB API] 请求方法: ${error.config?.method?.toUpperCase()}`)
  console.error(`[maxKB API] 请求数据:`, error.config?.data)
  console.error(`[maxKB API] 响应数据:`, error.response?.data)
  
  // 详细的错误分类处理...
})
```

## 调用时机

### 📋 **正确的调用时机**

| 步骤 | 调用时机 | 触发条件 | 数据存储 |
|------|----------|----------|----------|
| **步骤1** | 页面加载时 | mounted() | 存储到 this.applicationInfo |
| **步骤2** | 首次对话时 | 用户发送消息或点击新建对话 | 存储到 this.conversationId |
| **步骤3** | 每次发送消息 | 用户输入消息并发送 | 处理响应内容 |

### 📋 **数据流转**

```
页面加载
    ↓
步骤1: GET /api/application/profile
    ↓ 存储 applicationInfo.id
用户发送消息
    ↓
步骤2: GET /api/application/{applicationInfo.id}/chat/open
    ↓ 存储 response.data 作为 conversationId
步骤3: POST /api/application/chat_message/{conversationId}
    ↓ 处理AI回复
显示回复内容
```

## 验证方法

### 🧪 **浏览器开发者工具检查**

#### **Network面板验证** ✅
1. 页面加载时应看到 `GET /api/application/profile` 请求
2. 首次发送消息时应看到 `GET /api/application/{id}/chat/open` 请求
3. 每次发送消息时应看到 `POST /api/application/chat_message/{chat_id}` 请求

#### **Console面板验证** ✅
1. 查看 `[demoAI] 步骤1:` 日志
2. 查看 `[demoAI] 步骤2:` 日志
3. 查看 `[maxKB API] 请求:` 和 `[maxKB API] 响应:` 日志

## 更新日志

### v4.0.0 (2024-12-19)
- ✅ 修正API调用流程，严格按照官方三步骤执行
- ✅ 修正步骤1的调用时机为页面加载时
- ✅ 修正步骤2的响应处理，正确提取data字段作为chat_id
- ✅ 修正步骤3的请求参数，添加re_chat和stream字段
- ✅ 修正Authorization头格式，添加Bearer前缀
- ✅ 增强错误处理和调试日志
- ✅ 优化数据存储和流转逻辑

## 总结

修正后的API调用流程完全符合maxKB官方规范：

1. **时机正确** - 步骤1在页面加载时执行，步骤2在首次对话时执行，步骤3在每次发送消息时执行
2. **参数正确** - 使用正确的参数名和格式
3. **响应处理正确** - 正确提取和使用API响应中的关键字段
4. **错误处理完善** - 提供详细的错误日志和降级处理
5. **调试友好** - 完整的请求/响应日志便于问题排查

现在AI演示页面的maxKB集成完全符合官方API规范！🎉
