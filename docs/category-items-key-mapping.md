# 企业分类统计数据映射修正文档

## 概述

本文档说明了企业列表页面中categoryItems数据与getEnterpriseCategoryStats接口返回数据的key值映射修正。

## 问题分析

### 🔍 **当前问题**

在src/views/enterprise/index.vue的第258行categoryItems数组中，key值与fetchCategoryStats方法中的映射不一致，导致统计数据无法正确渲染到broad-list容器中。

### 🔍 **不一致的映射**

| categoryItems中的key | fetchCategoryStats中的case | API字段 | 状态 |
|---------------------|---------------------------|---------|------|
| `'totalNum'` | `'all'` | `enterpriseNum` | ❌ 不匹配 |
| `'screenNum'` | `'screen'` | `screenNum` | ❌ 不匹配 |
| `'levelprotectNum'` | `'security'` | `levelprotectNum` | ❌ 不匹配 |
| `'websiteNum'` | `'website'` | `websiteNum` | ❌ 不匹配 |
| `'operatorNuminteger'` | `'operator'` | `operatorNum` | ❌ 不匹配（还有拼写错误） |
| `'netbarNum'` | `'netbar'` | `netbarNum` | ❌ 不匹配 |
| `'wifiNum'` | `'nonbusiness'` | `wifiNum` | ❌ 不匹配 |
| `'other'` | `'other'` | 无对应API字段 | ✅ 匹配 |

## 解决方案

### 🔧 **修正categoryItems的key值**

需要将categoryItems数组中的key值修改为与fetchCategoryStats方法中的case值一致：

#### **修改前** ❌
```javascript
categoryItems: [
  {
    title: '全部单位',
    badge: 0, // 这里是getEnterpriseCategoryStats返回值的totalNum
    backgroundColor: '#F5F7FA',
    backgroundColor_active: '#2A52D7',
    key: 'totalNum', // ❌ 错误的key
  },
  {
    title: '电子屏单位',
    badge: 0, // 这里是getEnterpriseCategoryStats返回值的screenNum
    backgroundColor: '#F5F7FA',
    backgroundColor_active: '#60B8FF',
    key: 'screenNum', // ❌ 错误的key
  },
  {
    title: '等保备案单位',
    badge: '', // 这里是getEnterpriseCategoryStats返回值的levelprotectNum
    backgroundColor: '#F5F7FA',
    backgroundColor_active: '#44C991',
    key: 'levelprotectNum', // ❌ 错误的key
  },
  {
    title: '网站备案单位',
    badge: '',
    backgroundColor: '#F5F7FA',
    backgroundColor_active: '#F5BC6C',
    key: 'websiteNum', // ❌ 错误的key
  },
  {
    title: '运营商单位',
    badge: '',
    backgroundColor: '#F5F7FA',
    backgroundColor_active: '#24A8BB',
    key: 'operatorNuminteger', // ❌ 错误的key（还有拼写错误）
  },
  {
    title: '网吧单位',
    badge: '',
    backgroundColor: '#F5F7FA',
    backgroundColor_active: '#FB6B2A',
    key: 'netbarNum', // ❌ 错误的key
  },
  {
    title: '非经营单位',
    badge: '44',
    backgroundColor: '#F5F7FA',
    backgroundColor_active: '#D789D4',
    key: 'wifiNum', // ❌ 错误的key
  },
  {
    title: '其他',
    badge: '',
    backgroundColor: '#F5F7FA',
    backgroundColor_active: '#95ABD4',
    key: 'other', // ✅ 正确的key
  }
]
```

#### **修改后** ✅
```javascript
categoryItems: [
  {
    title: '全部单位',
    badge: 0, // 这里是getEnterpriseCategoryStats返回值的enterpriseNum
    backgroundColor: '#F5F7FA',
    backgroundColor_active: '#2A52D7',
    key: 'all', // ✅ 对应fetchCategoryStats中的'all'
  },
  {
    title: '电子屏单位',
    badge: 0, // 这里是getEnterpriseCategoryStats返回值的screenNum
    backgroundColor: '#F5F7FA',
    backgroundColor_active: '#60B8FF',
    key: 'screen', // ✅ 对应fetchCategoryStats中的'screen'
  },
  {
    title: '等保备案单位',
    badge: 0, // 这里是getEnterpriseCategoryStats返回值的levelprotectNum
    backgroundColor: '#F5F7FA',
    backgroundColor_active: '#44C991',
    key: 'security', // ✅ 对应fetchCategoryStats中的'security'
  },
  {
    title: '网站备案单位',
    badge: 0, // 这里是getEnterpriseCategoryStats返回值的websiteNum
    backgroundColor: '#F5F7FA',
    backgroundColor_active: '#F5BC6C',
    key: 'website', // ✅ 对应fetchCategoryStats中的'website'
  },
  {
    title: '运营商单位',
    badge: 0, // 这里是getEnterpriseCategoryStats返回值的operatorNum
    backgroundColor: '#F5F7FA',
    backgroundColor_active: '#24A8BB',
    key: 'operator', // ✅ 对应fetchCategoryStats中的'operator'
  },
  {
    title: '网吧单位',
    badge: 0, // 这里是getEnterpriseCategoryStats返回值的netbarNum
    backgroundColor: '#F5F7FA',
    backgroundColor_active: '#FB6B2A',
    key: 'netbar', // ✅ 对应fetchCategoryStats中的'netbar'
  },
  {
    title: '非经营单位',
    badge: 0, // 这里是getEnterpriseCategoryStats返回值的wifiNum
    backgroundColor: '#F5F7FA',
    backgroundColor_active: '#D789D4',
    key: 'nonbusiness', // ✅ 对应fetchCategoryStats中的'nonbusiness'
  },
  {
    title: '其他',
    badge: 0, // 其他类型，暂无对应API字段
    backgroundColor: '#F5F7FA',
    backgroundColor_active: '#95ABD4',
    key: 'other', // ✅ 对应fetchCategoryStats中的'other'
  }
]
```

### 🔧 **fetchCategoryStats方法映射逻辑**

确认fetchCategoryStats方法中的映射逻辑正确：

```javascript
// 获取分类统计数据
fetchCategoryStats() {
  getEnterpriseCategoryStats().then(response => {
    console.log('统计API响应数据:', response);
    
    if (response.code === 200 && response.data) {
      // 新API响应格式：{ code, msg, data: { enterpriseNum, screenNum, ... } }
      const statsData = response.data;
      
      // 更新分类统计数据，映射新API字段
      this.categoryItems = this.categoryItems.map(item => {
        let count = 0;
        switch(item.key) {
          case 'all': // ✅ 对应categoryItems中的key: 'all'
            count = statsData.enterpriseNum || 0;
            break;
          case 'screen': // ✅ 对应categoryItems中的key: 'screen'
            count = statsData.screenNum || 0;
            break;
          case 'security': // ✅ 对应categoryItems中的key: 'security'
            count = statsData.levelprotectNum || 0;
            break;
          case 'website': // ✅ 对应categoryItems中的key: 'website'
            count = statsData.websiteNum || 0;
            break;
          case 'operator': // ✅ 对应categoryItems中的key: 'operator'
            count = statsData.operatorNum || 0;
            break;
          case 'netbar': // ✅ 对应categoryItems中的key: 'netbar'
            count = statsData.netbarNum || 0;
            break;
          case 'nonbusiness': // ✅ 对应categoryItems中的key: 'nonbusiness'
            count = statsData.wifiNum || 0;
            break;
          default:
            count = 0;
        }
        
        return {
          ...item,
          badge: count.toString()
        };
      });
    } else {
      console.warn('获取分类统计数据失败');
    }
  }).catch(error => {
    console.error('获取分类统计数据失败:', error);
  });
}
```

## 数据流转过程

### 🔧 **完整的数据流转**

1. **API调用**: `getEnterpriseCategoryStats()` 调用 `/system/sysEnterprise/stat`
2. **API响应**: 返回 `{ code: 200, data: { enterpriseNum, screenNum, levelprotectNum, websiteNum, operatorNum, netbarNum, wifiNum } }`
3. **数据映射**: `fetchCategoryStats()` 方法根据 `item.key` 映射对应的统计数据
4. **UI渲染**: `broad-list` 容器根据 `categoryItems` 数组渲染分类卡片和统计数字

### 🔧 **映射关系表**

| categoryItems.key | fetchCategoryStats.case | API字段 | 说明 |
|-------------------|------------------------|---------|------|
| `'all'` | `case 'all'` | `enterpriseNum` | 全部单位数量 |
| `'screen'` | `case 'screen'` | `screenNum` | 电子屏单位数量 |
| `'security'` | `case 'security'` | `levelprotectNum` | 等保备案单位数量 |
| `'website'` | `case 'website'` | `websiteNum` | 网站备案单位数量 |
| `'operator'` | `case 'operator'` | `operatorNum` | 运营商单位数量 |
| `'netbar'` | `case 'netbar'` | `netbarNum` | 网吧单位数量 |
| `'nonbusiness'` | `case 'nonbusiness'` | `wifiNum` | 非经营单位数量 |
| `'other'` | `case 'other'` | 无对应字段 | 其他类型（默认为0） |

## 修改步骤

### 🔧 **具体修改步骤**

1. **修改全部单位**:
   ```javascript
   // 第264行
   key: 'totalNum', → key: 'all',
   ```

2. **修改电子屏单位**:
   ```javascript
   // 第271行
   key: 'screenNum', → key: 'screen',
   ```

3. **修改等保备案单位**:
   ```javascript
   // 第278行
   key: 'levelprotectNum', → key: 'security',
   ```

4. **修改网站备案单位**:
   ```javascript
   // 第285行
   key: 'websiteNum', → key: 'website',
   ```

5. **修改运营商单位**:
   ```javascript
   // 第292行
   key: 'operatorNuminteger', → key: 'operator',
   ```

6. **修改网吧单位**:
   ```javascript
   // 第299行
   key: 'netbarNum', → key: 'netbar',
   ```

7. **修改非经营单位**:
   ```javascript
   // 第306行
   key: 'wifiNum', → key: 'nonbusiness',
   ```

8. **其他单位保持不变**:
   ```javascript
   // 第313行
   key: 'other', // 保持不变
   ```

## 验证方法

### 🧪 **功能验证**

1. **API调用验证**:
   - 检查Network面板中的 `/system/sysEnterprise/stat` 请求
   - 确认响应数据格式正确

2. **数据映射验证**:
   - 查看Console中的 `统计API响应数据:` 日志
   - 确认 `fetchCategoryStats` 方法正确执行

3. **UI渲染验证**:
   - 确认broad-list容器中的数字正确显示
   - 验证各分类卡片的统计数据与API返回一致

### 🧪 **调试方法**

```javascript
// 在fetchCategoryStats方法中添加调试日志
console.log('categoryItems before update:', this.categoryItems);
console.log('statsData from API:', statsData);
console.log('categoryItems after update:', this.categoryItems);
```

## 预期效果

### 🎉 **修改完成后的效果**

1. **✅ 数据正确映射**: categoryItems的key值与fetchCategoryStats中的case值一致
2. **✅ 统计数据正确**: broad-list容器显示正确的统计数字
3. **✅ 实时更新**: 当企业数据变化时，统计数字能够正确更新
4. **✅ 分类筛选**: 点击分类卡片能够正确筛选对应的企业列表

## 总结

通过修正categoryItems数组中的key值，确保与fetchCategoryStats方法中的映射逻辑一致，从而实现：

1. **数据一致性** - key值映射正确
2. **功能完整性** - 统计数据正确显示
3. **用户体验** - 分类筛选功能正常工作
4. **代码可维护性** - 逻辑清晰，易于理解和维护

修改完成后，企业分类统计功能将完全正常工作！🎉
