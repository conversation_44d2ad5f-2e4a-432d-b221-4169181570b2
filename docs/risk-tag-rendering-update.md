# 风险标签渲染逻辑更新文档

## 概述

本文档详细说明了企业列表页面中风险标签（risk-tag）的渲染逻辑更新，根据`/system/sysEnterprise/list`接口返回的数据动态生成标签。

## 需求分析

### 🎯 **核心需求**

1. **数据源**: 从`/system/sysEnterprise/list`接口返回的数据中获取标签信息
2. **字段匹配**: 将`riskLevelOptions`的`key`值与API返回的字段进行匹配
3. **条件显示**: 只有当数值大于0时才显示对应标签
4. **标签格式**: 左边显示标签名称，右边显示数值
5. **样式设置**: 边框和文字颜色使用`riskLevelOptions`的`tagColor`属性

### 🎯 **API数据字段**

根据您提供的样例数据：
```json
{
  "screenNum": 1,
  "levelProjectNum": 0,
  "websiteNum": 0,
  "operatorNum": 0,
  "netBarNum": 6,
  "wifiNum": 0,
  "otherNum": 358
}
```

## 修改方案

### 🔧 **1. 修正riskLevelOptions配置**

#### **修改前** ❌
```javascript
riskLevelOptions: [
  { label: '网吧单位', tagColor: '#FB6B2A', key: 'netbarNum'},
  { label: '电子屏单位', tagColor: '#60B8FF', key: 'screenNum'},
  { label: '等保备案单位', tagColor: '#44C991', key: 'levelprotectNum'},
  { label: '网站备案单位', tagColor: '#F5BC6C', key: 'websiteNum'},
  { label: '运营商单位', tagColor: '#24A8BB', key: 'operatorNum'},
  { label: '非经营单位', tagColor: '#D789D4', key: 'nonbusinessNum'},
  { label: '其他', tagColor: '#95ABD4', key: 'otherNum'}
]
```

#### **修改后** ✅
```javascript
riskLevelOptions: [
  { label: '网吧单位', tagColor: '#FB6B2A', key: 'netBarNum' },
  { label: '电子屏单位', tagColor: '#60B8FF', key: 'screenNum' },
  { label: '等保备案单位', tagColor: '#44C991', key: 'levelProjectNum' },
  { label: '网站备案单位', tagColor: '#F5BC6C', key: 'websiteNum' },
  { label: '运营商单位', tagColor: '#24A8BB', key: 'operatorNum' },
  { label: '非经营单位', tagColor: '#D789D4', key: 'wifiNum' },
  { label: '其他', tagColor: '#95ABD4', key: 'otherNum' }
]
```

### 🔧 **2. 新增generateRiskLevelsFromApiData方法**

```javascript
// 根据API数据生成风险标签（数值大于0的才显示）
generateRiskLevelsFromApiData(item) {
  const riskTags = [];
  
  // 遍历riskLevelOptions，根据key值匹配API数据
  this.riskLevelOptions.forEach(option => {
    const value = item[option.key];
    if (value && value > 0) {
      riskTags.push({
        key: option.key,
        label: option.label,
        value: value,
        tagColor: option.tagColor
      });
    }
  });
  
  return riskTags;
}
```

### 🔧 **3. 修改数据映射逻辑**

#### **修改前** ❌
```javascript
// 生成随机风险等级标签（实际项目中应该从后端获取）
riskLevels: this.generateRandomRiskLevels()
```

#### **修改后** ✅
```javascript
// 根据API数据生成风险标签
riskLevels: this.generateRiskLevelsFromApiData(item)
```

### 🔧 **4. 更新getRiskLevelLabel方法**

#### **修改前** ❌
```javascript
// 获取标签文本
getRiskLevelLabel(level) {
  const option = this.riskLevelOptions.find(opt => opt.value === level);
  if (option) {
    return option.label;
  }
  return '未知'; // 默认返回未知
}
```

#### **修改后** ✅
```javascript
// 获取标签文本（支持新的标签格式）
getRiskLevelLabel(riskTag) {
  // 如果是新格式的标签对象
  if (typeof riskTag === 'object' && riskTag.label && riskTag.value) {
    return `${riskTag.label} ${riskTag.value}`;
  }
  
  // 兼容旧格式
  const option = this.riskLevelOptions.find(opt => opt.value === riskTag);
  if (option) {
    return option.label;
  }
  return '未知'; // 默认返回未知
}
```

### 🔧 **5. 更新getRiskLevelColor方法**

#### **修改前** ❌
```javascript
// 获取标签颜色
getRiskLevelColor(level) {
  const option = this.riskLevelOptions.find(opt => opt.value === level);
  if (option) {
    return option.tagColor;
  }
  return '#909399'; // 默认颜色
}
```

#### **修改后** ✅
```javascript
// 获取标签颜色（支持新的标签格式）
getRiskLevelColor(riskTag) {
  // 如果是新格式的标签对象
  if (typeof riskTag === 'object' && riskTag.tagColor) {
    return riskTag.tagColor;
  }
  
  // 兼容旧格式
  const option = this.riskLevelOptions.find(opt => opt.value === riskTag);
  if (option) {
    return option.tagColor;
  }
  return '#909399'; // 默认颜色
}
```

### 🔧 **6. 更新模板渲染逻辑**

#### **修改前** ❌
```vue
<el-tag
  v-for="(level, levelIndex) in item.riskLevels"
  :key="levelIndex"
  plain
  size="mini"
  :style="{ color: getRiskLevelColor(level), borderColor: getRiskLevelColor(level), backgroundColor: getLightenedColor(level) }"
  class="risk-tag"
>
  {{ getRiskLevelLabel(level) }} {{ level }}
</el-tag>
```

#### **修改后** ✅
```vue
<el-tag
  v-for="(riskTag, levelIndex) in item.riskLevels"
  :key="levelIndex"
  plain
  size="mini"
  :style="{ color: getRiskLevelColor(riskTag), borderColor: getRiskLevelColor(riskTag) }"
  class="risk-tag"
>
  {{ getRiskLevelLabel(riskTag) }}
</el-tag>
```

## 数据流转过程

### 🔧 **完整的数据流转**

1. **API调用**: `getEnterpriseList()` 调用 `/system/sysEnterprise/list`
2. **API响应**: 返回包含各种数量字段的企业数据
3. **标签生成**: `generateRiskLevelsFromApiData()` 根据数量字段生成标签对象
4. **UI渲染**: 模板中遍历标签对象，显示标签名称和数值

### 🔧 **标签对象结构**

```javascript
// 生成的标签对象格式
{
  key: 'screenNum',           // 对应API字段名
  label: '电子屏单位',         // 显示的标签名称
  value: 1,                   // API返回的数值
  tagColor: '#60B8FF'         // 标签颜色
}
```

### 🔧 **字段映射关系**

| riskLevelOptions.key | API字段 | 标签名称 | 显示条件 |
|---------------------|---------|----------|----------|
| `netBarNum` | `netBarNum` | 网吧单位 | > 0 |
| `screenNum` | `screenNum` | 电子屏单位 | > 0 |
| `levelProjectNum` | `levelProjectNum` | 等保备案单位 | > 0 |
| `websiteNum` | `websiteNum` | 网站备案单位 | > 0 |
| `operatorNum` | `operatorNum` | 运营商单位 | > 0 |
| `wifiNum` | `wifiNum` | 非经营单位 | > 0 |
| `otherNum` | `otherNum` | 其他 | > 0 |

## 示例效果

### 🎯 **根据样例数据的显示效果**

对于样例数据：
```json
{
  "screenNum": 1,
  "levelProjectNum": 0,
  "websiteNum": 0,
  "operatorNum": 0,
  "netBarNum": 6,
  "wifiNum": 0,
  "otherNum": 358
}
```

将显示以下标签：
- `电子屏单位 1` (蓝色边框 #60B8FF)
- `网吧单位 6` (橙色边框 #FB6B2A)
- `其他 358` (灰蓝色边框 #95ABD4)

不会显示的标签（因为数值为0）：
- 等保备案单位
- 网站备案单位
- 运营商单位
- 非经营单位

## 修改步骤

### 🔧 **具体修改步骤**

1. **修正riskLevelOptions配置** (第338-346行)
2. **添加generateRiskLevelsFromApiData方法** (在methods中)
3. **修改数据映射逻辑** (第527行)
4. **更新getRiskLevelLabel方法** (第647-653行)
5. **更新getRiskLevelColor方法** (第637-644行)
6. **更新模板渲染逻辑** (第127-136行)

## 验证方法

### 🧪 **功能验证**

1. **API数据验证**:
   - 检查Network面板中的 `/system/sysEnterprise/list` 请求
   - 确认响应数据包含各种数量字段

2. **标签生成验证**:
   - 查看Console中的企业数据，确认riskLevels字段格式正确
   - 验证只有数值大于0的字段生成了标签

3. **UI显示验证**:
   - 确认标签显示格式为"标签名称 数值"
   - 验证标签颜色与riskLevelOptions配置一致
   - 检查数值为0的字段不显示标签

### 🧪 **调试方法**

```javascript
// 在generateRiskLevelsFromApiData方法中添加调试日志
console.log('API item data:', item);
console.log('Generated risk tags:', riskTags);

// 在模板中添加调试信息
{{ item.riskLevels }}
```

## 预期效果

### 🎉 **修改完成后的效果**

1. **✅ 动态标签生成**: 根据API数据动态生成风险标签
2. **✅ 条件显示**: 只显示数值大于0的标签
3. **✅ 正确格式**: 标签显示为"标签名称 数值"格式
4. **✅ 样式一致**: 使用riskLevelOptions配置的颜色
5. **✅ 性能优化**: 避免显示无意义的0值标签

## 总结

通过本次更新：

1. **数据驱动** - 标签完全基于API返回的真实数据
2. **条件渲染** - 只显示有意义的标签（数值>0）
3. **格式统一** - 标签格式为"名称 数值"
4. **样式一致** - 使用配置的颜色方案
5. **可维护性** - 清晰的数据映射和方法结构

现在风险标签将完全基于真实的API数据进行渲染，提供更准确和有意义的信息展示！🎉
