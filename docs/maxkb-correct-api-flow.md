# maxKB正确API流程实现文档

## 概述

本文档详细说明了根据maxKB正确的API调用流程重新实现AI对话功能，确保按照官方规范的三步骤进行API调用。

## 正确的API流程

### 📋 **官方规范的三步骤**

#### **步骤1: 获取应用信息** ✅
```
GET /api/application/profile
```
- **目的**: 获取应用详细信息（应用ID、name等）
- **返回**: 应用配置信息，包含application_id

#### **步骤2: 打开会话** ✅
```
GET /api/application/{application_id}/chat/open
```
- **目的**: 获取应用ID对应的会话ID
- **参数**: application_id (必填，来自步骤1)
- **返回**: 会话信息，包含chat_id

#### **步骤3: 进行对话** ✅
```
POST /api/application/chat_message/{chat_id}
```
- **目的**: 使用会话ID进行对话
- **参数**: chat_id (必填，来自步骤2)
- **返回**: AI回复内容

## 代码实现

### 🔧 **API函数重构**

#### **步骤1: 获取应用信息** ✅
```javascript
/**
 * 步骤1: 获取应用配置信息 (根据maxKB官方文档)
 * @returns {Promise}
 */
export function getApplicationProfile() {
  return maxkbRequest({
    url: '/api/application/profile',
    method: 'get'
  })
}
```

#### **步骤2: 打开会话** ✅
```javascript
/**
 * 步骤2: 打开会话，获取会话ID (根据maxKB官方文档)
 * @param {string} applicationId - 应用ID
 * @returns {Promise}
 */
export function openChat(applicationId) {
  return maxkbRequest({
    url: `/api/application/${applicationId}/chat/open`,
    method: 'get'
  })
}
```

#### **步骤3: 发送消息** ✅
```javascript
/**
 * 步骤3: 发送消息到maxKB AI助手 (根据官方文档)
 * @param {Object} params
 * @param {string} params.chat_id - 会话ID (必填)
 * @param {string} params.message - 用户消息
 * @param {boolean} params.stream - 是否流式输出
 * @returns {Promise}
 */
export function sendMessage(params) {
  if (!params.chat_id) {
    throw new Error('chat_id is required')
  }
  
  return maxkbRequest({
    url: `/api/application/chat_message/${params.chat_id}`,
    method: 'post',
    data: {
      message: params.message,
      stream: params.stream || false
    }
  })
}
```

### 🔧 **完整流程实现**

#### **createConversation函数** ✅
```javascript
/**
 * 创建新会话 (按照maxKB正确流程)
 * @returns {Promise}
 */
export async function createConversation() {
  try {
    // 步骤1: 获取应用信息
    const appProfile = await getApplicationProfile()
    console.log('[maxKB] 步骤1 - 获取应用信息:', appProfile)
    
    if (!appProfile || !appProfile.id) {
      throw new Error('无法获取应用ID')
    }
    
    // 步骤2: 打开会话
    const chatSession = await openChat(appProfile.id)
    console.log('[maxKB] 步骤2 - 打开会话:', chatSession)
    
    if (!chatSession || !chatSession.id) {
      throw new Error('无法获取会话ID')
    }
    
    return {
      id: chatSession.id,
      applicationId: appProfile.id,
      applicationName: appProfile.name || 'maxKB AI助手'
    }
  } catch (error) {
    console.error('[maxKB] 创建会话失败:', error)
    throw error
  }
}
```

### 🔧 **前端调用更新**

#### **API调用参数修正** ✅
```javascript
// 修正前 ❌
const response = await sendMessage({
  message: userMessage,
  conversation_id: this.conversationId,  // 错误的参数名
  stream: false
})

// 修正后 ✅
const response = await sendMessage({
  message: userMessage,
  chat_id: this.conversationId,  // 正确的参数名
  stream: false
})
```

#### **会话创建流程** ✅
```javascript
// 创建maxKB会话 (按照正确的API流程)
async createMaxKBConversation() {
  try {
    console.log('[maxKB] 开始创建会话...')
    const response = await createConversation()
    
    if (response && response.id) {
      this.conversationId = response.id
      console.log('[maxKB] 会话创建成功:', {
        chatId: response.id,
        applicationId: response.applicationId,
        applicationName: response.applicationName
      })
      
      // 显示应用信息
      if (response.applicationName) {
        this.$message.success(`已连接到: ${response.applicationName}`)
      }
    } else {
      throw new Error('创建会话失败: 无效的响应')
    }
  } catch (error) {
    console.error('[maxKB] 创建会话失败:', error)
    this.$message.error(`创建会话失败: ${error.message}`)
    
    // 生成本地会话ID作为降级方案
    this.conversationId = 'local_' + Date.now()
    console.log('[maxKB] 使用本地会话ID:', this.conversationId)
  }
}
```

## API端点对照表

### 📋 **修正前后对比**

| 步骤 | 修正前 | 修正后 | 说明 |
|------|--------|--------|------|
| **获取应用信息** | `/application/profile` | `/api/application/profile` | 添加/api前缀 |
| **打开会话** | 无此步骤 | `/api/application/{id}/chat/open` | 新增必要步骤 |
| **发送消息** | `/application/chat` | `/api/application/chat_message/{chat_id}` | 使用正确端点 |

### 📋 **参数格式对比**

| 参数 | 修正前 | 修正后 | 说明 |
|------|--------|--------|------|
| **会话标识** | `conversation_id` | `chat_id` | 使用正确的参数名 |
| **应用标识** | 无 | `application_id` | 新增必要参数 |
| **消息内容** | `message` | `message` | 保持不变 |

## 调用流程图

### 📊 **完整API调用流程**

```
用户发起对话
    ↓
步骤1: GET /api/application/profile
    ↓ (获取application_id)
步骤2: GET /api/application/{application_id}/chat/open
    ↓ (获取chat_id)
步骤3: POST /api/application/chat_message/{chat_id}
    ↓ (发送消息并获取回复)
显示AI回复
```

### 📊 **数据流转**

```
getApplicationProfile()
    ↓ 返回: { id: "app_123", name: "AI助手" }
openChat(app_123)
    ↓ 返回: { id: "chat_456" }
sendMessage({ chat_id: "chat_456", message: "你好" })
    ↓ 返回: AI回复内容
```

## 错误处理增强

### 🛡️ **分步骤错误处理**

#### **步骤1错误处理** ✅
```javascript
if (!appProfile || !appProfile.id) {
  throw new Error('无法获取应用ID')
}
```

#### **步骤2错误处理** ✅
```javascript
if (!chatSession || !chatSession.id) {
  throw new Error('无法获取会话ID')
}
```

#### **步骤3错误处理** ✅
```javascript
if (!params.chat_id) {
  throw new Error('chat_id is required')
}
```

### 🛡️ **降级处理机制**

#### **本地会话ID生成** ✅
```javascript
// 生成本地会话ID作为降级方案
this.conversationId = 'local_' + Date.now()
console.log('[maxKB] 使用本地会话ID:', this.conversationId)
```

## 调试信息增强

### 🔍 **详细日志记录**

#### **步骤追踪** ✅
```javascript
console.log('[maxKB] 步骤1 - 获取应用信息:', appProfile)
console.log('[maxKB] 步骤2 - 打开会话:', chatSession)
console.log('[maxKB] 步骤3 - 发送消息:', userMessage)
```

#### **成功信息** ✅
```javascript
console.log('[maxKB] 会话创建成功:', {
  chatId: response.id,
  applicationId: response.applicationId,
  applicationName: response.applicationName
})
```

#### **错误信息** ✅
```javascript
console.error('[maxKB] 创建会话失败:', error)
this.$message.error(`创建会话失败: ${error.message}`)
```

## 验证方法

### 🧪 **浏览器开发者工具检查**

#### **Network面板验证** ✅
1. 查看是否按顺序发出三个请求：
   - `GET /api/application/profile`
   - `GET /api/application/{id}/chat/open`
   - `POST /api/application/chat_message/{chat_id}`

2. 验证请求参数：
   - 步骤2使用步骤1返回的application_id
   - 步骤3使用步骤2返回的chat_id

#### **Console面板验证** ✅
1. 查看步骤追踪日志
2. 确认每个步骤的返回数据
3. 验证会话创建成功信息

### 🧪 **功能测试**

#### **连接测试** ✅
- ✅ 页面加载时成功获取应用信息
- ✅ 显示正确的应用名称
- ✅ 连接状态显示为"已连接"

#### **对话测试** ✅
- ✅ 新建对话时成功创建会话
- ✅ 发送消息使用正确的chat_id
- ✅ 接收到AI回复

## 更新日志

### v3.3.0 (2024-12-19)
- ✅ 实现正确的三步骤API流程
- ✅ 添加打开会话API调用
- ✅ 修正发送消息API端点和参数
- ✅ 增强错误处理和调试信息
- ✅ 完善降级处理机制

## 总结

通过实现正确的三步骤API流程：

1. **规范性** - 完全符合maxKB官方API规范
2. **完整性** - 包含所有必要的API调用步骤
3. **可靠性** - 每个步骤都有错误处理和验证
4. **可调试性** - 提供详细的日志和状态信息

现在AI对话功能按照maxKB官方标准流程运行，确保与maxKB服务的正确集成！🎉
