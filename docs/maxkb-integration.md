# maxKB AI助手集成文档

## 概述

本项目集成了maxKB AI助手，提供智能问答、知识库检索和数据分析等功能。maxKB是一个基于大语言模型的知识库问答系统，支持多种文档格式和智能对话。

## 功能特性

### 🤖 智能对话
- 自然语言理解和生成
- 上下文感知的多轮对话
- 流式响应，实时显示回答过程
- Markdown格式支持

### 📚 知识库问答
- 基于企业知识库的精准问答
- 文档检索和内容提取
- 智能推理和信息整合

### 📊 数据分析
- 智能数据分析和解读
- 报表生成和趋势预测
- 可视化建议和洞察

### 🎨 用户体验
- 现代化聊天界面
- 响应式设计，支持移动端
- 快速提问按钮
- 全屏模式支持

## 技术架构

### 前端组件
- `MaxKBChat.vue` - 核心聊天组件
- `demoAI.vue` - AI问答演示页面
- `src/api/maxkb.js` - API接口封装

### API集成
- RESTful API调用
- Server-Sent Events (SSE) 流式响应
- 会话管理和历史记录
- 错误处理和重试机制

## 配置说明

### 环境变量配置

在 `.env.development` 文件中配置maxKB连接信息：

```bash
# maxKB AI助手配置
VUE_APP_MAXKB_BASE_URL = 'http://localhost:8080'
VUE_APP_MAXKB_API_KEY = 'your-api-key-here'
VUE_APP_MAXKB_APPLICATION_ID = 'your-application-id-here'
```

### 配置参数说明

| 参数 | 说明 | 示例 |
|------|------|------|
| `VUE_APP_MAXKB_BASE_URL` | maxKB服务地址 | `http://localhost:8080` |
| `VUE_APP_MAXKB_API_KEY` | API访问密钥 | `sk-xxx...` |
| `VUE_APP_MAXKB_APPLICATION_ID` | 应用ID | `app-xxx...` |

## 部署指南

### 1. maxKB服务部署

参考 [maxKB官方文档](https://doc.fastgpt.cn/docs/) 部署maxKB服务：

```bash
# 使用Docker部署
docker run -d \
  --name maxkb \
  -p 8080:8080 \
  -e MYSQL_HOST=your-mysql-host \
  -e MYSQL_PORT=3306 \
  -e MYSQL_USER=root \
  -e MYSQL_PASSWORD=your-password \
  -e MYSQL_DB=maxkb \
  maxkb/maxkb:latest
```

### 2. 创建应用

1. 访问maxKB管理界面
2. 创建新的应用
3. 配置知识库和模型
4. 获取应用ID和API密钥

### 3. 前端配置

更新环境变量文件中的配置信息：

```bash
VUE_APP_MAXKB_BASE_URL = 'http://your-maxkb-server:8080'
VUE_APP_MAXKB_API_KEY = 'your-actual-api-key'
VUE_APP_MAXKB_APPLICATION_ID = 'your-actual-app-id'
```

## 使用指南

### 基础使用

1. 访问 `/ai/demo` 页面
2. 选择对话模式（普通对话/知识库问答/数据分析）
3. 输入问题或点击快速提问按钮
4. 查看AI助手的回答

### 组件集成

在其他页面中使用MaxKBChat组件：

```vue
<template>
  <div>
    <max-kb-chat
      title="客服助手"
      :welcome-title="welcomeTitle"
      :welcome-text="welcomeText"
      :quick-questions="quickQuestions"
      :use-stream="true"
      @fullscreen-change="handleFullscreen"
    />
  </div>
</template>

<script>
import MaxKBChat from '@/components/MaxKBChat.vue'

export default {
  components: {
    MaxKBChat
  },
  data() {
    return {
      welcomeTitle: '欢迎使用客服助手',
      welcomeText: '我可以帮助您解决各种问题...',
      quickQuestions: [
        '如何使用系统？',
        '忘记密码怎么办？',
        '联系客服'
      ]
    }
  },
  methods: {
    handleFullscreen(isFullscreen) {
      console.log('全屏状态:', isFullscreen)
    }
  }
}
</script>
```

### API调用

直接调用maxKB API：

```javascript
import { sendMessage, sendStreamMessage } from '@/api/maxkb'

// 发送普通消息
const response = await sendMessage({
  message: '你好',
  conversationId: 'sk-ajfbwhkrpkkoarnsfdgjrntkywruajkpivkniahexyavimhf'
})

// 发送流式消息
await sendStreamMessage({
  message: '请分析数据',
  conversationId: 'sk-ajfbwhkrpkkoarnsfdgjrntkywruajkpivkniahexyavimhf',
  onMessage: (data) => {
    console.log('收到消息:', data.content)
  },
  onComplete: () => {
    console.log('对话完成')
  }
})
```

## 开发模式

### Mock服务

项目内置了mock服务，在没有真实maxKB服务时可以使用模拟数据：

- 自动检测API调用并返回模拟响应
- 支持不同对话模式的差异化回答
- 模拟流式响应效果

### 调试技巧

1. 打开浏览器开发者工具
2. 查看Network面板的API调用
3. 检查Console面板的错误信息
4. 使用Vue DevTools调试组件状态

## 常见问题

### Q: 连接maxKB失败怎么办？
A: 检查以下几点：
- maxKB服务是否正常运行
- 网络连接是否正常
- API密钥和应用ID是否正确
- 跨域配置是否正确

### Q: 如何自定义AI助手的回答？
A: 可以通过以下方式：
- 在maxKB中配置知识库
- 调整模型参数
- 设置系统提示词

### Q: 如何处理大量并发请求？
A: 建议：
- 使用连接池
- 实现请求队列
- 添加限流机制
- 监控服务性能

## 更新日志

### v1.0.0 (2024-12-19)
- ✅ 集成maxKB AI助手
- ✅ 实现智能对话功能
- ✅ 支持流式响应
- ✅ 添加多种对话模式
- ✅ 完善错误处理机制

## 参考资源

- [maxKB官方文档](https://doc.fastgpt.cn/docs/)
- [maxKB GitHub仓库](https://github.com/labring/FastGPT)
- [Vue.js官方文档](https://vuejs.org/)
- [Element UI文档](https://element.eleme.cn/)

## 技术支持

如有问题，请联系开发团队或提交Issue。
