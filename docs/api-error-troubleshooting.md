# API错误排查文档

## 概述

本文档详细说明了在使用 `npm run dev:prod` 模式时可能遇到的API错误及其解决方案，特别是登录时调用 `/prod-api/system/user/getInfo` 接口的相关问题。

## 常见错误类型

### 1. TypeError: Cannot read properties of undefined (reading 'avatar')

#### 错误描述
```
TypeError: Cannot read properties of undefined (reading 'avatar')
    at Object.GetInfo (user.js:72)
```

#### 原因分析
- **Mock禁用**: 使用 `npm run dev:prod` 时Mock被禁用，真实API返回的数据结构与Mock数据不同
- **数据结构差异**: 后端API返回的用户数据结构与前端期望的不匹配
- **字段缺失**: API响应中缺少 `avatar` 字段或用户对象为 `undefined`

#### 解决方案
✅ **已修复** - 使用API适配器和安全的属性访问

### 2. 网络连接错误

#### 错误描述
```
Network Error
Request failed with status code 404/500/502
```

#### 原因分析
- 后端服务未启动
- API地址配置错误
- 跨域问题
- 网络连接问题

#### 解决方案
1. 检查后端服务状态
2. 验证API地址配置
3. 配置跨域设置
4. 检查网络连接

### 3. 认证相关错误

#### 错误描述
```
401 Unauthorized
403 Forbidden
Token expired
```

#### 原因分析
- Token过期或无效
- 权限不足
- 认证配置错误

#### 解决方案
1. 重新登录获取新Token
2. 检查用户权限配置
3. 验证认证流程

## 修复方案详解

### 1. API适配器实现

#### 核心文件
- `src/utils/apiAdapter.js` - API响应适配器
- `src/store/modules/user.js` - 用户状态管理

#### 主要功能
```javascript
// 登录响应适配
export function adaptLoginResponse(response) {
  const data = handleApiResponse(response, { debug: true })
  
  // 支持多种token字段名
  const token = data.access_token || data.token || data.accessToken || data.authToken
  const expiresIn = data.expires_in || data.expiresIn || data.expire || 3600
  
  if (!token) {
    throw new Error('登录响应中未找到有效的token')
  }
  
  return { token, expiresIn, refreshToken, originalData: data }
}

// 用户信息响应适配
export function adaptUserInfoResponse(response) {
  const data = handleApiResponse(response, { debug: true })
  
  // 处理不同的用户数据结构
  let userData = data.user || data.data || data.userInfo || data
  
  if (!userData) {
    throw new Error('响应中未找到用户数据')
  }
  
  // 标准化用户字段
  const userInfo = {
    userId: userData.userId || userData.id || userData.user_id || '',
    userName: userData.userName || userData.username || userData.name || '',
    nickName: userData.nickName || userData.nickname || userData.nick_name || '',
    avatar: userData.avatar || userData.avatarUrl || userData.avatar_url || '',
    // ... 更多字段
  }
  
  return { user: userInfo, roles, permissions, originalData: data }
}
```

### 2. 错误处理机制

#### 统一错误处理
```javascript
export function handleApiError(error, context = {}) {
  console.error('API Error:', error)
  console.error('Context:', context)
  
  if (error.response) {
    const status = error.response.status
    switch (status) {
      case 401: return '登录已过期，请重新登录'
      case 403: return '没有权限访问该资源'
      case 404: return '请求的资源不存在'
      case 500: return '服务器内部错误'
      default: return `请求失败 (${status})`
    }
  } else if (error.request) {
    return '网络连接失败，请检查网络设置'
  } else {
    return error.message || '未知错误'
  }
}
```

#### 安全属性访问
```javascript
// 安全地获取avatar，提供默认值
const avatar = (userInfoData.user.avatar && !isEmpty(userInfoData.user.avatar)) 
  ? userInfoData.user.avatar 
  : defAva
```

### 3. 调试功能

#### API调试日志
```javascript
export function logApiDebug(apiName, params, response) {
  if (process.env.NODE_ENV === 'development') {
    console.group(`🔍 API Debug: ${apiName}`)
    console.log('📤 Request Params:', params)
    console.log('📥 Response:', response)
    console.log('⚙️ Environment:', {
      mock: process.env.VUE_APP_ENABLE_MOCK === 'true',
      env: process.env.NODE_ENV,
      baseUrl: process.env.VUE_APP_BASE_API
    })
    console.groupEnd()
  }
}
```

## 排查步骤

### 1. 检查Mock状态
```bash
# 查看控制台输出
[Mock] Mock拦截器已禁用 - 当前模式: development
```

### 2. 检查API响应
```javascript
// 在浏览器控制台查看
🔍 API Debug: Login
📤 Request Params: {username: "admin"}
📥 Response: {data: {...}, code: 200}
⚙️ Environment: {mock: false, env: "development", baseUrl: "/prod-api"}
```

### 3. 检查网络请求
- 打开浏览器开发者工具
- 查看Network面板
- 检查API请求状态和响应

### 4. 检查错误日志
```javascript
// 查看详细错误信息
API Error: Error: 响应中未找到用户数据
Context: {api: "GetInfo"}
```

## 配置检查清单

### 环境变量配置
- [ ] `VUE_APP_ENABLE_MOCK = false` (生产配置模式)
- [ ] `VUE_APP_BASE_API = '/prod-api'` (API基础路径)
- [ ] 后端服务地址配置正确

### 后端API要求
- [ ] 登录接口返回有效token
- [ ] 用户信息接口返回完整用户数据
- [ ] 响应格式符合前端期望

### 前端配置
- [ ] API适配器正确处理响应
- [ ] 错误处理机制完善
- [ ] 调试日志启用

## 常见问题解答

### Q: 为什么开发模式正常，生产配置模式出错？
A: 开发模式使用Mock数据，生产配置模式连接真实API，数据结构可能不同。

### Q: 如何查看API的真实响应数据？
A: 查看浏览器控制台的API Debug日志或Network面板。

### Q: 如何临时启用Mock进行对比？
A: 使用命令 `VUE_APP_ENABLE_MOCK=true npm run dev:prod`

### Q: 后端API应该返回什么格式的数据？
A: 参考Mock数据格式或查看API适配器支持的字段名。

## 最佳实践

### 1. 开发流程
1. 使用Mock数据进行前端开发
2. 与后端约定API数据格式
3. 使用API适配器处理格式差异
4. 在生产配置模式下测试真实API

### 2. 错误处理
1. 始终使用try-catch包装API调用
2. 提供用户友好的错误提示
3. 记录详细的错误日志
4. 实现降级处理机制

### 3. 调试技巧
1. 启用API调试日志
2. 使用浏览器开发者工具
3. 对比Mock和真实API响应
4. 逐步排查问题原因

## 更新日志

### v1.0.0 (2024-12-19)
- ✅ 实现API响应适配器
- ✅ 添加统一错误处理机制
- ✅ 完善调试功能
- ✅ 修复avatar属性访问错误
- ✅ 支持多种API响应格式
