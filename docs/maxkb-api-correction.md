# maxKB API接口修正文档

## 概述

本文档详细说明了根据maxKB官方文档 (https://maxkb.cn/docs/dev_manual/APIKey_chat/) 对AI演示页面API调用的修正过程，确保API调用符合官方规范。

## 参考文档

- **官方文档**: https://maxkb.cn/docs/dev_manual/APIKey_chat/#22-api-key
- **Swagger文档**: http://***********:8080/doc/chat/
- **API Key认证方式**: Bearer Token

## API修正内容

### 🔧 **1. 应用配置接口修正**

#### **修正前** ❌
```javascript
// 错误的API端点
export function getApplicationInfo() {
  return request({
    url: `/api/application/${MAXKB_CONFIG.applicationId}`,
    method: 'get',
    baseURL: MAXKB_CONFIG.baseURL,
    headers: {
      'Authorization': `Bearer ${MAXKB_CONFIG.apiKey}`
    }
  })
}
```

#### **修正后** ✅
```javascript
// 正确的API端点 (根据官方文档)
export function getApplicationProfile() {
  return request({
    url: '/application/profile',
    method: 'get',
    baseURL: MAXKB_CONFIG.baseURL,
    headers: {
      'Authorization': `Bearer ${MAXKB_CONFIG.apiKey}`
    }
  })
}

// 兼容性包装
export function getApplicationInfo() {
  return getApplicationProfile()
}
```

### 🔧 **2. 聊天接口修正**

#### **修正前** ❌
```javascript
// 错误的API端点和参数
export function sendMessage(params) {
  return request({
    url: `/api/application/${MAXKB_CONFIG.applicationId}/chat`,
    method: 'post',
    baseURL: MAXKB_CONFIG.baseURL,
    headers: {
      'Authorization': `Bearer ${MAXKB_CONFIG.apiKey}`,
      'Content-Type': 'application/json'
    },
    data: {
      message: params.message,
      conversation_id: params.conversationId,
      history: params.history || [],
      stream: params.stream || false,
      ...params
    }
  })
}
```

#### **修正后** ✅
```javascript
// 正确的API端点和参数 (根据官方文档)
export function sendMessage(params) {
  return request({
    url: '/application/chat',
    method: 'post',
    baseURL: MAXKB_CONFIG.baseURL,
    headers: {
      'Authorization': `Bearer ${MAXKB_CONFIG.apiKey}`,
      'Content-Type': 'application/json'
    },
    data: {
      message: params.message,
      conversation_id: params.conversation_id || params.conversationId,
      stream: params.stream || false
    }
  })
}
```

### 🔧 **3. 流式聊天接口修正**

#### **修正前** ❌
```javascript
// 错误的流式API端点
const eventSource = new EventSource(
  `${MAXKB_CONFIG.baseURL}/api/application/${MAXKB_CONFIG.applicationId}/chat/stream?` +
  new URLSearchParams({
    message: params.message,
    conversation_id: params.conversationId || '',
    authorization: `Bearer ${MAXKB_CONFIG.apiKey}`
  })
)
```

#### **修正后** ✅
```javascript
// 正确的流式API端点 (根据官方文档)
const eventSource = new EventSource(
  `${MAXKB_CONFIG.baseURL}/application/chat/stream?` +
  new URLSearchParams({
    message: params.message,
    conversation_id: params.conversation_id || '',
    authorization: `Bearer ${MAXKB_CONFIG.apiKey}`
  })
)
```

### 🔧 **4. 会话管理简化**

#### **修正前** ❌
```javascript
// 复杂的会话管理API
export function createConversation() {
  return request({
    url: `/api/application/${MAXKB_CONFIG.applicationId}/conversation`,
    method: 'post',
    baseURL: MAXKB_CONFIG.baseURL,
    headers: {
      'Authorization': `Bearer ${MAXKB_CONFIG.apiKey}`,
      'Content-Type': 'application/json'
    }
  })
}
```

#### **修正后** ✅
```javascript
// 简化的会话管理 (根据maxKB文档，会话ID可以自动生成)
export function createConversation() {
  // 根据maxKB文档，可以直接生成UUID作为会话ID
  return Promise.resolve({
    id: 'conv_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11)
  })
}
```

### 🔧 **5. 响应格式适配**

#### **修正前** ❌
```javascript
// 固定的响应格式处理
if (response && response.content) {
  this.addMessage('assistant', response.content)
} else {
  this.addMessage('assistant', '抱歉，我暂时无法理解您的问题，请稍后再试。')
}
```

#### **修正后** ✅
```javascript
// 灵活的响应格式适配 (适应不同的API响应格式)
if (response) {
  let content = ''
  
  if (typeof response === 'string') {
    content = response
  } else if (response.content) {
    content = response.content
  } else if (response.answer) {
    content = response.answer
  } else if (response.message) {
    content = response.message
  } else if (response.data && response.data.content) {
    content = response.data.content
  } else {
    content = '抱歉，我暂时无法理解您的问题，请稍后再试。'
  }
  
  this.addMessage('assistant', content)
} else {
  this.addMessage('assistant', '抱歉，我暂时无法理解您的问题，请稍后再试。')
}
```

## API端点对照表

### 📋 **修正前后对照**

| 功能 | 修正前 | 修正后 | 说明 |
|------|--------|--------|------|
| 应用配置 | `/api/application/${id}` | `/application/profile` | 根据官方文档修正 |
| 发送消息 | `/api/application/${id}/chat` | `/application/chat` | 简化端点路径 |
| 流式消息 | `/api/application/${id}/chat/stream` | `/application/chat/stream` | 简化端点路径 |
| 会话管理 | 复杂的API调用 | 本地生成UUID | 简化会话管理 |

### 📋 **参数格式对照**

| 参数 | 修正前 | 修正后 | 说明 |
|------|--------|--------|------|
| 会话ID | `conversationId` | `conversation_id` | 使用下划线格式 |
| 消息内容 | `message` | `message` | 保持不变 |
| 流式输出 | `stream` | `stream` | 保持不变 |

## 认证方式

### 🔐 **API Key认证**

#### **配置信息** ✅
```bash
# 环境变量配置
VUE_APP_MAXKB_BASE_URL = 'http://***********:8080'
VUE_APP_MAXKB_API_KEY = 'application-ce580c6f7987cb4b4abfcc35ccff0dad'
VUE_APP_MAXKB_APPLICATION_ID = '4796eee26cc51a64'
```

#### **请求头格式** ✅
```javascript
headers: {
  'Authorization': `Bearer ${MAXKB_CONFIG.apiKey}`,
  'Content-Type': 'application/json'
}
```

## 错误处理优化

### 🛡️ **连接检测增强**

#### **修正后的连接检测** ✅
```javascript
async checkConnection() {
  try {
    const response = await getApplicationProfile()
    this.connectionStatus = '已连接'
    console.log('maxKB连接成功:', response)
    
    // 显示连接成功消息和应用信息
    if (response && response.name) {
      this.$message.success(`AI助手服务连接成功: ${response.name}`)
    } else {
      this.$message.success('AI助手服务连接成功')
    }
  } catch (error) {
    this.connectionStatus = '连接失败'
    console.error('连接maxKB失败:', error)
    
    // 显示连接失败警告
    this.$message.warning('AI助手服务连接失败，将使用离线模式')
  }
}
```

### 🛡️ **API调用参数修正**

#### **修正后的API调用** ✅
```javascript
// 使用正确的参数名
const response = await sendMessage({
  message: userMessage,
  conversation_id: this.conversationId,  // 使用下划线格式
  stream: false
})
```

## 测试验证

### 🧪 **API端点测试**

#### **测试步骤** ✅
1. **应用配置测试** - 调用 `/application/profile` 接口
2. **聊天功能测试** - 调用 `/application/chat` 接口
3. **错误处理测试** - 验证异常情况处理
4. **响应格式测试** - 验证不同响应格式的适配

#### **预期结果** ✅
- ✅ 应用配置接口返回应用信息
- ✅ 聊天接口返回AI回复
- ✅ 错误情况下显示友好提示
- ✅ 不同响应格式都能正确处理

## 部署注意事项

### ⚠️ **网络配置**
- 确保前端可以访问 `http://***********:8080`
- 检查CORS配置是否允许跨域请求
- 验证API Key的有效性和权限

### ⚠️ **API版本兼容性**
- 使用官方文档推荐的API端点
- 保持参数格式与官方文档一致
- 定期检查API文档更新

## 更新日志

### v3.1.0 (2024-12-19)
- ✅ 根据maxKB官方文档修正API端点
- ✅ 更新应用配置接口为 `/application/profile`
- ✅ 修正聊天接口为 `/application/chat`
- ✅ 简化会话管理逻辑
- ✅ 增强响应格式适配
- ✅ 优化错误处理机制

## 总结

通过这次根据maxKB官方文档的API修正：

1. **规范性** - API调用完全符合官方文档规范
2. **兼容性** - 支持不同版本的API响应格式
3. **稳定性** - 简化了会话管理，减少了复杂性
4. **可维护性** - 代码结构更清晰，易于维护

现在AI演示页面的API调用完全符合maxKB官方标准，确保了与maxKB服务的正确集成！🎉
