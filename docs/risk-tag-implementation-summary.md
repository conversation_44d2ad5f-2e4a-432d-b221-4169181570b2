# 风险标签渲染逻辑实现总结

## 概述

本文档总结了企业列表页面中风险标签（risk-tag）的渲染逻辑实现，根据`/system/sysEnterprise/list`接口返回的数据动态生成标签。

## 已完成的修改

### ✅ **1. riskLevelOptions数据结构**

#### **当前结构** (第338行)
```javascript
riskLevelOptions: {
  'netBarNum': { label: '网吧单位', tagColor: '#FB6B2A' },
  'screenNum': { label: '电子屏单位', tagColor: '#60B8FF' },
  'levelProjectNum': { label: '等保备案单位', tagColor: '#44C991' },
  'websiteNum': { label: '网站备案单位', tagColor: '#F5BC6C' },
  'operatorNum': { label: '运营商单位', tagColor: '#24A8BB' },
  'wifiNum': { label: '非经营单位', tagColor: '#D789D4' },
  'otherNum': { label: '其他', tagColor: '#95ABD4' }
}
```

### ✅ **2. 模板渲染逻辑已更新** (第127-136行)
```vue
<el-tag
  v-for="(riskTag, levelIndex) in item.riskLevels"
  :key="levelIndex"
  plain
  size="mini"
  :style="{ color: getRiskLevelColor(riskTag), borderColor: getRiskLevelColor(riskTag) }"
  class="risk-tag"
>
  {{ getRiskLevelLabel(riskTag) }}
</el-tag>
```

## 需要完成的修改

### 🔧 **1. 修正riskLevelOptions字段名**

需要将第338行的riskLevelOptions修改为正确的字段名：

```javascript
// 当前 ❌
riskLevelOptions: {
  'netbarNum': { label: '网吧单位', tagColor: '#FB6B2A' },
  'levelprotectNum': { label: '等保备案单位', tagColor: '#44C991'},
  'nonbusinessNum': { label: '非经营单位', tagColor: '#D789D4' },
  // ...
}

// 应该修改为 ✅
riskLevelOptions: {
  'netBarNum': { label: '网吧单位', tagColor: '#FB6B2A' },
  'levelProjectNum': { label: '等保备案单位', tagColor: '#44C991' },
  'wifiNum': { label: '非经营单位', tagColor: '#D789D4' },
  // ...
}
```

### 🔧 **2. 添加generateRiskTagsFromApiData方法**

在methods中添加以下方法：

```javascript
// 根据API数据生成风险标签（数值大于0的才显示）
generateRiskTagsFromApiData(item) {
  const riskTags = [];
  
  // 遍历riskLevelOptions，根据字段名匹配API数据
  Object.keys(this.riskLevelOptions).forEach(fieldName => {
    const value = item[fieldName];
    if (value && value > 0) {
      const config = this.riskLevelOptions[fieldName];
      riskTags.push({
        fieldName: fieldName,
        label: config.label,
        value: value,
        tagColor: config.tagColor
      });
    }
  });
  
  return riskTags;
}
```

### 🔧 **3. 修改数据映射逻辑**

将第529行的数据映射修改为：

```javascript
// 当前 ❌
riskLevels: this.generateRandomRiskLevels()

// 应该修改为 ✅
riskLevels: this.generateRiskTagsFromApiData(item)
```

### 🔧 **4. 更新getRiskLevelColor方法**

修改第640-646行的方法：

```javascript
// 获取标签颜色（支持新的标签格式）
getRiskLevelColor(riskTag) {
  // 如果是新格式的标签对象
  if (typeof riskTag === 'object' && riskTag.tagColor) {
    return riskTag.tagColor;
  }
  
  // 兼容旧格式
  const option = this.riskLevelOptions.find(opt => opt.value === riskTag);
  if (option) {
    return option.tagColor;
  }
  return '#909399'; // 默认颜色
}
```

### 🔧 **5. 更新getRiskLevelLabel方法**

修改第649-655行的方法：

```javascript
// 获取标签文本（支持新的标签格式）
getRiskLevelLabel(riskTag) {
  // 如果是新格式的标签对象
  if (typeof riskTag === 'object' && riskTag.label) {
    return riskTag.label;
  }
  
  // 兼容旧格式
  const option = this.riskLevelOptions.find(opt => opt.value === riskTag);
  if (option) {
    return option.label;
  }
  return '未知'; // 默认返回未知
}
```

## 实现逻辑说明

### 🎯 **数据流转过程**

1. **API调用**: `getEnterpriseList()` → `/system/sysEnterprise/list`
2. **数据处理**: `generateRiskTagsFromApiData(item)` 处理每个企业数据
3. **标签生成**: 遍历`riskLevelOptions`，匹配API字段，生成标签对象
4. **UI渲染**: 模板遍历标签对象，显示标签

### 🎯 **标签生成逻辑**

```javascript
// 对于API返回的数据：
{
  "screenNum": 1,
  "levelProjectNum": 0,
  "websiteNum": 0,
  "operatorNum": 0,
  "netBarNum": 6,
  "wifiNum": 0,
  "otherNum": 358
}

// 生成的标签对象：
[
  { fieldName: 'screenNum', label: '电子屏单位', value: 1, tagColor: '#60B8FF' },
  { fieldName: 'netBarNum', label: '网吧单位', value: 6, tagColor: '#FB6B2A' },
  { fieldName: 'otherNum', label: '其他', value: 358, tagColor: '#95ABD4' }
]
```

### 🎯 **字段映射关系**

| API字段 | riskLevelOptions键 | 标签名称 | 显示条件 |
|---------|-------------------|----------|----------|
| `netBarNum` | `netBarNum` | 网吧单位 | > 0 |
| `screenNum` | `screenNum` | 电子屏单位 | > 0 |
| `levelProjectNum` | `levelProjectNum` | 等保备案单位 | > 0 |
| `websiteNum` | `websiteNum` | 网站备案单位 | > 0 |
| `operatorNum` | `operatorNum` | 运营商单位 | > 0 |
| `wifiNum` | `wifiNum` | 非经营单位 | > 0 |
| `otherNum` | `otherNum` | 其他 | > 0 |

## 预期效果

### 🎉 **实现后的效果**

1. **✅ 动态标签**: 根据API真实数据生成标签
2. **✅ 条件显示**: 只显示数值大于0的标签
3. **✅ 正确样式**: 使用riskLevelOptions配置的颜色
4. **✅ 标签内容**: 显示标签名称（不显示数值）
5. **✅ 边框颜色**: 使用tagColor作为边框和文字颜色

### 🎉 **示例显示效果**

对于样例数据，将显示：
- `电子屏单位` (蓝色边框 #60B8FF)
- `网吧单位` (橙色边框 #FB6B2A)  
- `其他` (灰蓝色边框 #95ABD4)

不显示的标签（数值为0）：
- 等保备案单位
- 网站备案单位
- 运营商单位
- 非经营单位

## 修改步骤总结

### 🔧 **需要手动完成的修改**

1. **修正riskLevelOptions字段名** (第338-347行)
   - `'netbarNum'` → `'netBarNum'`
   - `'levelprotectNum'` → `'levelProjectNum'`
   - `'nonbusinessNum'` → `'wifiNum'`

2. **添加generateRiskTagsFromApiData方法** (在methods中)

3. **修改数据映射** (第529行)
   - `this.generateRandomRiskLevels()` → `this.generateRiskTagsFromApiData(item)`

4. **更新getRiskLevelColor方法** (第640-646行)

5. **更新getRiskLevelLabel方法** (第649-655行)

## 验证方法

### 🧪 **测试步骤**

1. **检查API数据**: 确认`/system/sysEnterprise/list`返回正确的数量字段
2. **验证标签生成**: 查看Console中的riskLevels数据格式
3. **确认UI显示**: 验证标签显示和颜色正确
4. **测试条件显示**: 确认数值为0的字段不显示标签

### 🧪 **调试方法**

```javascript
// 在generateRiskTagsFromApiData方法中添加调试
console.log('API item:', item);
console.log('Generated tags:', riskTags);

// 在模板中临时显示数据
{{ item.riskLevels }}
```

## 总结

通过以上修改，风险标签将：

1. **✅ 基于真实数据** - 完全依赖API返回的数量字段
2. **✅ 智能显示** - 只显示有意义的标签（数值>0）
3. **✅ 样式统一** - 使用配置的颜色方案
4. **✅ 性能优化** - 避免显示无用的0值标签
5. **✅ 可维护性** - 清晰的配置和方法结构

完成这些修改后，风险标签功能将完全按照需求正确工作！🎉
