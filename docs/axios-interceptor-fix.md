# axios拦截器冲突修复文档

## 问题描述

在集成maxKB API时发现了一个关键问题：项目的统一axios拦截器会自动为所有请求添加系统的Authorization token，这会覆盖maxKB API专用的Authorization头，导致API认证失败。

## 问题分析

### 🔍 **根本原因**

#### **系统axios拦截器** ❌
在 `src/utils/request.js` 中的请求拦截器：

```javascript
// request拦截器 (第32-34行)
service.interceptors.request.use(config => {
  const isToken = (config.headers || {}).isToken === false
  if (getToken() && !isToken) {
    config.headers['Authorization'] = 'Bearer ' + getToken() // 系统token
  }
  // ... 其他逻辑
})
```

#### **问题影响** ❌
1. **Authorization覆盖** - 系统token覆盖maxKB API Key
2. **认证失败** - maxKB服务无法识别系统token
3. **请求失败** - 所有maxKB API调用返回401错误

#### **预期请求** ✅
```
GET http://***********:8080/application/profile
Authorization: Bearer application-ce580c6f7987cb4b4abfcc35ccff0dad
```

#### **实际请求** ❌
```
GET http://***********:8080/application/profile
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9... (系统token)
```

## 解决方案

### 🔧 **创建独立的axios实例**

#### **修复前** ❌
```javascript
// 使用系统的request实例
import request from '@/utils/request'

export function getApplicationProfile() {
  return request({
    url: '/application/profile',
    method: 'get',
    baseURL: MAXKB_CONFIG.baseURL,
    headers: {
      'Authorization': `Bearer ${MAXKB_CONFIG.apiKey}` // 会被系统拦截器覆盖
    }
  })
}
```

#### **修复后** ✅
```javascript
// 创建专用的axios实例
import axios from 'axios'

// 创建专用于maxKB的axios实例，避免被系统拦截器影响
const maxkbRequest = axios.create({
  baseURL: MAXKB_CONFIG.baseURL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// maxKB专用请求拦截器
maxkbRequest.interceptors.request.use(config => {
  // 为maxKB API添加专用的Authorization头
  if (MAXKB_CONFIG.apiKey) {
    config.headers['Authorization'] = `Bearer ${MAXKB_CONFIG.apiKey}`
  }
  
  console.log(`[maxKB API] 请求: ${config.method?.toUpperCase()} ${config.baseURL}${config.url}`)
  console.log(`[maxKB API] Authorization: Bearer ${MAXKB_CONFIG.apiKey}`)
  
  return config
}, error => {
  console.error('[maxKB API] 请求错误:', error)
  return Promise.reject(error)
})

export function getApplicationProfile() {
  return maxkbRequest({
    url: '/application/profile',
    method: 'get'
  })
}
```

### 🔧 **专用响应拦截器**

#### **错误处理增强** ✅
```javascript
// maxKB专用响应拦截器
maxkbRequest.interceptors.response.use(response => {
  console.log(`[maxKB API] 响应:`, response.data)
  return response.data
}, error => {
  console.error('[maxKB API] 响应错误:', error)
  
  if (error.response) {
    const { status, data } = error.response
    console.error(`[maxKB API] HTTP ${status}:`, data)
    
    if (status === 401) {
      throw new Error('maxKB API认证失败，请检查API Key')
    } else if (status === 404) {
      throw new Error('maxKB API端点不存在')
    } else if (status >= 500) {
      throw new Error('maxKB服务器错误')
    }
  } else if (error.request) {
    throw new Error('无法连接到maxKB服务')
  }
  
  return Promise.reject(error)
})
```

### 🔧 **API函数重构**

#### **sendMessage函数** ✅
```javascript
// 修复前 - 使用系统request
export function sendMessage(params) {
  return request({
    url: '/application/chat',
    method: 'post',
    baseURL: MAXKB_CONFIG.baseURL,
    headers: {
      'Authorization': `Bearer ${MAXKB_CONFIG.apiKey}`, // 会被覆盖
      'Content-Type': 'application/json'
    },
    data: {
      message: params.message,
      conversation_id: params.conversation_id || params.conversationId,
      stream: params.stream || false
    }
  })
}

// 修复后 - 使用专用maxkbRequest
export function sendMessage(params) {
  return maxkbRequest({
    url: '/application/chat',
    method: 'post',
    data: {
      message: params.message,
      conversation_id: params.conversation_id || params.conversationId,
      stream: params.stream || false
    }
  })
}
```

#### **getApplicationProfile函数** ✅
```javascript
// 修复前 - 使用系统request
export function getApplicationProfile() {
  return request({
    url: '/application/profile',
    method: 'get',
    baseURL: MAXKB_CONFIG.baseURL,
    headers: {
      'Authorization': `Bearer ${MAXKB_CONFIG.apiKey}` // 会被覆盖
    }
  })
}

// 修复后 - 使用专用maxkbRequest
export function getApplicationProfile() {
  return maxkbRequest({
    url: '/application/profile',
    method: 'get'
  })
}
```

## 技术细节

### 📋 **axios实例对比**

| 特性 | 系统request | maxkbRequest |
|------|-------------|--------------|
| **baseURL** | `process.env.VUE_APP_BASE_API` | `process.env.VUE_APP_MAXKB_BASE_URL` |
| **Authorization** | 系统Bearer token | maxKB API Key |
| **拦截器** | 系统业务逻辑 | maxKB专用逻辑 |
| **错误处理** | 系统错误格式 | maxKB错误格式 |
| **超时设置** | 10秒 | 30秒 |

### 📋 **请求头对比**

#### **系统API请求** ✅
```
POST /dev-api/system/user/list
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9...
Content-Type: application/json
```

#### **maxKB API请求** ✅
```
POST http://***********:8080/application/chat
Authorization: Bearer application-ce580c6f7987cb4b4abfcc35ccff0dad
Content-Type: application/json
```

### 📋 **调试信息增强**

#### **请求日志** ✅
```javascript
console.log(`[maxKB API] 请求: POST http://***********:8080/application/chat`)
console.log(`[maxKB API] Authorization: Bearer application-ce580c6f7987cb4b4abfcc35ccff0dad`)
```

#### **响应日志** ✅
```javascript
console.log(`[maxKB API] 响应:`, response.data)
```

#### **错误日志** ✅
```javascript
console.error('[maxKB API] HTTP 401:', { error: 'Unauthorized' })
```

## 验证方法

### 🧪 **浏览器开发者工具检查**

#### **Network面板验证** ✅
1. 打开浏览器开发者工具
2. 切换到Network面板
3. 访问AI演示页面
4. 查看 `/application/profile` 请求
5. 验证请求头中的Authorization是否为maxKB API Key

#### **Console面板验证** ✅
1. 查看控制台日志
2. 确认看到 `[maxKB API] 请求:` 日志
3. 确认Authorization头正确
4. 确认没有401认证错误

### 🧪 **功能测试**

#### **连接测试** ✅
- ✅ 页面加载时显示"AI助手服务连接成功"
- ✅ 连接状态显示为"已连接"
- ✅ 没有认证失败的错误提示

#### **对话测试** ✅
- ✅ 发送消息能够正常调用maxKB API
- ✅ 接收到AI回复
- ✅ 没有401或403错误

## 最佳实践

### 📋 **第三方API集成原则**

#### **1. 独立axios实例** ✅
- 为每个第三方服务创建独立的axios实例
- 避免与系统拦截器冲突
- 便于独立配置和调试

#### **2. 专用拦截器** ✅
- 为第三方API创建专用的请求/响应拦截器
- 处理特定的认证和错误格式
- 提供详细的调试信息

#### **3. 配置隔离** ✅
- 使用独立的配置对象
- 避免与系统配置混淆
- 便于维护和更新

#### **4. 错误处理** ✅
- 提供友好的错误提示
- 区分不同类型的错误
- 记录详细的错误日志

## 更新日志

### v3.2.0 (2024-12-19)
- ✅ 创建maxKB专用axios实例
- ✅ 解决系统拦截器冲突问题
- ✅ 添加专用请求/响应拦截器
- ✅ 增强错误处理和调试信息
- ✅ 确保maxKB API认证正确

## 总结

通过创建独立的axios实例，成功解决了系统拦截器与maxKB API认证的冲突问题：

1. **隔离性** - maxKB API请求完全独立于系统API
2. **正确性** - 确保使用正确的API Key进行认证
3. **可调试性** - 提供详细的请求/响应日志
4. **可维护性** - 清晰的代码结构，易于维护

现在maxKB API调用使用正确的认证信息，确保与maxKB服务的正常通信！🎉
