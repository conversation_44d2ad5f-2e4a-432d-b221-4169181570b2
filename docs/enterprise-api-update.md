# 企业列表API接口更新文档

## 概述

本文档详细说明了企业列表页面API接口的更新，将原有的 `/enterprise/list` 接口改为 `/system/sysEnterprise/list`，并适配新的请求参数和响应格式。

## API接口变更

### 🔧 **接口地址更新**

#### **修改前** ❌
```javascript
// src/api/enterprise.js
export function getEnterpriseList(params) {
  return request({
    url: '/enterprise/list',
    method: 'get',
    params
  })
}
```

#### **修改后** ✅
```javascript
// src/api/enterprise.js
export function getEnterpriseList(params) {
  return request({
    url: '/system/sysEnterprise/list',
    method: 'get',
    params
  })
}
```

## 请求参数格式变更

### 🔧 **参数映射关系**

| 原参数名 | 新参数名 | 类型 | 说明 |
|----------|----------|------|------|
| `categoryType` | `relatedItem` | string | 分类类型映射到关联项 |
| `keyword` | `searchWord` / `name` / `creditCode` / `corporationName` | string | 根据搜索类型映射 |
| `searchType` | - | - | 用于判断keyword映射到哪个字段 |
| `pageNum` | `pageNum` | string | 页码（转为字符串） |
| `pageSize` | `pageSize` | string | 每页数量（转为字符串） |

### 🔧 **新增参数**

| 参数名 | 类型 | 必需 | 说明 |
|--------|------|------|------|
| `areaCode` | string | 可选 | 地区代码 |
| `corporationName` | string | 可选 | 法人姓名 |
| `creditCode` | string | 可选 | 统一社会信用代码 |
| `flowType` | string | 可选 | 注册流程类型 |
| `name` | string | 可选 | 单位名称 |
| `property` | string | 可选 | 单位性质 |
| `relatedItem` | string | 可选 | 关联项 |
| `searchWord` | string | 可选 | 模糊搜索词 |

### 🔧 **关联项映射**

```javascript
// 分类类型到关联项的映射
const categoryToRelatedItem = {
  'screen': 'screen',        // 电子屏
  'security': 'levelproject', // 等保备案
  'website': 'website',      // 网站备案
  'operator': 'operator',    // 运营商
  'netbar': 'netbar',       // 网吧
  'nonbusiness': 'wifi',    // 非经营
  'other': 'other'          // 其他
}
```

### 🔧 **参数构建逻辑**

```javascript
// 构建新API格式的参数
const params = {
  pageNum: this.pagination.currentPage.toString(),
  pageSize: this.pagination.pageSize.toString()
};

// 添加搜索参数
if (this.searchKeyword) {
  switch (this.searchType) {
    case 'name':
      params.name = this.searchKeyword;
      break;
    case 'creditCode':
      params.creditCode = this.searchKeyword;
      break;
    case 'legalPerson':
      params.corporationName = this.searchKeyword;
      break;
    case 'all':
    default:
      params.searchWord = this.searchKeyword;
      break;
  }
}

// 添加关联项参数
if (categoryType !== 'all') {
  switch(categoryType) {
    case 'screen':
      params.relatedItem = 'screen';
      break;
    case 'security':
      params.relatedItem = 'levelproject';
      break;
    // ... 其他映射
  }
}

// 添加其他筛选条件
if (this.filters.industry) {
  params.property = this.filters.industry;
}
```

## 响应数据格式变更

### 🔧 **响应格式对比**

#### **原响应格式** ❌
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "total": 1564,
    "list": [...]
  }
}
```

#### **新响应格式** ✅
```json
{
  "code": 200,
  "msg": "消息内容",
  "total": 1564,
  "rows": [...]
}
```

### 🔧 **企业对象字段映射**

| 前端字段 | 新API字段 | 说明 |
|----------|-----------|------|
| `legalPerson` | `corporationName` | 法人姓名 |
| `phone` | `corporationPhoneNumber` | 法人电话 |
| `handlerName` | `handlerName` | 负责人姓名 |
| `handlerPhone` | `handlerPhoneNumber` | 负责人电话 |
| `address` | `regLocation` | 注册地址 |

### 🔧 **关联数量统计**

```javascript
// 关联数量统计映射
relatedCounts: {
  screen: item.screenNum || 0,        // 关联电子屏数
  security: item.levelProjectNum || 0, // 关联等保备案数
  website: item.websiteNum || 0,      // 关联网站备案数
  operator: item.operatorNum || 0,    // 关联运营商备案数
  netbar: item.netBarNum || 0,       // 关联网吧数
  wifi: item.wifiNum || 0            // 关联非经营备案数
}
```

## 响应处理逻辑更新

### 🔧 **数据处理代码**

```javascript
getEnterpriseList(params).then(response => {
  console.log('API响应数据:', response);
  
  if (response.code === 200 && response.rows) {
    // 新API响应格式：{ code, msg, total, rows }
    const total = response.total || 0;
    const list = response.rows || [];

    // 更新分页总数
    this.pagination.total = total;

    // 处理企业列表数据，映射新API字段到前端使用的字段
    this.enterpriseList = list.map(item => {
      return {
        ...item,
        // 映射字段名
        legalPerson: item.corporationName, // 法人姓名
        phone: item.corporationPhoneNumber, // 法人电话
        handlerName: item.handlerName, // 负责人姓名
        handlerPhone: item.handlerPhoneNumber, // 负责人电话
        address: item.regLocation, // 注册地址
        // 关联数量统计
        relatedCounts: {
          screen: item.screenNum || 0,
          security: item.levelProjectNum || 0,
          website: item.websiteNum || 0,
          operator: item.operatorNum || 0,
          netbar: item.netBarNum || 0,
          wifi: item.wifiNum || 0
        },
        // 添加 selected 属性，用于复选框
        selected: this.selectedItems.includes(item.id),
        // 生成随机风险等级标签（实际项目中应该从后端获取）
        riskLevels: this.generateRandomRiskLevels()
      };
    });

    // 重置全选状态
    this.selectAll = this.enterpriseList.length > 0 && this.selectedItems.length === this.enterpriseList.length;
  } else {
    console.warn('获取到的数据为空或格式不正确:', response);
    this.enterpriseList = [];
    this.pagination.total = 0;
  }
}).catch(error => {
  console.error('获取企业列表失败:', error);
  this.$message.error('获取企业列表失败，请稍后重试');
  this.enterpriseList = [];
  this.pagination.total = 0;
});
```

## 新API字段详细说明

### 📋 **企业基本信息**

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `id` | string | 企业ID |
| `name` | string | 单位名称 |
| `creditCode` | string | 统一社会信用代码 |
| `property` | string | 单位性质 |
| `status` | string | 状态 |
| `statusLabel` | string | 状态标签 |
| `regLocation` | string | 单位注册地址 |
| `realLocation` | string | 实际办公地址 |

### 📋 **法人信息**

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `corporationName` | string | 法人姓名 |
| `corporationPhoneNumber` | string | 法人联系电话 |
| `corporationCertificateCode` | string | 法人证件号码 |
| `corporationCertificateType` | string | 法人证件类型 |
| `corporationCertificateValidityPeriod` | string | 法人证件有效期 |

### 📋 **负责人信息**

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `handlerName` | string | 负责人姓名 |
| `handlerPhoneNumber` | string | 负责人联系电话 |
| `handlerOfficePhoneNumber` | string | 负责人办公室电话 |
| `handlerEmail` | string | 负责人电子邮箱 |
| `handlerPermanentAddress` | string | 负责人常住地址 |

### 📋 **关联数量统计**

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `screenNum` | integer | 关联电子屏数 |
| `levelProjectNum` | integer | 关联等保备案数 |
| `websiteNum` | integer | 关联网站备案数 |
| `operatorNum` | integer | 关联运营商备案数 |
| `netBarNum` | integer | 关联网吧数 |
| `wifiNum` | integer | 关联非经营备案数 |

## 兼容性处理

### 🔧 **向后兼容**

为了确保前端代码的稳定性，在数据映射时保持了原有的字段名：

```javascript
// 保持原有字段名的兼容性
{
  ...item,                              // 保留所有原始字段
  legalPerson: item.corporationName,    // 映射法人姓名
  phone: item.corporationPhoneNumber,   // 映射法人电话
  // ... 其他映射
}
```

### 🔧 **错误处理**

```javascript
// 增强的错误处理
if (response.code === 200 && response.rows) {
  // 处理成功响应
} else {
  console.warn('获取到的数据为空或格式不正确:', response);
  this.enterpriseList = [];
  this.pagination.total = 0;
}
```

## 测试验证

### 🧪 **验证步骤**

1. **接口调用验证**
   - 检查Network面板中的请求URL是否为 `/system/sysEnterprise/list`
   - 验证请求参数格式是否正确

2. **数据显示验证**
   - 确认企业列表正常显示
   - 验证分页功能正常
   - 检查搜索和筛选功能

3. **字段映射验证**
   - 确认法人姓名、电话等字段正确显示
   - 验证关联数量统计正确

### 🧪 **调试信息**

添加了详细的调试日志：

```javascript
console.log('API请求参数:', params);
console.log('API响应数据:', response);
```

## 注意事项

### ⚠️ **重要提醒**

1. **参数类型**: 新API要求 `pageNum` 和 `pageSize` 为字符串类型
2. **响应格式**: 新API响应中数据在 `rows` 字段而不是 `data.list`
3. **字段映射**: 需要正确映射新API字段到前端使用的字段名
4. **关联项**: 分类筛选需要映射到正确的 `relatedItem` 值

### ⚠️ **后续优化**

1. **风险等级**: 当前使用随机生成，后续应从后端获取真实数据
2. **状态处理**: 需要根据实际业务逻辑处理企业状态显示
3. **权限控制**: 根据用户权限控制字段显示和操作权限

## 更新日志

### v1.0.0 (2024-12-19)
- ✅ 更新API接口地址为 `/system/sysEnterprise/list`
- ✅ 适配新的请求参数格式
- ✅ 更新响应数据处理逻辑
- ✅ 添加字段映射和兼容性处理
- ✅ 增强错误处理和调试日志

## 总结

通过本次更新：

1. **接口统一** - 使用新的系统级企业管理接口
2. **数据完整** - 支持更丰富的企业信息字段
3. **功能增强** - 支持更精确的搜索和筛选
4. **兼容性好** - 保持前端代码的稳定性
5. **可维护性** - 清晰的字段映射和错误处理

现在企业列表页面已经完全适配新的API接口，可以正常获取和显示企业数据！🎉
