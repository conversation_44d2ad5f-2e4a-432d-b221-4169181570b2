# 禁用maxKB Mock拦截器文档

## 概述

本文档说明了如何禁用maxKB的mock拦截器，以便AI演示页面能够调用真实的maxKB API服务。

## 问题分析

### 🔍 **问题现象**

用户反馈AI演示页面无法正常调用maxKB API，控制台显示：
- 没有真正调用 `/api/application/profile` 接口
- API请求被mock拦截器拦截
- 返回的是模拟数据而不是真实的maxKB服务响应

### 🔍 **根本原因**

#### **Mock拦截器影响** ❌
```javascript
// mock/maxkb.js 文件中的拦截器
mockApi.forEach(api => {
  Mock.mock(api.url, api.type, api.response)  // 拦截了所有maxKB API
})
```

#### **拦截的API列表** ❌
- `GET /api/application/profile` - 获取应用信息
- `GET /api/application/{id}/chat/open` - 打开会话
- `POST /api/application/chat_message/{id}` - 发送消息

## 解决方案

### 🔧 **禁用maxKB Mock拦截器**

#### **修改前** ❌
```javascript
// mock/maxkb.js
// 注册mock接口
mockApi.forEach(api => {
  Mock.mock(api.url, api.type, api.response)
})
```

#### **修改后** ✅
```javascript
// mock/maxkb.js
// 注册mock接口 - 已禁用，允许真实的maxKB API调用
// mockApi.forEach(api => {
//   Mock.mock(api.url, api.type, api.response)
// })

console.log('%c[maxKB Mock] maxKB Mock拦截器已禁用，将使用真实的maxKB API', 'color: #FF6B6B; font-weight: bold;')
```

### 🔧 **Mock系统架构**

#### **Mock控制机制** ✅
```javascript
// src/main.js
// 引入mock拦截器 - 根据环境变量控制是否启用
if (process.env.VUE_APP_ENABLE_MOCK === 'true') {
  import('@/utils/mockInterceptor')
  console.log('%c[Mock] Mock拦截器已启用')
} else {
  console.log('%c[Mock] Mock拦截器已禁用')
}
```

#### **Mock拦截器范围** ✅
```javascript
// src/utils/mockInterceptor.js
// 只拦截企业管理和用户管理相关的API
// 不包含maxKB相关的API拦截
```

#### **maxKB Mock独立控制** ✅
```javascript
// mock/maxkb.js
// 独立的maxKB mock控制
// 现已禁用，允许真实API调用
```

## 验证方法

### 🧪 **浏览器开发者工具检查**

#### **Network面板验证** ✅
1. **打开AI演示页面** - 访问 `/ai/demo`
2. **查看Network面板** - 应该看到真实的API请求
3. **验证请求地址** - 确认请求发送到 `http://***********:8080`

#### **预期的API调用序列** ✅
```
1. 页面加载时:
   GET http://***********:8080/api/application/profile

2. 首次发送消息时:
   GET http://***********:8080/api/application/{id}/chat/open

3. 发送消息时:
   POST http://***********:8080/api/application/chat_message/{chat_id}
```

#### **Console面板验证** ✅
1. **查看Mock状态** - 应该看到 `[maxKB Mock] maxKB Mock拦截器已禁用`
2. **查看API日志** - 应该看到 `[maxKB API] 请求:` 日志
3. **查看响应数据** - 应该看到真实的maxKB服务响应

### 🧪 **功能测试**

#### **连接测试** ✅
- ✅ 页面加载时调用真实的 `/api/application/profile` 接口
- ✅ 获取真实的应用信息（应用ID、名称等）
- ✅ 显示正确的连接状态

#### **对话测试** ✅
- ✅ 首次发送消息时调用 `/api/application/{id}/chat/open`
- ✅ 获取真实的会话ID
- ✅ 发送消息时调用 `/api/application/chat_message/{chat_id}`
- ✅ 接收真实的AI回复

## Mock系统说明

### 📋 **Mock拦截器分类**

| 拦截器 | 文件位置 | 控制方式 | 状态 |
|--------|----------|----------|------|
| **企业管理Mock** | `src/utils/mockInterceptor.js` | 环境变量控制 | ✅ 保持启用 |
| **用户管理Mock** | `src/utils/mockInterceptor.js` | 环境变量控制 | ✅ 保持启用 |
| **maxKB Mock** | `mock/maxkb.js` | 手动禁用 | ❌ 已禁用 |

### 📋 **环境变量控制**

#### **开发环境** ✅
```bash
# .env.development
VUE_APP_ENABLE_MOCK = 'true'  # 启用企业和用户管理的mock
```

#### **生产环境** ✅
```bash
# .env.production
VUE_APP_ENABLE_MOCK = 'false'  # 禁用所有mock
```

### 📋 **API分类处理**

#### **继续使用Mock的API** ✅
- 企业管理相关API（列表、详情、增删改查）
- 用户管理相关API（登录、用户信息、路由）
- 系统配置相关API

#### **使用真实API的服务** ✅
- maxKB AI助手相关API
- 其他第三方服务API

## 技术细节

### 🔧 **Mock.js vs Axios Mock Adapter**

#### **Mock.js方式** ❌
```javascript
// mock/maxkb.js (已禁用)
Mock.mock('/api/application/profile', 'get', mockResponse)
```
- **特点**: 全局拦截，难以精确控制
- **问题**: 会拦截所有匹配的请求，包括真实API调用

#### **Axios Mock Adapter方式** ✅
```javascript
// src/utils/mockInterceptor.js
mockAdapter.onGet('/enterprise/list').reply(mockResponse)
```
- **特点**: 精确控制，只拦截特定的axios实例
- **优势**: 可以选择性拦截，不影响其他API调用

### 🔧 **maxKB专用axios实例**

#### **独立实例设计** ✅
```javascript
// src/api/maxkb.js
const maxkbRequest = axios.create({
  baseURL: MAXKB_CONFIG.baseURL,  // http://***********:8080
  timeout: 30000
})
```
- **优势**: 完全独立于系统的axios实例
- **效果**: 不受系统mock拦截器影响

## 注意事项

### ⚠️ **开发环境配置**

#### **确保maxKB服务可访问** ✅
- 检查maxKB服务是否运行在 `http://***********:8080`
- 确认网络连接正常
- 验证API Key有效性

#### **CORS配置** ✅
- 确保maxKB服务允许跨域请求
- 检查前端域名是否在允许列表中

### ⚠️ **生产环境部署**

#### **环境变量设置** ✅
```bash
# 生产环境
VUE_APP_ENABLE_MOCK = 'false'
VUE_APP_MAXKB_BASE_URL = 'http://***********:8080'
VUE_APP_MAXKB_API_KEY = 'application-ce580c6f7987cb4b4abfcc35ccff0dad'
```

#### **API服务检查** ✅
- 确认maxKB服务在生产环境中正常运行
- 验证API Key在生产环境中有效
- 测试网络连接和防火墙配置

## 更新日志

### v1.0.0 (2024-12-19)
- ✅ 禁用maxKB Mock拦截器
- ✅ 允许真实的maxKB API调用
- ✅ 保持其他Mock拦截器正常工作
- ✅ 添加禁用状态的控制台日志

## 总结

通过禁用maxKB的mock拦截器：

1. **API调用正常** - AI演示页面现在可以调用真实的maxKB API
2. **功能完整** - 支持完整的三步骤API调用流程
3. **Mock分离** - maxKB API使用真实服务，其他API继续使用mock
4. **开发友好** - 保持了开发环境的便利性
5. **生产就绪** - 为生产环境部署做好准备

现在AI演示页面应该能够正常连接到maxKB服务并进行真实的AI对话了！🎉
