# 企业详情页面API更新文档

## 概述

本文档详细说明了企业详情页面（detailReport.vue）的API接口更新，将原有的 `/enterprise/detail/{id}` 接口改为 `/sysEnterprise/detail`，并根据新的API响应数据结构重新渲染页面数据。

## API接口更新

### 🔧 **接口地址变更** ✅

#### **修改前** ❌
```javascript
// src/api/enterprise.js
export function getEnterpriseDetail(id) {
  return request({
    url: `/enterprise/detail/${id}`,
    method: 'get'
  })
}
```

#### **修改后** ✅
```javascript
// src/api/enterprise.js
export function getEnterpriseDetail(id) {
  return request({
    url: `/sysEnterprise/detail`,
    method: 'get',
    params: { id }
  })
}
```

### 🔧 **请求参数变更** ✅
- **修改前**: 路径参数 `/{id}`
- **修改后**: 查询参数 `?id={id}`

## 数据结构映射

### 🔧 **API响应格式** ✅

新API响应格式：
```json
{
  "code": 200,
  "msg": "成功",
  "data": {
    // 企业详细信息对象
  }
}
```

### 🔧 **字段映射关系** ✅

| 前端字段 | API字段 | 说明 |
|----------|---------|------|
| **基本信息** |
| `id` | `id` | 企业ID |
| `name` | `name` | 单位名称 |
| `creditCode` | `creditCode` | 统一社会信用代码 |
| `property` | `property` | 单位性质 |
| `status` | `status` | 状态 |
| `statusLabel` | `statusLabel` | 状态标签 |
| `createTime` | `createTime` | 创建时间 |
| **地址信息** |
| `regLocation` | `regLocation` | 单位注册地址 |
| `realLocation` | `realLocation` | 实际办公地址 |
| `address` | `regLocation` | 注册地址（组件兼容） |
| **法人信息** |
| `legalPerson` | `corporationName` | 法人姓名 |
| `contactPhone` | `corporationPhoneNumber` | 法人电话 |
| `corporationCertificateCode` | `corporationCertificateCode` | 法人证件号 |
| `corporationCertificateType` | `corporationCertificateType` | 法人证件类型 |
| `corporationCertificateValidityPeriod` | `corporationCertificateValidityPeriod` | 法人证件有效期 |
| `corporationCertificateFront` | `corporationCertificateFront` | 法人证件正面 |
| `corporationCertificateBack` | `corporationCertificateBack` | 法人证件反面 |
| `corporationCertificateHand` | `corporationCertificateHand` | 法人证件手持 |
| **负责人信息** |
| `handlerName` | `handlerName` | 负责人姓名 |
| `handlerPhoneNumber` | `handlerPhoneNumber` | 负责人手机 |
| `handlerOfficePhoneNumber` | `handlerOfficePhoneNumber` | 负责人办公电话 |
| `handlerEmail` | `handlerEmail` | 负责人邮箱 |
| `handlerPermanentAddress` | `handlerPermanentAddress` | 负责人常住地址 |
| `handlerCertificateCode` | `handlerCertificateCode` | 负责人证件号 |
| `handlerCertificateType` | `handlerCertificateType` | 负责人证件类型 |
| `handlerCertificateValidityPeriod` | `handlerCertificateValidityPeriod` | 负责人证件有效期 |
| `handlerCertificateFront` | `handlerCertificateFront` | 负责人证件正面 |
| `handlerCertificateBack` | `handlerCertificateBack` | 负责人证件反面 |
| `handlerCertificateHand` | `handlerCertificateHand` | 负责人证件手持 |
| **其他信息** |
| `areaCode` | `areaCode` | 所属辖区 |
| `areaCodeLabel` | `areaCodeLabel` | 所属辖区标签 |
| `buzType` | `buzType` | 单位业务类型 |
| `publicCode` | `publicCode` | 公安备案号 |
| `staffNumRange` | `staffNumRange` | 人员规模 |
| `licensePic` | `licensePic` | 营业执照 |
| `officialLetterPic` | `officialLetterPic` | 单位公函 |
| **关联数量统计** |
| `screenNum` | `screenNum` | 关联电子屏数 |
| `levelProjectNum` | `levelProjectNum` | 关联等保备案数 |
| `websiteNum` | `websiteNum` | 关联网站备案数 |
| `operatorNum` | `operatorNum` | 关联运营商备案数 |
| `netBarNum` | `netBarNum` | 关联网吧数 |
| `wifiNum` | `wifiNum` | 关联非经营备案数 |

### 🔧 **组件兼容性字段** ✅

为了保持组件的兼容性，添加了以下映射字段：

```javascript
// 为组件兼容性添加的字段映射
companyType: apiData.property, // 企业类型
policeRecordNumber: apiData.publicCode, // 公安备案号
scale: apiData.staffNumRange, // 企业规模
state: apiData.status === '正常' ? 'valid' : 'invalid', // 状态类型
address: apiData.regLocation, // 注册地址
licensePhoto: apiData.licensePic, // 营业执照照片
photo: null, // 企业照片（API中暂无此字段）
```

## 数据处理更新

### 🔧 **fetchEnterpriseDetail方法更新** ✅

```javascript
// 获取企业详情
fetchEnterpriseDetail() {
  const id = this.id || this.$route.params.id || 1

  getEnterpriseDetail(id).then(response => {
    console.log('企业详情API响应:', response);
    
    if (response.code === 200 && response.data) {
      // 映射新API数据结构到前端使用的格式
      const apiData = response.data;
      
      this.enterpriseData = {
        // 基本信息映射
        id: apiData.id,
        name: apiData.name,
        creditCode: apiData.creditCode,
        // ... 其他字段映射
      };

      // 生成其他数据
      this.generateRiskFactors()
      this.generateSupervisionRecords()
      this.generateRelationData()
    } else {
      this.$message.error('获取企业详情失败')
    }
  }).catch(error => {
    console.error('获取企业详情失败', error)
    this.$message.error('获取企业详情失败')
  })
}
```

### 🔧 **generatePersonsData方法更新** ✅

```javascript
// 生成法人信息和责任人信息数据
generatePersonsData() {
  // 生成法人信息（使用API数据）
  this.legalPerson = {
    name: this.enterpriseData.legalPerson || '未填写',
    phone: this.enterpriseData.contactPhone || '未填写',
    idType: this.enterpriseData.corporationCertificateType || '身份证',
    idNumber: this.enterpriseData.corporationCertificateCode || '未填写',
    expiryDate: this.enterpriseData.corporationCertificateValidityPeriod || '未填写',
    portraitPhoto: this.enterpriseData.corporationCertificateFront, // 法人证件正面
    nationalEmblemPhoto: this.enterpriseData.corporationCertificateBack, // 法人证件反面
    holdingPhoto: this.enterpriseData.corporationCertificateHand, // 法人证件手持
    isValid: this.enterpriseData.corporationCertificateCodeValid // 证件有效性
  };

  // 生成责任人信息（使用API数据）
  this.responsiblePerson = {
    name: this.enterpriseData.handlerName || '未填写',
    phone: this.enterpriseData.handlerPhoneNumber || '未填写',
    idType: this.enterpriseData.handlerCertificateType || '身份证',
    idNumber: this.enterpriseData.handlerCertificateCode || '未填写',
    expiryDate: this.enterpriseData.handlerCertificateValidityPeriod || '未填写',
    portraitPhoto: this.enterpriseData.handlerCertificateFront, // 负责人证件正面
    nationalEmblemPhoto: this.enterpriseData.handlerCertificateBack, // 负责人证件反面
    holdingPhoto: this.enterpriseData.handlerCertificateHand, // 负责人证件手持
    officePhone: this.enterpriseData.handlerOfficePhoneNumber || '未填写',
    email: this.enterpriseData.handlerEmail || '未填写',
    address: this.enterpriseData.handlerPermanentAddress || '未填写',
    isValid: this.enterpriseData.handlerCertificateCodeValid // 证件有效性
  };
}
```

### 🔧 **新增方法：generateRandomRiskLevels** ✅

```javascript
// 生成随机风险等级标签
generateRandomRiskLevels() {
  const availableOptions = this.riskLevelOptions;
  if (!availableOptions || availableOptions.length === 0) {
    return [1, 2, 3]; // 默认风险等级
  }
  
  const numTags = Math.floor(Math.random() * 4) + 1; // 1-4个标签
  const selectedOptions = [];
  
  for (let i = 0; i < numTags; i++) {
    const randomIndex = Math.floor(Math.random() * availableOptions.length);
    const option = availableOptions[randomIndex];
    if (!selectedOptions.includes(option.value)) {
      selectedOptions.push(option.value);
    }
  }
  
  return selectedOptions;
}
```

## 证件信息处理

### 🔧 **证件照片字段映射** ✅

| 证件类型 | 正面照片 | 反面照片 | 手持照片 |
|----------|----------|----------|----------|
| **法人证件** | `corporationCertificateFront` | `corporationCertificateBack` | `corporationCertificateHand` |
| **负责人证件** | `handlerCertificateFront` | `handlerCertificateBack` | `handlerCertificateHand` |

### 🔧 **证件有效性字段** ✅

| 证件类型 | 有效性字段 |
|----------|------------|
| **法人证件** | `corporationCertificateCodeValid` |
| **负责人证件** | `handlerCertificateCodeValid` |

## 调试和验证

### 🧪 **调试日志** ✅

```javascript
console.log('企业详情API响应:', response);
```

### 🧪 **验证步骤** ✅

1. **接口调用验证**
   - 检查Network面板中的请求URL是否为 `/sysEnterprise/detail`
   - 验证请求参数格式是否正确（查询参数而非路径参数）
   - 确认响应数据结构

2. **数据显示验证**
   - 确认企业基本信息正常显示
   - 验证法人和负责人信息正确映射
   - 检查证件照片是否正确显示
   - 验证关联数量统计

3. **组件兼容性验证**
   - 确认EnterpriseOverview组件正常显示
   - 验证EnterpriseBasicInfo组件数据正确
   - 检查其他子组件功能正常

## 注意事项

### ⚠️ **重要提醒** ✅

1. **参数格式**: 新API使用查询参数而不是路径参数
2. **响应格式**: 新API响应中数据在 `response.data` 中
3. **字段映射**: 需要正确映射新API字段到前端使用的字段名
4. **证件信息**: 新增了证件照片和有效性字段
5. **默认值处理**: 对于可能为空的字段使用 `|| '未填写'` 处理

### ⚠️ **后续优化** ✅

1. **风险等级**: 当前使用随机生成，后续应从后端获取真实数据
2. **证件照片**: 需要处理图片显示和预览功能
3. **数据验证**: 增加数据有效性验证
4. **错误处理**: 增强API调用的错误处理机制

## 更新日志

### v1.0.0 (2024-12-19)
- ✅ 更新企业详情API接口地址为 `/sysEnterprise/detail`
- ✅ 修改请求参数格式为查询参数
- ✅ 完整映射新API响应数据结构
- ✅ 更新法人和负责人信息处理逻辑
- ✅ 添加证件照片和有效性字段支持
- ✅ 增强组件兼容性字段映射
- ✅ 添加调试日志和错误处理

## 总结

通过本次更新：

1. **接口统一** - 使用新的系统级企业详情接口
2. **数据完整** - 支持更丰富的企业详细信息字段
3. **证件管理** - 支持法人和负责人证件照片显示
4. **组件兼容** - 保持现有组件的正常工作
5. **可维护性** - 清晰的字段映射和错误处理
6. **调试友好** - 完整的请求/响应日志

现在企业详情页面已经完全适配新的API接口，能够正确显示企业的详细信息！🎉
