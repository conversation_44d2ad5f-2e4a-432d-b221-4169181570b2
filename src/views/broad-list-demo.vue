<template>
  <div class="app-container">
    <h2 class="page-title">宽列表示例</h2>

    <div class="demo-section">
      <h3 class="section-title">基础列表</h3>
      <broad-list :items="basicItems" @item-click="handleItemClick"></broad-list>
    </div>

    <div class="demo-section">
      <h3 class="section-title">带图标的列表</h3>
      <broad-list :items="iconItems" @item-click="handleItemClick"></broad-list>
    </div>

    <div class="demo-section">
      <h3 class="section-title">带描述和元数据的列表</h3>
      <broad-list :items="detailItems" @item-click="handleItemClick"></broad-list>
    </div>

    <div class="demo-section">
      <h3 class="section-title">带右侧内容的列表</h3>
      <broad-list :items="rightContentItems" @item-click="handleItemClick"></broad-list>
    </div>

    <div class="demo-section">
      <h3 class="section-title">自定义样式列表</h3>
      <div class="custom-list-container">
        <broad-list :items="customItems" @item-click="handleItemClick"></broad-list>
      </div>
    </div>

    <div class="demo-section">
      <h3 class="section-title">横向布局列表</h3>
      <div class="horizontal-list-container">
        <broad-list
          :items="horizontalItems"
          :horizontal="true"
          @item-click="handleItemClick"
          class="horizontal"
        ></broad-list>
      </div>
    </div>
  </div>
</template>

<script>
import BroadList from '@/components/broad-list.vue'

export default {
  name: 'BroadListDemo',
  components: {
    BroadList
  },
  data() {
    return {
      basicItems: [
        { title: '企业基本信息' },
        { title: '企业财务状况' },
        { title: '企业人员情况' },
        { title: '企业资质证书' },
        { title: '企业项目案例' }
      ],
      iconItems: [
        { title: '企业基本信息', icon: 'el-icon-office-building' },
        { title: '企业财务状况', icon: 'el-icon-money' },
        { title: '企业人员情况', icon: 'el-icon-user' },
        { title: '企业资质证书', icon: 'el-icon-document' },
        { title: '企业项目案例', icon: 'el-icon-collection' }
      ],
      detailItems: [
        {
          title: '企业基本信息',
          description: '包含企业名称、统一社会信用代码、注册地址等基础信息',
          meta: ['更新时间: 2023-05-19', '数据来源: 工商局']
        },
        {
          title: '企业财务状况',
          description: '包含企业资产负债、营业收入、利润等财务数据',
          meta: ['更新时间: 2023-04-30', '数据来源: 财政局']
        },
        {
          title: '企业人员情况',
          description: '包含企业员工数量、结构、管理人员等信息',
          meta: ['更新时间: 2023-05-10', '数据来源: 人社局']
        },
        {
          title: '企业资质证书',
          description: '包含企业各类资质证书、许可证等信息',
          meta: ['更新时间: 2023-05-15', '数据来源: 行政审批局']
        },
        {
          title: '企业项目案例',
          description: '包含企业近年来完成的重点项目案例',
          meta: ['更新时间: 2023-05-18', '数据来源: 发改委']
        }
      ],
      rightContentItems: [
        {
          title: '企业基本信息',
          rightText: '已完成',
          rightIcon: 'el-icon-check'
        },
        {
          title: '企业财务状况',
          rightText: '处理中',
          rightIcon: 'el-icon-loading'
        },
        {
          title: '企业人员情况',
          rightText: '待处理',
          rightIcon: 'el-icon-time'
        },
        {
          title: '企业资质证书',
          rightText: '已拒绝',
          rightIcon: 'el-icon-close'
        },
        {
          title: '企业项目案例',
          rightText: '已过期',
          rightIcon: 'el-icon-warning'
        }
      ],
      customItems: [
        {
          title: '企业基本信息',
          icon: 'el-icon-office-building',
          description: '包含企业名称、统一社会信用代码、注册地址等基础信息',
          meta: ['更新时间: 2023-05-19'],
          rightText: '已完成',
          rightIcon: 'el-icon-check'
        },
        {
          title: '企业财务状况',
          icon: 'el-icon-money',
          description: '包含企业资产负债、营业收入、利润等财务数据',
          meta: ['更新时间: 2023-04-30'],
          rightText: '处理中',
          rightIcon: 'el-icon-loading'
        },
        {
          title: '企业人员情况',
          icon: 'el-icon-user',
          description: '包含企业员工数量、结构、管理人员等信息',
          meta: ['更新时间: 2023-05-10'],
          rightText: '待处理',
          rightIcon: 'el-icon-time'
        },
        {
          title: '企业资质证书',
          icon: 'el-icon-document',
          description: '包含企业各类资质证书、许可证等信息',
          meta: ['更新时间: 2023-05-15'],
          rightText: '已拒绝',
          rightIcon: 'el-icon-close'
        },
        {
          title: '企业项目案例',
          icon: 'el-icon-collection',
          description: '包含企业近年来完成的重点项目案例',
          meta: ['更新时间: 2023-05-18'],
          rightText: '已过期',
          rightIcon: 'el-icon-warning'
        }
      ],
      horizontalItems: [
        {
          title: '企业信息',
          icon: 'el-icon-office-building',
          badge: '5'
        },
        {
          title: '财务数据',
          icon: 'el-icon-money',
          badge: '3'
        },
        {
          title: '人员管理',
          icon: 'el-icon-user',
          badge: '8'
        },
        {
          title: '资质证书',
          icon: 'el-icon-document',
          badge: '2'
        },
        {
          title: '项目案例',
          icon: 'el-icon-collection',
          badge: '6'
        },
        {
          title: '风险评估',
          icon: 'el-icon-warning',
          badge: '1'
        },
        {
          title: '监管记录',
          icon: 'el-icon-notebook-2',
          badge: '4'
        }
      ]
    }
  },
  methods: {
    handleItemClick(item) {
      this.$message.success(`您点击了: ${item.title}`);
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;

  .page-title {
    margin-bottom: 20px;
    font-size: 24px;
    font-weight: 600;
    color: #303133;
  }

  .demo-section {
    margin-bottom: 30px;

    .section-title {
      margin-bottom: 15px;
      font-size: 18px;
      font-weight: 500;
      color: #606266;
    }
  }

  .custom-list-container {
    :deep(.broad-list-container) {
      background-color: #f0f9eb;

      .broad-list {
        .broad-list-item {
          &:hover {
            background-color: #e1f3d8;

            &::after {
              background-color: #67c23a;
            }

            .item-title {
              color: #67c23a;
            }
          }
        }
      }
    }
  }

  .horizontal-list-container {
    :deep(.broad-list-container) {
      background-color: #f5f7fa;
      border-radius: 8px;
      overflow: hidden;

      .broad-list {
        padding: 10px;

        .broad-list-item {
          border-radius: 6px;
          margin: 0 5px;
          padding: 20px 15px;

          &:hover, &.active {
            background-color: #ecf5ff;
            transform: translateY(-5px);

            &::after {
              background-color: #409EFF;
            }

            .item-title {
              color: #409EFF;
            }
          }

          .item-content {
            .item-icon {
              font-size: 36px;
              margin-bottom: 15px;
              color: #409EFF;
            }

            .item-title {
              font-weight: 600;
            }

            .item-right {
              .badge {
                background-color: #409EFF;
              }
            }
          }
        }
      }
    }
  }
}
</style>
