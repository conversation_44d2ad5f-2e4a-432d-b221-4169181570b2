<template>
  <div class="app-container deepseek-chat-page">
    <!-- 左侧历史记录面板 -->
    <div class="history-panel" :class="{ 'collapsed': historyCollapsed }">
      <div class="history-header">
        <h3>历史记录</h3>
        <button class="collapse-btn" @click="toggleHistoryPanel">
          <i :class="historyCollapsed ? 'el-icon-arrow-right' : 'el-icon-arrow-left'"></i>
        </button>
      </div>

      <div class="history-content" v-if="!historyCollapsed">
        <!-- 今天的对话 -->
        <div class="history-group" v-if="todayConversations.length > 0">
          <div class="history-group-title">今天</div>
          <div
            v-for="(conv, index) in todayConversations"
            :key="'today-' + index"
            class="history-item"
            :class="{ 'active': conv.id === conversationId }"
            @click="loadConversation(conv.id)"
          >
            <div class="history-item-title">{{ conv.title }}</div>
            <div class="history-item-time">{{ formatTime(conv.time) }}</div>
          </div>
        </div>

        <!-- 7天内的对话 -->
        <div class="history-group" v-if="weekConversations.length > 0">
          <div class="history-group-title">7天内</div>
          <div
            v-for="(conv, index) in weekConversations"
            :key="'week-' + index"
            class="history-item"
            :class="{ 'active': conv.id === conversationId }"
            @click="loadConversation(conv.id)"
          >
            <div class="history-item-title">{{ conv.title }}</div>
            <div class="history-item-time">{{ formatTime(conv.time) }}</div>
          </div>
        </div>

        <!-- 30天内的对话 -->
        <div class="history-group" v-if="monthConversations.length > 0">
          <div class="history-group-title">30天内</div>
          <div
            v-for="(conv, index) in monthConversations"
            :key="'month-' + index"
            class="history-item"
            :class="{ 'active': conv.id === conversationId }"
            @click="loadConversation(conv.id)"
          >
            <div class="history-item-title">{{ conv.title }}</div>
            <div class="history-item-time">{{ formatTime(conv.time) }}</div>
          </div>
        </div>

        <!-- 更早的对话 -->
        <div class="history-group" v-if="olderConversations.length > 0">
          <div class="history-group-title">更早</div>
          <div
            v-for="(conv, index) in olderConversations"
            :key="'older-' + index"
            class="history-item"
            :class="{ 'active': conv.id === conversationId }"
            @click="loadConversation(conv.id)"
          >
            <div class="history-item-title">{{ conv.title }}</div>
            <div class="history-item-time">{{ formatDate(conv.time) }}</div>
          </div>
        </div>
      </div>

      <div class="history-footer" v-if="!historyCollapsed">
        <button class="new-chat-btn" @click="startNewConversation">
          <i class="el-icon-plus"></i> 新对话
        </button>
      </div>
    </div>

    <!-- 右侧聊天面板 -->
    <section class="chat-container">
      <!-- <div class="chat-header">
        <h2>DeepSeek 问答</h2>
        <div class="chat-actions">
          <button class="action-btn" @click="startNewConversation" title="新对话">
            <i class="el-icon-plus"></i>
          </button>
        </div>
      </div> -->

      <div class="chat-content">
        <div class="chatBox">
          <div v-if="errorMessage" class="error-message">
            {{ errorMessage }}
          </div>
          <div class="chat-messages">
            <div v-for="(message, index) in currentMessages" :key="index" class="message">
              <div v-if="message.sender === 'user'" class="user-message-container">
                <article class="message-content user-message">{{ message.text }}</article>
                <img src="@/assets/images/profile.jpg" class="avatar user-avatar" alt="用户头像">
              </div>
              <div v-else class="bot-message-container">
                <img src="@/assets/logo/logo.png" class="avatar bot-avatar" alt="机器人头像">
                <article
                  class="message-content bot-message"
                  v-html="renderMarkdown(message.text)"
                  :data-completed="message.completed || !isStreaming"
                ></article>
              </div>
            </div>
          </div>
          <div class="chat-input">
            <textarea
              v-model="inputMessage"
              placeholder="请输入您的问题"
              @keyup.enter.exact="handleSend"
              rows="3"
              :disabled="isStreaming"
            />
            <div class="input-options">
              <div class="button-group">
                <button class="option-btn" :class="{ 'active': activeOption === 'ds' }" @click="toggleOption('ds')">
                  <i class="el-icon-cpu"></i> 深度思考(DeepSeek R1)
                </button>
                <button class="option-btn" :class="{ 'active': activeOption === 'rs' }" @click="toggleOption('rs')">
                  <i class="el-icon-office-building"></i> 一企一档
                </button>
                <!-- <button class="option-btn" :class="{ 'active': activeOption === 'hs' }" @click="toggleOption('hs')">
                  <i class="el-icon-time"></i> 历史对话
                </button> -->
              </div>
            </div>
            <div class="input-actions">
              <div class="action-buttons">
                <button class="upload-btn" @click="handleFileUpload" :disabled="isStreaming">
                  <i class="el-icon-upload2"></i>
                </button>
                <input
                  type="file"
                  ref="fileInput"
                  style="display: none"
                  @change="onFileSelected"
                  accept=".txt,.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.csv,.json,.md"
                />
                <button
                  @click="handleButtonClick"
                  :disabled="!inputMessage.trim() && !isStreaming"
                  class="send-btn"
                >
                  <i class="el-icon-s-promotion"></i>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import marked from 'marked';
import DOMPurify from 'dompurify';
import { postStream } from '@/api/deepSeek';

export default {
  name: "DeepSeekChat",
  data() {
    return {
      currentMessages: [],
      inputMessage: "",
      errorMessage: "",
      currentBotMessage: '',
      isStreaming: false,
      abortController: null,
      conversationId: null,
      // 头像URL可以存储在data中以便动态更改
      userAvatar: '',
      botAvatar: '',
      // 历史记录相关
      historyCollapsed: false,
      conversations: [],
      // 示例对话历史，实际应用中应从本地存储或后端获取
      todayConversations: [],
      weekConversations: [],
      monthConversations: [],
      olderConversations: [],
      // 深度思考功能
      enableDeepThinking: true,
      // 当前激活的选项
      activeOption: 'ds'
    }
  },
  created() {
    this.generateConversationId();
    this.loadConversationsHistory();

    // 加载深度思考设置
    // const savedThinking = localStorage.getItem('deepseek-enable-thinking');
    // if (savedThinking !== null) {
    //   this.enableDeepThinking = savedThinking === 'true';
    // }

    // // 加载激活选项设置，确保默认是深度思考
    // const savedOption = localStorage.getItem('deepseek-active-option');
    // if (savedOption) {
    //   this.activeOption = savedOption;
    // } else {
    //   this.activeOption = 'ds'; // 默认激活深度思考
    //   localStorage.setItem('deepseek-active-option', 'ds');
    // }

    // 处理URL参数
    this.processUrlParams();

    // 如果有传递的消息，设置到输入框中，但不自动添加到对话中
    // 这样可以避免提问出现两次的bug
    if (this.$route.query.message) {
      this.inputMessage = this.$route.query.message;
      // 使用nextTick确保DOM已更新
      this.$nextTick(() => {
        this.handleSend();
      });
    }
  },

  beforeDestroy() {
    // 组件销毁前保存当前对话
    if (this.currentMessages.length > 0) {
      this.saveCurrentConversation();
    }
  },
  methods: {
    generateConversationId() {
      this.conversationId = 'conv-' + Date.now();
    },
    renderMarkdown(content) {
      content = content || '';
      content = content.replace(/<think>/g, '<div class="thinking-container"><img src="https://cdn-icons-png.flaticon.com/512/1055/1055687.png" class="thinking-icon"><span class="deepThink">');
      if (!content.includes('</think>')) {
        content = content.concat('</span></div>');
      }
      if (content.includes('</think>')) {
        content = content.replace(/<\/span><\/div>/g, '');
        content = content.replace(/<\/think>/g, '</span></div>');
      }
      const html = marked(content);
      const sanitizedHtml = DOMPurify.sanitize(html);
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = sanitizedHtml.toString();
      const deepThinkElements = tempDiv.querySelectorAll('.deepThink');
      deepThinkElements.forEach((element) => {
        if (element.textContent.trim() === '') {
          element.textContent = '暂无推理过程';
        }
      });
      return tempDiv.innerHTML;
    },
    async handleSend() {
      if (!this.inputMessage.trim() || this.isStreaming) return;
      this.abortController = new AbortController();
      const userMessage = {
        sender: 'user',
        text: this.inputMessage,
        conversationId: this.conversationId
      };
      this.currentMessages.push(userMessage);
      const botMessageIndex = this.currentMessages.length;
      this.currentMessages.push({
        sender: 'system',
        text: '',
        completed: false,
        conversationId: this.conversationId
      });
      const conversationHistory = this.buildConversationHistory();
      this.inputMessage = '';
      this.isStreaming = true;
      this.currentBotMessage = '';
      try {
        const response = await postStream({
          messages: conversationHistory,
          model: "deepseek-chat",
          stream: true,
          conversation_id: this.conversationId,
          enable_thinking: this.enableDeepThinking // 添加深度思考参数
        }, {
          signal: this.abortController.signal
        });
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        while (this.isStreaming) {
          const { done, value } = await reader.read();
          if (done) break;
          const text = decoder.decode(value);
          const lines = text.split('\n\n').filter(line => line.trim());
          for (const line of lines) {
            if (line.trim() === 'data: [DONE]') {
              this.finishGeneration(botMessageIndex);
              return;
            }
            if (line.startsWith('data: ')) {
              try {
                const jsonStr = line.substring(6);
                const data = JSON.parse(jsonStr);
                if (data.choices?.[0]?.delta?.content) {
                  this.currentBotMessage += data.choices[0].delta.content;
                  this.$set(this.currentMessages, botMessageIndex, {
                    sender: 'system',
                    text: this.currentBotMessage,
                    completed: false,
                    conversationId: this.conversationId
                  });
                  this.$nextTick(() => {
                    const container = this.$el.querySelector('.chat-messages');
                    container.scrollTop = container.scrollHeight;
                  });
                }
              } catch (e) {
                console.error('解析JSON出错:', e);
              }
            }
          }
        }
      } catch (error) {
        if (error.name !== 'AbortError') {
          console.error('流式请求出错:', error);
          this.errorMessage = '请求出错: ' + error.message;
          this.$set(this.currentMessages, botMessageIndex, {
            sender: 'system',
            text: '请求出错: ' + error.message,
            completed: true,
            conversationId: this.conversationId
          });
        }
      } finally {
        if (this.isStreaming) {
          this.finishGeneration(botMessageIndex);
        }
      }
    },

    buildConversationHistory() {
      const currentConversationMessages = this.currentMessages.filter(
        msg => msg.conversationId === this.conversationId
      );
      const recentMessages = currentConversationMessages.slice(-10);
      return recentMessages.map(message => {
        return {
          role: message.sender === 'user' ? 'user' : 'assistant',
          content: message.text
        };
      });
    },

    finishGeneration(index) {
      this.isStreaming = false;
      if (this.abortController) {
        this.abortController.abort();
        this.abortController = null;
      }
      this.$set(this.currentMessages, index, {
        ...this.currentMessages[index],
        completed: true
      });

      // 对话完成后，保存到历史记录
      this.saveCurrentConversation();
    },

    handleButtonClick() {
      if (this.isStreaming) {
        this.finishGeneration(this.currentMessages.length - 1);
      } else {
        this.handleSend();
      }
    },

    startNewConversation() {
      // 保存当前对话到历史记录
      if (this.currentMessages.length > 0) {
        this.saveCurrentConversation();
      }

      // 生成新的对话ID并清空当前消息
      this.generateConversationId();
      this.currentMessages = [];
      this.inputMessage = '';
    },

    saveCurrentConversation() {
      // 如果没有消息，不保存
      if (this.currentMessages.length === 0) return;

      // 获取第一条用户消息作为标题
      const userMessages = this.currentMessages.filter(msg => msg.sender === 'user');
      if (userMessages.length === 0) return;

      const title = userMessages[0].text.substring(0, 30) + (userMessages[0].text.length > 30 ? '...' : '');
      const time = new Date();

      // 检查是否已存在相同ID的对话，如果存在则更新
      const existingIndex = this.conversations.findIndex(conv => conv.id === this.conversationId);

      // 创建对话记录
      const conversation = {
        id: this.conversationId,
        title: title,
        time: time,
        messages: JSON.parse(JSON.stringify(this.currentMessages)) // 深拷贝消息
      };

      if (existingIndex !== -1) {
        // 更新现有对话
        this.$set(this.conversations, existingIndex, conversation);
      } else {
        // 添加新对话
        this.conversations.push(conversation);
      }

      // 更新分组
      this.updateConversationGroups();

      // 保存到本地存储
      try {
        localStorage.setItem('deepseek-conversations', JSON.stringify(this.conversations));
        console.log('对话已保存到历史记录', this.conversations.length);
      } catch (error) {
        console.error('保存对话历史出错:', error);
      }
    },

    loadConversationsHistory() {
      // 从本地存储加载对话历史
      try {
        const savedConversations = localStorage.getItem('deepseek-conversations');
        if (savedConversations) {
          this.conversations = JSON.parse(savedConversations);

          // 将字符串日期转换为Date对象
          this.conversations.forEach(conv => {
            conv.time = new Date(conv.time);
          });

          // 更新分组
          this.updateConversationGroups();

          console.log('已加载历史对话', this.conversations.length);
          console.log('今天:', this.todayConversations.length);
          console.log('7天内:', this.weekConversations.length);
          console.log('30天内:', this.monthConversations.length);
          console.log('更早:', this.olderConversations.length);
        } else {
          console.log('没有找到历史对话记录');
          // 创建一些示例对话用于测试
          if (process.env.NODE_ENV === 'development') {
            this.createSampleConversations();
          }
        }
      } catch (error) {
        console.error('加载对话历史出错:', error);
      }
    },

    // 创建示例对话用于测试
    createSampleConversations() {
      const now = new Date();

      // 今天的对话
      const todayConv = {
        id: 'sample-today-' + Date.now(),
        title: '今天的示例对话',
        time: now,
        messages: [
          { sender: 'user', text: '今天的示例对话', conversationId: 'sample-today', completed: true },
          { sender: 'system', text: '这是一个示例回复', conversationId: 'sample-today', completed: true }
        ]
      };

      // 一周内的对话
      const weekConv = {
        id: 'sample-week-' + Date.now(),
        title: '一周内的示例对话',
        time: new Date(now.getFullYear(), now.getMonth(), now.getDate() - 3),
        messages: [
          { sender: 'user', text: '一周内的示例对话', conversationId: 'sample-week', completed: true },
          { sender: 'system', text: '这是一个示例回复', conversationId: 'sample-week', completed: true }
        ]
      };

      // 一个月内的对话
      const monthConv = {
        id: 'sample-month-' + Date.now(),
        title: '一个月内的示例对话',
        time: new Date(now.getFullYear(), now.getMonth(), now.getDate() - 15),
        messages: [
          { sender: 'user', text: '一个月内的示例对话', conversationId: 'sample-month', completed: true },
          { sender: 'system', text: '这是一个示例回复', conversationId: 'sample-month', completed: true }
        ]
      };

      // 更早的对话
      const olderConv = {
        id: 'sample-older-' + Date.now(),
        title: '更早的示例对话',
        time: new Date(now.getFullYear(), now.getMonth() - 2, now.getDate()),
        messages: [
          { sender: 'user', text: '更早的示例对话', conversationId: 'sample-older', completed: true },
          { sender: 'system', text: '这是一个示例回复', conversationId: 'sample-older', completed: true }
        ]
      };

      this.conversations = [todayConv, weekConv, monthConv, olderConv];
      this.updateConversationGroups();

      // 保存到本地存储
      localStorage.setItem('deepseek-conversations', JSON.stringify(this.conversations));
    },

    updateConversationGroups() {
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const weekAgo = new Date(today);
      weekAgo.setDate(weekAgo.getDate() - 7);
      const monthAgo = new Date(today);
      monthAgo.setDate(monthAgo.getDate() - 30);

      // 按时间分组
      this.todayConversations = this.conversations.filter(conv => conv.time >= today);
      this.weekConversations = this.conversations.filter(conv => conv.time >= weekAgo && conv.time < today);
      this.monthConversations = this.conversations.filter(conv => conv.time >= monthAgo && conv.time < weekAgo);
      this.olderConversations = this.conversations.filter(conv => conv.time < monthAgo);

      // 按时间倒序排序
      const sortByTimeDesc = (a, b) => b.time - a.time;
      this.todayConversations.sort(sortByTimeDesc);
      this.weekConversations.sort(sortByTimeDesc);
      this.monthConversations.sort(sortByTimeDesc);
      this.olderConversations.sort(sortByTimeDesc);
    },

    loadConversation(id) {
      // 保存当前对话
      if (this.currentMessages.length > 0 && this.conversationId !== id) {
        this.saveCurrentConversation();
      }

      // 查找并加载选定的对话
      const conversation = this.conversations.find(conv => conv.id === id);
      if (conversation) {
        this.conversationId = id;
        this.currentMessages = [...conversation.messages];

        // 滚动到底部
        this.$nextTick(() => {
          const container = this.$el.querySelector('.chat-messages');
          if (container) {
            container.scrollTop = container.scrollHeight;
          }
        });
      }
    },

    toggleHistoryPanel() {
      this.historyCollapsed = !this.historyCollapsed;
    },

    formatTime(date) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    },

    formatDate(date) {
      return date.toLocaleDateString([], { year: 'numeric', month: '2-digit', day: '2-digit' });
    },

    toggleDeepThinking() {
      this.enableDeepThinking = !this.enableDeepThinking;
      // 保存设置到本地存储
      localStorage.setItem('deepseek-enable-thinking', this.enableDeepThinking.toString());
    },

    processUrlParams() {
      // 处理URL参数
      const { ds, rs, hs } = this.$route.query;

      // 处理深度思考参数
      if (ds === 'true') {
        this.activeOption = 'ds';
        this.enableDeepThinking = true;
      }

      // 处理一企一档参数
      if (rs === 'true') {
        this.activeOption = 'rs';
        this.enableDeepThinking = false;
      }

      // 处理历史对话参数
      if (hs === 'true') {
        this.activeOption = 'hs';
        // 如果是历史对话，则展开历史记录面板
        this.historyCollapsed = false;
      }

      // 保存设置到本地存储
      localStorage.setItem('deepseek-enable-thinking', this.enableDeepThinking.toString());
      localStorage.setItem('deepseek-active-option', this.activeOption);
    },

    toggleOption(option) {
      // 如果点击的是深度思考或一企一档，它们是互斥的
      if (option === 'ds' || option === 'rs') {
        // 如果当前已经激活，则不做任何操作
        if (this.activeOption === option) return;

        // 如果当前激活的是深度思考或一企一档，则切换
        if (this.activeOption === 'ds' || this.activeOption === 'rs') {
          this.activeOption = option;

          // 如果切换到深度思考，则启用深度思考功能
          if (option === 'ds') {
            this.enableDeepThinking = true;
          } else {
            this.enableDeepThinking = false;
          }

          // 保存设置到本地存储
          localStorage.setItem('deepseek-enable-thinking', this.enableDeepThinking.toString());
          localStorage.setItem('deepseek-active-option', this.activeOption);
        } else {
          // 如果当前激活的是历史对话，则保持历史对话激活，同时切换深度思考状态
          if (option === 'ds') {
            this.enableDeepThinking = true;
          } else {
            this.enableDeepThinking = false;
          }

          // 保存设置到本地存储
          localStorage.setItem('deepseek-enable-thinking', this.enableDeepThinking.toString());
        }
      } else if (option === 'hs') {
        // 历史对话可以与深度思考或一企一档共存
        if (this.activeOption === 'hs') {
          // 如果当前已经激活历史对话，则切换回之前的选项
          const savedOption = localStorage.getItem('deepseek-active-option') || 'ds';
          this.activeOption = savedOption;
          // 折叠历史记录面板
          this.historyCollapsed = true;
        } else {
          // 保存当前选项，然后激活历史对话
          localStorage.setItem('deepseek-active-option', this.activeOption);
          this.activeOption = option;
          // 展开历史记录面板
          this.historyCollapsed = false;
        }

        // 保存设置到本地存储
        localStorage.setItem('deepseek-active-option', this.activeOption);
      }
    },

    // 处理文件上传按钮点击
    handleFileUpload() {
      this.$refs.fileInput.click();
    },

    // 处理文件选择
    onFileSelected(event) {
      const file = event.target.files[0];
      if (!file) return;

      // 文件大小限制（10MB）
      const maxSize = 10 * 1024 * 1024;
      if (file.size > maxSize) {
        this.$message.error('文件大小不能超过10MB');
        this.$refs.fileInput.value = '';
        return;
      }

      const reader = new FileReader();

      reader.onload = (e) => {
        try {
          let content = '';

          // 根据文件类型处理内容
          if (file.type === 'application/json') {
            // 如果是JSON文件，尝试格式化显示
            const jsonObj = JSON.parse(e.target.result);
            content = JSON.stringify(jsonObj, null, 2);
          } else if (file.type === 'text/csv') {
            // 如果是CSV文件，直接显示内容
            content = e.target.result;
          } else {
            // 其他文件类型，直接显示内容
            content = e.target.result;
          }

          // 如果内容太长，截断显示
          if (content.length > 2000) {
            content = content.substring(0, 2000) + '...(内容过长，已截断)';
          }

          // 将文件内容添加到输入框
          this.inputMessage = `我上传了一个文件：${file.name}\n\n文件内容：\n${content}`;

          // 清空文件输入，以便下次选择同一文件时也能触发change事件
          this.$refs.fileInput.value = '';

          // 提示用户
          this.$message.success('文件内容已加载到输入框');
        } catch (error) {
          console.error('读取文件出错:', error);
          this.$message.error('读取文件出错: ' + error.message);
          this.$refs.fileInput.value = '';
        }
      };

      reader.onerror = () => {
        this.$message.error('读取文件失败');
        this.$refs.fileInput.value = '';
      };

      // 根据文件类型选择读取方式
      if (file.type.includes('text') ||
          file.type.includes('json') ||
          file.type.includes('csv') ||
          file.name.endsWith('.md')) {
        reader.readAsText(file);
      } else {
        // 对于其他类型的文件，可能需要特殊处理
        this.$message.warning('此类型文件可能无法正确显示内容');
        reader.readAsText(file);
      }
    }
  }
}
</script>

<style scoped lang="scss">
.deepseek-chat-page {
  display: flex;
  width: 100%;
  height: calc(100vh - 120px); /* 减去头部导航的高度 */
  background-color: white;
  position: relative;
}

/* 左侧历史记录面板 */
.history-panel {
  width: 280px;
  height: 100%;
  border-right: 1px solid #e0e0e0;
  background-color: #f9f9f9;
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease;
  overflow: hidden;

  &.collapsed {
    width: 50px;
  }
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #e0e0e0;

  h3 {
    margin: 0;
    font-size: 16px;
    color: #333;
  }
}

.collapse-btn {
  background: none;
  border: none;
  cursor: pointer;
  color: #666;
  padding: 5px;
  border-radius: 4px;

  &:hover {
    background-color: #e0e0e0;
  }
}

.history-content {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
}

.history-group {
  margin-bottom: 20px;
}

.history-group-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
  padding-left: 5px;
}

.history-item {
  padding: 10px;
  border-radius: 6px;
  cursor: pointer;
  margin-bottom: 5px;

  &:hover {
    background-color: #e9e9e9;
  }

  &.active {
    background-color: #e3f2fd;
  }
}

.history-item-title {
  font-size: 14px;
  margin-bottom: 5px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.history-item-time {
  font-size: 12px;
  color: #888;
}

.history-footer {
  padding: 15px;
  border-top: 1px solid #e0e0e0;
}

.new-chat-btn {
  width: 100%;
  padding: 10px;
  background-color: #2196f3;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;

  &:hover {
    background-color: #1976d2;
  }
}

/* 右侧聊天面板 */
.chat-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: white;
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #e0e0e0;

  h2 {
    margin: 0;
    font-size: 18px;
    color: #333;
  }
}

.chat-actions {
  display: flex;
  gap: 10px;
}

.action-btn {
  background: none;
  border: none;
  cursor: pointer;
  color: #666;
  padding: 8px;
  border-radius: 4px;

  &:hover {
    background-color: #f0f0f0;
  }
}

.chat-content {
  flex: 1;
  padding: 0;
  display: flex;
  flex-direction: column;
  height: calc(100% - 60px); /* 减去头部的高度 */
}

.chatBox {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  overflow: hidden;
  background-color: white;
}

.error-message {
  background-color: #f8d7da;
  color: #721c24;
  padding: 12px;
  margin: 12px;
  border-radius: 8px;
  text-align: center;
}

.chat-messages {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  background-color: #f9f9f9;
}

.message {
  margin-bottom: 20px;
  display: flex;
  align-items: flex-start;
}

.user-message-container {
  display: flex;
  justify-content: flex-end;
  width: 100%;
  align-items: flex-start;
  gap: 12px;
}

.bot-message-container {
  display: flex;
  justify-content: flex-start;
  width: 100%;
  align-items: flex-start;
  gap: 12px;
}

.avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  object-fit: cover;
  flex-shrink: 0;
}

.user-avatar {
  border: 2px solid #2196f3;
}

.bot-avatar {
  border: 2px solid #4caf50;
}

.message-content {
  max-width: calc(70% - 50px);
  padding: 12px 16px;
  border-radius: 18px;
  line-height: 1.5;
  word-wrap: break-word;
  font-size: 15px;
}

.user-message {
  background-color: #e3f2fd;
  color: #0d47a1;
  border-top-right-radius: 4px;
}

.bot-message {
  background-color: white;
  color: #333;
  border-top-left-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.chat-input {
  padding: 15px 20px;
  border-top: 1px solid #e0e0e0;
  background-color: #fff;
}

.chat-input textarea {
  width: 100%;
  padding: 15px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  resize: none;
  min-height: 60px;
  max-height: 150px;
  font-family: inherit;
  font-size: 15px;
  margin-bottom: 10px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.chat-input textarea:focus {
  outline: none;
  border-color: #2196f3;
  box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.2);
}

.input-options {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 10px;
}

.button-group {
  display: flex;
  align-items: center;
  gap: 10px;
}

.option-btn {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 6px 12px;
  background-color: #f5f5f5;
  border: 1px solid #e0e0e0;
  border-radius: 16px;
  color: #666;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: #e9e9e9;
    border-color: #2196f3;
    color: #2196f3;
  }

  &.active {
    background-color: #e3f2fd;
    border-color: #2196f3;
    color: #2196f3;
  }

  i {
    font-size: 14px;
  }
}

.input-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 8px;
}

.upload-btn {
  background-color: transparent;
  color: #666;
  border: none;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: #f0f0f0;
    color: #2196f3;
  }

  &:disabled {
    color: #ccc;
    cursor: not-allowed;
  }

  i {
    font-size: 18px;
  }
}

.send-btn {
  background-color: #2196f3;
  color: white;
  border: none;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: #1976d2;
  }

  &:disabled {
    background-color: #e0e0e0;
    color: #9e9e9e;
    cursor: not-allowed;
  }

  i {
    font-size: 16px;
  }
}

.bot-message:not([data-completed="true"]):after {
  content: "|";
  animation: blink 1s infinite;
  color: #666;
}

@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0; }
}

/* 深度思考样式 */
.deepThink {
  color: #666;
  font-style: italic;
  background-color: #f5f5f5;
  padding: 10px 14px;
  border-radius: 8px;
  display: inline-block;
  margin-top: 8px;
  font-size: 14px;
}

.thinking-container {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 8px 0;
}

.thinking-icon {
  width: 16px;
  height: 16px;
  opacity: 0.6;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .history-panel {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    z-index: 10;

    &.collapsed {
      transform: translateX(-100%);
      width: 280px;
    }
  }

  .message-content {
    max-width: calc(85% - 40px);
  }
}
</style>
