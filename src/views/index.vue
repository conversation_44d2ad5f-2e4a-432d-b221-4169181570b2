<template>
  <div class="app-container home">
    <el-row type="flex" justify="center">
       <el-col :xs="24" :sm="24" :md="20" :lg="20" >
        <div class="content-title">
          <div class="title-left">
            <img src="@/assets/images/tz/u117.png" alt="标题图标">
          </div>
          <div class="title-right">
            <h1>我是瑞霖捜，你的警务AI助手！</h1>
            <p>让我们开启全新的办公模式吧~</p>
          </div>
        </div>
      </el-col>
    </el-row>
    <el-row :gutter="20">

      <section class="chat-container">
        <div class="chat-content">
          <div class="chatBox">
            <div v-if="errorMessage" class="error-message">
              {{ errorMessage }}
            </div>
            <div class="chat-messages">
              <div v-for="(message, index) in currentMessages" :key="index" class="message">
                <div v-if="message.sender === 'user'" class="user-message-container">
                  <article class="message-content user-message">{{ message.text }}</article>
                  <img src="@/assets/images/profile.jpg" class="avatar user-avatar" alt="用户头像">
                </div>
                <div v-else class="bot-message-container">
                  <img src="@/assets/logo/logo.png" class="avatar bot-avatar" alt="机器人头像">
                  <article
                    class="message-content bot-message"
                    v-html="renderMarkdown(message.text)"
                    :data-completed="message.completed || !isStreaming"
                  ></article>
                </div>
              </div>
            </div>
            <div class="chat-input">
              <textarea
                v-model="inputMessage"
                placeholder="请输入您的问题"
                @keyup.enter.exact="handleSend"
                rows="6"
                :disabled="isStreaming"
              />
              <div class="input-options">
                <div class="button-group">
                  <button class="option-btn" :class="{ 'active': activeOption === 'ds' }" @click="toggleOption('ds')">
                    <i class="el-icon-cpu"></i> 深度思考(DeepSeek R1)
                  </button>
                  <button class="option-btn" :class="{ 'active': activeOption === 'qs' }" @click="toggleOption('qs')">
                    <i class="el-icon-office-building"></i> 一企一档
                  </button>
                  <button class="option-btn" :class="{ 'active': activeOption === 'hs' }" @click="toggleOption('hs')">
                    <i class="el-icon-time"></i> 历史对话
                  </button>
                </div>
              </div>
              <!-- <div class="input-actions">
                <button @click="navigateToDeepSeekChat" class="navigate-btn" :disabled="isStreaming">
                  <i class="el-icon-right"></i> 进入问答页面
                </button>
              </div> -->
              <div class="input-actions">
                <div class="action-buttons">
                  <button class="upload-btn" @click="handleFileUpload" :disabled="isStreaming">
                    <i class="el-icon-upload2"></i>
                  </button>
                  <input
                    type="file"
                    ref="fileInput"
                    style="display: none"
                    @change="onFileSelected"
                    accept=".txt,.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.csv,.json,.md"
                  />
                  <button
                    @click="navigateToDeepSeekChat"
                    :disabled="!inputMessage.trim() && !isStreaming"
                    class="send-btn"
                  >
                    <i class="el-icon-s-promotion"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

    </el-row>
    <el-divider />
    <el-row style="margin-left:5%;margin-right:5%;">

      <el-col :xs="24" :sm="24" :md="13" :lg="13">
        <el-card class="update-log" style="margin-right: 15px;">
          <div slot="header" class="clearfix">
            <span>代办任务</span>
          </div>
          <div class="task-list">
            <el-tabs v-model="activeTaskTab">
              <el-tab-pane :label="'上级交办任务 (' + Math.min(superiorTasks.length, 5) + ')'" name="superior">
                <ul class="task-items" v-if="superiorTasks.length > 0">
                  <li
                    v-for="(task, index) in superiorTasks.slice(0, 5)"
                    :key="'superior-' + index"
                    class="task-item"
                    @click="goTarget(task.link)"
                  >
                    <div class="task-index">{{ index + 1 }}</div>
                    <div class="task-content" :title="task.content">
                      {{ task.content }}
                      <span v-if="task.isNew" class="new-badge">NEW</span>
                    </div>
                    <div class="task-source">{{ task.source }}</div>
                    <div class="task-time">{{ formatDateTime(task.time) }}</div>
                  </li>
                </ul>
                <div v-else class="empty-task">
                  <i class="el-icon-s-order"></i>
                  <span>暂无上级交办任务</span>
                </div>
              </el-tab-pane>
              <el-tab-pane :label="'下级反馈任务 (' + Math.min(subordinateTasks.length, 5) + ')'" name="subordinate">
                <ul class="task-items" v-if="subordinateTasks.length > 0">
                  <li
                    v-for="(task, index) in subordinateTasks.slice(0, 5)"
                    :key="'subordinate-' + index"
                    class="task-item"
                    @click="goTarget(task.link)"
                  >
                    <div class="task-index">{{ index + 1 }}</div>
                    <div class="task-content" :title="task.content">
                      {{ task.content }}
                      <span v-if="task.isNew" class="new-badge">NEW</span>
                    </div>
                    <div class="task-source">{{ task.source }}</div>
                    <div class="task-time">{{ formatDateTime(task.time) }}</div>
                  </li>
                </ul>
                <div v-else class="empty-task">
                  <i class="el-icon-s-order"></i>
                  <span>暂无下级反馈任务</span>
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>

        </el-card>
      </el-col>
      <el-col :xs="24" :sm="24" :md="11" :lg="11">
        <section class="business-block" style="margin-left: 15px;">
          <div class="flex-row">
            <h3>自主业务区块</h3>
            <p>数据视仓 ></p>
          </div>
          <div class="card-con">
            <!-- 卡片行 -->
            <div
              class="card-row"
              v-for="(row, rowIndex) in cardRows"
              :key="'row-' + rowIndex"
            >
              <!-- 卡片项 -->
              <div
                class="card-item"
                v-for="(card, cardIndex) in row"
                :key="'card-' + rowIndex + '-' + cardIndex"
                @click="goTarget(card.link)"
              >
                <!-- 卡片左侧图标 -->
                <div class="card-left">
                  <img
                    :src="require('@/assets/images/tz/' + card.image)"
                    :alt="card.title + ' 图标'"
                  >
                </div>

                <!-- 卡片右侧内容 -->
                <div class="card-right">
                  <!-- 卡片标题 -->
                  <div class="card-title">{{ card.title }}</div>

                  <!-- 卡片副标题和数字 -->
                  <div
                    class="card-subtitle"
                    v-if="card.subtit || card.subtitNum"
                  >
                    {{ card.subtit }}
                    <span
                      v-if="card.subtitNum"
                      class="subtitle-num"
                    >
                      {{ card.subtitNum }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import marked from 'marked';
import DOMPurify from 'dompurify';
import { postStream } from '@/api/deepSeek';


export default {
  name: "Index",
  computed: {
    cardRows() {
      // 将卡片列表分成每行两个卡片
      const rows = [];
      for (let i = 0; i < this.cardList.length; i += 2) {
        rows.push(this.cardList.slice(i, i + 2));
      }
      return rows;
    }
  },
  data() {
    return {
      // 版本号
      version: "3.6.5",
      currentMessages: [],
      inputMessage: "",
      errorMessage: "",
      currentBotMessage: '',
      isStreaming: false,
      abortController: null,
      conversationId: null,
      // 头像URL可以存储在data中以便动态更改
      userAvatar: '',
      botAvatar: '',
      // 深度思考功能
      enableDeepThinking: true,
      // 当前激活的选项
      activeOption: 'ds',
      // 当前激活的任务选项卡
      activeTaskTab: 'superior',
      // 上级交办任务数据
      superiorTasks: [
        {
          content: '完成第三季度财务报表审核',
          source: '财务部',
          time: new Date('2023-10-15 09:30:00'),
          isNew: true,
          link: '/task/detail/1001'
        },
        {
          content: '组织部门团建活动策划与实施',
          source: '人事部',
          time: new Date('2023-10-16 14:20:00'),
          isNew: false,
          link: '/task/detail/1002'
        },
        {
          content: '完成新系统上线前的最终测试',
          source: '技术部',
          time: new Date('2023-10-17 11:00:00'),
          isNew: true,
          link: '/task/detail/1003'
        },
        {
          content: '准备下周管理层会议材料',
          source: '办公室',
          time: new Date('2023-10-18 16:45:00'),
          isNew: false,
          link: '/task/detail/1004'
        },
        {
          content: '完成年度预算计划初稿',
          source: '财务部',
          time: new Date('2023-10-19 10:15:00'),
          isNew: false,
          link: '/task/detail/1005'
        }
      ],
      // 下级反馈任务数据
      subordinateTasks: [
        {
          content: '市场调研报告已完成，等待审核',
          source: '市场部',
          time: new Date('2023-10-15 15:30:00'),
          isNew: true,
          link: '/task/feedback/2001'
        },
        {
          content: '新员工培训计划已制定完毕',
          source: '培训部',
          time: new Date('2023-10-16 09:45:00'),
          isNew: false,
          link: '/task/feedback/2002'
        },
        {
          content: '客户满意度调查结果分析',
          source: '客服部',
          time: new Date('2023-10-17 13:20:00'),
          isNew: true,
          link: '/task/feedback/2003'
        },
        {
          content: '产品原型设计已完成',
          source: '设计部',
          time: new Date('2023-10-18 11:30:00'),
          isNew: true,
          link: '/task/feedback/2004'
        },
        {
          content: '销售数据周报已生成',
          source: '销售部',
          time: new Date('2023-10-19 17:00:00'),
          isNew: false,
          link: '/task/feedback/2005'
        }
      ],
      cardList: [
        {
          title: '一企一档',
          image: 'u155.png',
          time: new Date('2023-10-19 17:00:00'),
          subtit: '',
          subtitNum: '',
          link: '/enterprise/index'
        },
        {
          title: '电子屏专项',
          image: 'u155.png',
          time: new Date('2023-10-19 17:00:00'),
          subtit: '',
          subtitNum: '',
          link: '/task/feedback/2005'
        },
        {
          title: '基础数据管理',
          image: 'u155.png',
          time: new Date('2023-10-19 17:00:00'),
          subtit: '企业上报',
          subtitNum: 6,
          link: '/task/feedback/2005'
        },
        {
          title: '人员虚实档案',
          image: 'u155.png',
          time: new Date('2023-10-19 17:00:00'),
          subtit: '',
          subtitNum: '',
          link: '/task/feedback/2005'
        },
        {
          title: '非现场检查',
          image: 'u155.png',
          time: new Date('2023-10-19 17:00:00'),
          subtit: '',
          subtitNum: '',
          link: '/task/feedback/2005'
        },
        {
          title: '行政监管',
          image: 'u155.png',
          time: new Date('2023-10-19 17:00:00'),
          subtit: '未处理',
          subtitNum: 3,
          link: '/task/feedback/2005'
        },
        {
          title: '任务中心',
          image: 'u155.png',
          time: new Date('2023-10-19 17:00:00'),
          subtit: '',
          subtitNum: '',
          link: '/task/feedback/2005'
        },
        {
          title: '通知公告',
          image: 'u155.png',
          time: new Date('2023-10-19 17:00:00'),
          subtit: '',
          subtitNum: '',
          link: '/task/feedback/2005'
        },

      ]
    }
  },
  created() {
    this.generateConversationId();

    // 加载深度思考设置
    // const savedThinking = localStorage.getItem('deepseek-enable-thinking');
    // if (savedThinking !== null) {
    //   this.enableDeepThinking = savedThinking === 'true';
    // }
    // // 加载激活选项设置，确保默认是深度思考
    // const savedOption = localStorage.getItem('deepseek-active-option');
    // if (savedOption) {
    //   this.activeOption = savedOption;
    // } else {
    //   this.activeOption = 'ds'; // 默认激活深度思考
    //   localStorage.setItem('deepseek-active-option', 'ds');
    // }
  },
  methods: {
    generateConversationId() {
      this.conversationId = 'conv-' + Date.now();
    },
    renderMarkdown(content) {
      content = content || '';
      content = content.replace(/<think>/g, '<div class="thinking-container"><img src="https://cdn-icons-png.flaticon.com/512/1055/1055687.png" class="thinking-icon"><span class="deepThink">');
      if (!content.includes('</think>')) {
        content = content.concat('</span></div>');
      }
      if (content.includes('</think>')) {
        content = content.replace(/<\/span><\/div>/g, '');
        content = content.replace(/<\/think>/g, '</span></div>');
      }
      const html = marked(content);
      const sanitizedHtml = DOMPurify.sanitize(html);
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = sanitizedHtml.toString();
      const deepThinkElements = tempDiv.querySelectorAll('.deepThink');
      deepThinkElements.forEach((element) => {
        if (element.textContent.trim() === '') {
          element.textContent = '暂无推理过程';
        }
      });
      return tempDiv.innerHTML;
    },
    async handleSend() {
      if (!this.inputMessage.trim() || this.isStreaming) return;
      this.abortController = new AbortController();
      const userMessage = {
        sender: 'user',
        text: this.inputMessage,
        conversationId: this.conversationId
      };
      this.currentMessages.push(userMessage);
      const botMessageIndex = this.currentMessages.length;
      this.currentMessages.push({
        sender: 'system',
        text: '',
        completed: false,
        conversationId: this.conversationId
      });
      const conversationHistory = this.buildConversationHistory();
      this.inputMessage = '';
      this.isStreaming = true;
      this.currentBotMessage = '';
      try {
        const response = await postStream({
          messages: conversationHistory,
          model: "deepseek-chat",
          stream: true,
          conversation_id: this.conversationId,
          enable_thinking: this.enableDeepThinking // 添加深度思考参数
        }, {
          signal: this.abortController.signal
        });
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        while (this.isStreaming) {
          const { done, value } = await reader.read();
          if (done) break;
          const text = decoder.decode(value);
          const lines = text.split('\n\n').filter(line => line.trim());
          for (const line of lines) {
            if (line.trim() === 'data: [DONE]') {
              this.finishGeneration(botMessageIndex);
              return;
            }
            if (line.startsWith('data: ')) {
              try {
                const jsonStr = line.substring(6);
                const data = JSON.parse(jsonStr);
                if (data.choices?.[0]?.delta?.content) {
                  this.currentBotMessage += data.choices[0].delta.content;
                  this.$set(this.currentMessages, botMessageIndex, {
                    sender: 'system',
                    text: this.currentBotMessage,
                    completed: false,
                    conversationId: this.conversationId
                  });
                  this.$nextTick(() => {
                    const container = this.$el.querySelector('.chat-messages');
                    container.scrollTop = container.scrollHeight;
                  });
                }
              } catch (e) {
                console.error('解析JSON出错:', e);
              }
            }
          }
        }
      } catch (error) {
        if (error.name !== 'AbortError') {
          console.error('流式请求出错:', error);
          this.errorMessage = '请求出错: ' + error.message;
          this.$set(this.currentMessages, botMessageIndex, {
            sender: 'system',
            text: '请求出错: ' + error.message,
            completed: true,
            conversationId: this.conversationId
          });
        }
      } finally {
        if (this.isStreaming) {
          this.finishGeneration(botMessageIndex);
        }
      }
    },

    buildConversationHistory() {
      const currentConversationMessages = this.currentMessages.filter(
        msg => msg.conversationId === this.conversationId
      );
      const recentMessages = currentConversationMessages.slice(-10);
      return recentMessages.map(message => {
        return {
          role: message.sender === 'user' ? 'user' : 'assistant',
          content: message.text
        };
      });
    },

    finishGeneration(index) {
      this.isStreaming = false;
      if (this.abortController) {
        this.abortController.abort();
        this.abortController = null;
      }
      this.$set(this.currentMessages, index, {
        ...this.currentMessages[index],
        completed: true
      });
    },

    handleButtonClick() {
      if (this.isStreaming) {
        this.finishGeneration(this.currentMessages.length - 1);
      } else {
        this.handleSend();
      }
    },

    startNewConversation() {
      this.generateConversationId();
    },

    navigateToDeepSeekChat() {
      // 创建查询参数对象
      const query = {};

      // 如果是历史对话按钮，直接跳转，不需要输入内容
      if (this.activeOption === 'hs') {
        query.hs = 'true';
      }
      // 如果是深度思考或一企一档按钮，需要输入内容才能跳转
      else {
        // 如果没有输入内容，则不跳转
        if (!this.inputMessage.trim()) {
          // 提示用户输入内容
          this.$message({
            message: '请输入问题后再点击',
            type: 'warning'
          });
          return;
        }

        // 如果有输入内容，则将其作为参数传递到新页面
        query.message = this.inputMessage.trim();

        // 添加当前激活的选项作为参数
        query[this.activeOption] = 'true';
      }

      // 使用新窗口打开
      const url = this.$router.resolve({ path: '/deepseek/chat', query }).href;
      window.open(url, '_blank');
    },

    goTarget(href) {
      window.open(href, "_blank")
    },

    toggleDeepThinking() {
      this.enableDeepThinking = !this.enableDeepThinking;
      // 保存设置到本地存储
      localStorage.setItem('deepseek-enable-thinking', this.enableDeepThinking.toString());
    },

    toggleOption(option) {
      // 如果点击的是深度思考或一企一档，它们是互斥的
      if (option === 'ds' || option === 'rs') {
        // 如果当前已经激活，则不做任何操作
        if (this.activeOption === option) return;

        // 如果当前激活的是深度思考或一企一档，则切换
        if (this.activeOption === 'ds' || this.activeOption === 'rs') {
          this.activeOption = option;

          // 如果切换到深度思考，则启用深度思考功能
          if (option === 'ds') {
            this.enableDeepThinking = true;
          } else {
            this.enableDeepThinking = false;
          }

          // 保存设置到本地存储
          localStorage.setItem('deepseek-enable-thinking', this.enableDeepThinking.toString());
          localStorage.setItem('deepseek-active-option', this.activeOption);
        } else {
          // 如果当前激活的是历史对话，则保持历史对话激活，同时切换深度思考状态
          if (option === 'ds') {
            this.enableDeepThinking = true;
          } else {
            this.enableDeepThinking = false;
          }

          // 保存设置到本地存储
          localStorage.setItem('deepseek-enable-thinking', this.enableDeepThinking.toString());
        }
      } else if (option === 'hs') {
        // 历史对话可以与深度思考或一企一档共存
        if (this.activeOption === 'hs') {
          // 如果当前已经激活历史对话，则切换回之前的选项
          const savedOption = localStorage.getItem('deepseek-active-option') || 'ds';
          this.activeOption = savedOption;
        } else {
          // 保存当前选项，然后激活历史对话
          localStorage.setItem('deepseek-active-option', this.activeOption);
          this.activeOption = option;
        }
      }

      // 直接跳转到聊天页面
      this.navigateToDeepSeekChat();
    },

    // 处理文件上传按钮点击
    handleFileUpload() {
      this.$refs.fileInput.click();
    },

    // 处理文件选择
    onFileSelected(event) {
      const file = event.target.files[0];
      if (!file) return;

      // 文件大小限制（10MB）
      const maxSize = 10 * 1024 * 1024;
      if (file.size > maxSize) {
        this.$message.error('文件大小不能超过10MB');
        this.$refs.fileInput.value = '';
        return;
      }

      const reader = new FileReader();

      reader.onload = (e) => {
        try {
          let content = '';

          // 根据文件类型处理内容
          if (file.type === 'application/json') {
            // 如果是JSON文件，尝试格式化显示
            const jsonObj = JSON.parse(e.target.result);
            content = JSON.stringify(jsonObj, null, 2);
          } else if (file.type === 'text/csv') {
            // 如果是CSV文件，直接显示内容
            content = e.target.result;
          } else {
            // 其他文件类型，直接显示内容
            content = e.target.result;
          }

          // 如果内容太长，截断显示
          if (content.length > 2000) {
            content = content.substring(0, 2000) + '...(内容过长，已截断)';
          }

          // 将文件内容添加到输入框
          this.inputMessage = `我上传了一个文件：${file.name}\n\n文件内容：\n${content}`;

          // 清空文件输入，以便下次选择同一文件时也能触发change事件
          this.$refs.fileInput.value = '';

          // 提示用户
          this.$message.success('文件内容已加载到输入框');
        } catch (error) {
          console.error('读取文件出错:', error);
          this.$message.error('读取文件出错: ' + error.message);
          this.$refs.fileInput.value = '';
        }
      };

      reader.onerror = () => {
        this.$message.error('读取文件失败');
        this.$refs.fileInput.value = '';
      };

      // 根据文件类型选择读取方式
      if (file.type.includes('text') ||
          file.type.includes('json') ||
          file.type.includes('csv') ||
          file.name.endsWith('.md')) {
        reader.readAsText(file);
      } else {
        // 对于其他类型的文件，可能需要特殊处理
        this.$message.warning('此类型文件可能无法正确显示内容');
        reader.readAsText(file);
      }
    },

    // 格式化日期时间
    formatDateTime(date) {
      if (!date) return '';

      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');

      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    }
  }
}
</script>

<style scoped lang="scss">
.home {
  /* 添加背景图片 */
  background-image: url('~@/assets/images/tz/index_bg.jpg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  position: relative;
  min-height: 100vh;
  padding: 20px;

  /* 添加背景遮罩，使内容更易读 */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.85); /* 半透明白色背景 */
    z-index: -1;
  }

  blockquote {
    padding: 10px 20px;
    margin: 0 0 20px;
    font-size: 17.5px;
    border-left: 5px solid #eee;
  }

  hr {
    margin-top: 20px;
    margin-bottom: 20px;
    border: 0;
    border-top: 1px solid #eee;
  }

  .col-item {
    margin-bottom: 20px;
  }

  ul {
    padding: 0;
    margin: 0;
    list-style-type: none;
  }

  font-family: "open sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: 13px;
  color: #676a6c;
  overflow-x: hidden;

  h4 {
    margin-top: 0px;
  }

  h2 {
    margin-top: 10px;
    font-size: 26px;
    font-weight: 100;
  }

  p {
    margin-top: 10px;

    b {
      font-weight: 700;
    }
  }

  .update-log {
    ol {
      display: block;
      list-style-type: decimal;
      margin-block-start: 1em;
      margin-block-end: 1em;
      margin-inline-start: 0;
      margin-inline-end: 0;
      padding-inline-start: 40px;
    }
  }

  .task-list {
    padding: 0;

    .el-tabs__header {
      margin-bottom: 15px;
    }

    .task-items {
      list-style: none;
      padding: 0;
      margin: 0;
    }

    .task-item {
      display: flex;
      align-items: center;
      padding: 10px 0;
      border-bottom: 1px solid #ebeef5;
      cursor: pointer;
      transition: background-color 0.2s;

      /* 在移动端优化任务项布局 */
      @media (max-width: 768px) {
        flex-wrap: wrap;
        padding: 12px 0;
      }

      &:hover {
        background-color: #f5f7fa;
      }

      &:last-child {
        border-bottom: none;
      }

      .task-index {
        width: 50px;
        text-align: center;
        color: #606266;
        font-size: 13px;

        /* 在移动端优化序号显示 */
        @media (max-width: 768px) {
          width: 30px;
          font-size: 12px;
        }
      }

      .task-content {
        flex: 1;
        padding: 0 10px;
        color: #303133;
        font-size: 13px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        max-width: 50%;
        position: relative;

        /* 在移动端优化任务内容显示 */
        @media (max-width: 768px) {
          max-width: 100%;
          padding: 0 5px;
        }

        .new-badge {
          display: inline-block;
          padding: 0 5px;
          font-size: 10px;
          line-height: 16px;
          color: #fff;
          background-color: #f56c6c;
          border-radius: 10px;
          margin-left: 5px;
          vertical-align: top;
        }
      }

      .task-source {
        width: 100px;
        text-align: center;
        color: #606266;
        font-size: 13px;

        /* 在移动端优化任务来源显示 */
        @media (max-width: 768px) {
          width: auto;
          margin-right: 10px;
          margin-left: auto;
        }
      }

      .task-time {
        width: 150px;
        text-align: center;
        color: #909399;
        font-size: 13px;

        /* 在移动端优化时间显示 */
        @media (max-width: 768px) {
          width: 100%;
          text-align: right;
          margin-top: 5px;
          font-size: 12px;
        }
      }
    }

    .empty-task {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 30px 0;
      color: #909399;

      i {
        font-size: 32px;
        margin-bottom: 10px;
      }

      span {
        font-size: 14px;
      }
    }
  }
}
.chat-container {
  display: flex;
  width: 88%;
  height: 100%;
  border: 1px solid rgba(224, 224, 224, 0.5);
  border-radius: 12px;
  overflow: hidden;
  background-color: rgba(255, 255, 255, 0.9);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  margin: 0 auto;
}
.chat-content {
  width: 100%;
  height: 100%;
  padding: 15px;
  box-sizing: border-box;
}
.chatBox {
  display: flex;
  flex-direction: column;
  height: 92%;
  width: 100%;
  // border: 1px solid #e0e0e0;
  border-radius: 10px;
  overflow: hidden;
  background-color: white;
}
.error-message {
  background-color: #f8d7da;
  color: #721c24;
  padding: 12px;
  margin-bottom: 12px;
  border-radius: 8px;
  text-align: center;
}
.chat-messages {
  flex: 1;
  padding: 15px;
  overflow-y: auto;
}
.message {
  margin-bottom: 15px;
  display: flex;
  align-items: flex-start;
}
.user-message-container {
  display: flex;
  justify-content: flex-end;
  width: 100%;
  align-items: flex-start;
  gap: 10px;
}
.bot-message-container {
  display: flex;
  justify-content: flex-start;
  width: 100%;
  align-items: flex-start;
  gap: 10px;
}
.avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  flex-shrink: 0;
}
.user-avatar {
  border: 2px solid #2196f3;
}
.bot-avatar {
  border: 2px solid #4caf50;
}
.message-content {
  max-width: calc(80% - 50px);
  padding: 10px 15px;
  border-radius: 18px;
  line-height: 1.4;
  word-wrap: break-word;
}
.user-message {
  background-color: #e3f2fd;
  margin-left: 20%;
}
.bot-message {
  background-color: #f5f5f5;
  margin-right: 20%;
}
.chat-input {
  display: flex;
  flex-direction: column;
  // padding: 15px 20px;
  // border-top: 1px solid #e0e0e0;
  // background-color: #fff;
}
.chat-input textarea {
  // width: 100%;
  padding: 15px;
  border:none;
  // border: 1px solid #e0e0e0;
  // border-radius: 8px;
  // margin-bottom: 10px;
  resize: none;
  // min-height: 60px;
  // max-height: 150px;
  // font-family: inherit;
  // font-size: 15px;
  // box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}
/* 输入框焦点样式 */
.chat-input textarea:focus {
  outline: none;
}

.input-options {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.button-group {
  display: flex;
  align-items: center;
  gap: 10px;

  /* 在移动端优化按钮组布局 */
  @media (max-width: 768px) {
    flex-wrap: wrap;
    justify-content: center;
    gap: 8px;
  }
}

.option-btn {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 6px 12px;
  background-color: #f5f5f5;
  border: 1px solid #e0e0e0;
  border-radius: 16px;
  color: #666;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s;

  /* 在移动端优化按钮样式 */
  @media (max-width: 768px) {
    padding: 5px 10px;
    font-size: 12px;
    white-space: nowrap;
  }

  &:hover {
    background-color: #e9e9e9;
    border-color: #2196f3;
    color: #2196f3;
  }

  &.active {
    background-color: #e3f2fd;
    border-color: #2196f3;
    color: #2196f3;
  }

  i {
    font-size: 14px;
  }
}

.input-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 8px;
}

.upload-btn {
  background-color: transparent;
  color: #666;
  border: none;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: #f0f0f0;
    color: #2196f3;
  }

  &:disabled {
    color: #ccc;
    cursor: not-allowed;
  }

  i {
    font-size: 18px;
  }
}

.send-btn {
  background-color: #2196f3;
  color: white;
  border: none;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background-color: #1976d2;
  }

  &:disabled {
    background-color: #e0e0e0;
    color: #9e9e9e;
    cursor: not-allowed;
  }

  i {
    font-size: 16px;
  }
}

// .chat-input button {
//   padding: 0 24px;
//   height: 40px;
//   background-color: #2196f3;
//   color: white;
//   border: none;
//   border-radius: 8px;
//   cursor: pointer;
//   font-size: 14px;
//   transition: background-color 0.2s;
// }
// .chat-input button:hover {
//   background-color: #1976d2;
// }
.chat-input button:disabled {
  background-color: #b0bec5;
  cursor: not-allowed;
}
.chat-input .navigate-btn {
  margin-left: 10px;
  background-color: #4caf50;
}
.chat-input .navigate-btn:hover {
  background-color: #388e3c;
}
.bot-message:not([data-completed="true"]):after {
  content: "|";
  animation: blink 1s infinite;
  color: #666;
}
@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0; }
}
/* 深度思考样式 */
.deepThink {
  color: #666;
  font-style: italic;
  background-color: #f5f5f5;
  padding: 10px 14px;
  border-radius: 8px;
  display: inline-block;
  margin-top: 8px;
  font-size: 14px;
}
.thinking-container {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 8px 0;
}
.thinking-icon {
  width: 16px;
  height: 16px;
  opacity: 0.6;
}
/* 任务列表样式已更新，使用 ul 和 li 替代 el-table */

/* 卡片样式 */
/* 标题行 */
.flex-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 0 5px;

  /* 标题 */
  h3 {
    margin: 0;
    font-size: 18px;
    color: #303133;
    font-weight: 600;
    position: relative;
    padding-left: 12px;

    /* 左侧装饰线 */
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 4px;
      height: 16px;
      background-color: #2196f3;
      border-radius: 2px;
    }
  }

  /* "更多"链接 */
  p {
    margin: 0;
    font-size: 14px;
    color: #2196f3;
    cursor: pointer;
    transition: color 0.2s ease;

    &:hover {
      color: #1976d2;
      text-decoration: underline;
    }
  }
}

/* 卡片容器 */
.card-con {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 5px;
}

/* 卡片行 */
.card-row {
  display: flex;
  gap: 20px;

  /* 响应式布局 - 在小屏幕上垂直排列 */
  @media (max-width: 768px) {
    flex-direction: column;
  }
}

/* 卡片项 */
.card-item {
  display: flex;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  flex: 1;
  border: 1px solid rgba(224, 224, 224, 0.5);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);

  /* 悬停效果 */
  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
    border-color: rgba(33, 150, 243, 0.4);
    background-color: rgba(255, 255, 255, 0.95);
  }

  /* 点击效果 */
  &:active {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

/* 卡片左侧图标 */
.card-left {
  width: 42px;
  height: 42px;
  margin-right: 16px;
  display: flex;
  align-items: center;
  justify-content: center;

  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    transition: transform 0.2s ease;

    /* 图标悬停效果 */
    .card-item:hover & {
      transform: scale(1.05);
    }
  }
}

/* 卡片右侧内容 */
.card-right {
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex: 1;
  min-width: 0; /* 确保文本可以正确截断 */
}

/* 卡片标题 */
.card-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 6px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 卡片副标题 */
.card-subtitle {
  font-size: 12px;
  color: #909399;
  display: flex;
  align-items: center;

  /* 副标题数字标记 */
  .subtitle-num {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background-color: #f56c6c;
    color: #fff;
    border-radius: 10px;
    padding: 0 6px;
    height: 18px;
    margin-left: 6px;
    font-size: 12px;
    font-weight: 500;
  }
}

/* 标题区域样式 */
.content-title {
  display: flex;
  align-items: center;
  padding: 20px;
  margin-bottom: 20px;
  // background-color: rgba(255, 255, 255, 0.9);
  // border-radius: 12px;
  // box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
  // backdrop-filter: blur(5px);
  // -webkit-backdrop-filter: blur(5px);
  // border: 1px solid rgba(224, 224, 224, 0.5);

  /* 在移动端优化标题区域布局 */
  @media (max-width: 768px) {
    flex-direction: column;
    text-align: center;
    padding: 15px;
  }

  .title-left {
    flex: 0 0 auto;
    margin-right: 20px;

    /* 在移动端优化图片布局 */
    @media (max-width: 768px) {
      margin-right: 0;
      margin-bottom: 15px;
    }

    img {
      width: 120px;
      height: 120px;
      object-fit: contain;
      // border-radius: 50%;
      // box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
      padding: 5px;
      // background-color: #fff;
      transition: transform 0.3s ease;

      &:hover {
        transform: scale(1.05);
      }

      /* 在移动端优化图片大小 */
      @media (max-width: 768px) {
        width: 70px;
        height: 70px;
      }
    }
  }

  .title-right {
    flex: 1;

    /* 在移动端优化文字布局 */
    @media (max-width: 768px) {
      width: 100%;
    }

    h1 {
      margin: 0 0 10px 0;
      font-size: 24px;
      font-weight: 600;
      color: #303133;

      /* 在移动端优化标题字体大小 */
      @media (max-width: 768px) {
        font-size: 20px;
        margin-bottom: 8px;
      }
    }

    p {
      margin: 0;
      font-size: 16px;
      color: #606266;

      /* 在移动端优化副标题字体大小 */
      @media (max-width: 768px) {
        font-size: 14px;
      }
    }
  }
}
.update-log, .business-block {
  padding: 15px;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  border: 1px solid rgba(224, 224, 224, 0.5);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  height: 100%; /* 确保两个区块高度一致 */
  display: flex;
  flex-direction: column;

  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
  }
}

/* 确保在小屏幕上有适当的间距和垂直排列 */
@media (max-width: 991px) {
  .update-log, .business-block {
    margin-bottom: 20px;
  }

  /* 在小屏幕上强制垂直排列 */
  .el-row {
    display: block !important;
  }

  .el-col {
    width: 100% !important;
  }
}

/* 确保在大屏幕上两个区块并排显示且高度一致 */
@media (min-width: 992px) {
  .el-row {
    display: flex;
    flex-wrap: nowrap; /* 防止换行 */

    .el-col {
      display: flex;
      flex-direction: column;
      float: none; /* 覆盖 Element UI 的默认浮动 */
    }

    .el-card, .business-block {
      flex: 1;
      display: flex;
      flex-direction: column;
      height: 100%; /* 确保高度一致 */
    }

    .el-card .el-card__body {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: auto; /* 内容过多时可滚动 */
    }

    .task-list, .card-con {
      flex: 1;
      display: flex;
      flex-direction: column;
    }
  }

  /* 确保宽度比例正确 */
  .el-col[class*=md-13] {
    width: 56% !important;
  }

  .el-col[class*=md-11] {
    width: 44% !important;
  }
}
</style>

