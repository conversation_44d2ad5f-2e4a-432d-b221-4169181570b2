<template>
  <div class="enterprise-form">
    <el-form ref="form" :model="form" :rules="rules" label-width="120px" size="small">
      <el-form-item label="企业名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入企业名称"></el-input>
      </el-form-item>
      
      <el-form-item label="统一社会信用代码" prop="creditCode">
        <el-input v-model="form.creditCode" placeholder="请输入统一社会信用代码"></el-input>
      </el-form-item>
      
      <el-form-item label="企业类型" prop="type">
        <el-select v-model="form.type" placeholder="请选择企业类型" style="width: 100%">
          <el-option label="有限责任公司" value="有限责任公司"></el-option>
          <el-option label="股份有限公司" value="股份有限公司"></el-option>
          <el-option label="个人独资企业" value="个人独资企业"></el-option>
          <el-option label="合伙企业" value="合伙企业"></el-option>
          <el-option label="其他" value="其他"></el-option>
        </el-select>
      </el-form-item>
      
      <el-form-item label="行业分类" prop="industry">
        <el-select v-model="form.industry" placeholder="请选择行业分类" style="width: 100%">
          <el-option v-for="item in industryOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      
      <el-form-item label="企业规模" prop="scale">
        <el-select v-model="form.scale" placeholder="请选择企业规模" style="width: 100%">
          <el-option v-for="item in scaleOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      
      <el-form-item label="风险等级" prop="riskLevel">
        <el-select v-model="form.riskLevel" placeholder="请选择风险等级" style="width: 100%">
          <el-option v-for="item in riskLevelOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      
      <el-form-item label="注册资本" prop="registeredCapital">
        <el-input v-model="form.registeredCapital" placeholder="请输入注册资本"></el-input>
      </el-form-item>
      
      <el-form-item label="成立日期" prop="establishDate">
        <el-date-picker
          v-model="form.establishDate"
          type="date"
          placeholder="选择日期"
          style="width: 100%"
          value-format="yyyy-MM-dd">
        </el-date-picker>
      </el-form-item>
      
      <el-form-item label="所属区域" prop="region">
        <el-select v-model="form.region" placeholder="请选择所属区域" style="width: 100%">
          <el-option v-for="item in regionOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      
      <el-form-item label="注册地址" prop="address">
        <el-input v-model="form.address" placeholder="请输入注册地址"></el-input>
      </el-form-item>
      
      <el-form-item label="经营范围" prop="businessScope">
        <el-input
          type="textarea"
          v-model="form.businessScope"
          :rows="3"
          placeholder="请输入经营范围">
        </el-input>
      </el-form-item>
      
      <el-form-item label="法定代表人" prop="legalPerson">
        <el-input v-model="form.legalPerson" placeholder="请输入法定代表人姓名"></el-input>
      </el-form-item>
      
      <el-form-item label="联系电话" prop="phone">
        <el-input v-model="form.phone" placeholder="请输入联系电话"></el-input>
      </el-form-item>
      
      <el-form-item label="电子邮箱" prop="email">
        <el-input v-model="form.email" placeholder="请输入电子邮箱"></el-input>
      </el-form-item>
      
      <el-form-item>
        <el-button type="primary" @click="submitForm">保存</el-button>
        <el-button @click="cancel">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'EnterpriseForm',
  props: {
    enterprise: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      form: {
        id: '',
        name: '',
        creditCode: '',
        type: '',
        industry: '',
        scale: '',
        riskLevel: '',
        registeredCapital: '',
        establishDate: '',
        region: '',
        address: '',
        businessScope: '',
        legalPerson: '',
        phone: '',
        email: ''
      },
      rules: {
        name: [
          { required: true, message: '请输入企业名称', trigger: 'blur' }
        ],
        creditCode: [
          { required: true, message: '请输入统一社会信用代码', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '请选择企业类型', trigger: 'change' }
        ],
        industry: [
          { required: true, message: '请选择行业分类', trigger: 'change' }
        ],
        scale: [
          { required: true, message: '请选择企业规模', trigger: 'change' }
        ],
        riskLevel: [
          { required: true, message: '请选择风险等级', trigger: 'change' }
        ],
        region: [
          { required: true, message: '请选择所属区域', trigger: 'change' }
        ],
        address: [
          { required: true, message: '请输入注册地址', trigger: 'blur' }
        ],
        legalPerson: [
          { required: true, message: '请输入法定代表人姓名', trigger: 'blur' }
        ]
      },
      industryOptions: [
        { value: '制造业', label: '制造业' },
        { value: '服务业', label: '服务业' },
        { value: '建筑业', label: '建筑业' },
        { value: '金融业', label: '金融业' },
        { value: '其他', label: '其他' }
      ],
      scaleOptions: [
        { value: '大型', label: '大型' },
        { value: '中型', label: '中型' },
        { value: '小型', label: '小型' },
        { value: '微型', label: '微型' }
      ],
      riskLevelOptions: [
        { value: '高风险', label: '高风险' },
        { value: '中风险', label: '中风险' },
        { value: '低风险', label: '低风险' }
      ],
      regionOptions: [
        { value: '通州区', label: '通州区' },
        { value: '朝阳区', label: '朝阳区' },
        { value: '海淀区', label: '海淀区' },
        { value: '丰台区', label: '丰台区' },
        { value: '其他', label: '其他' }
      ]
    }
  },
  created() {
    if (this.enterprise) {
      // 编辑模式，填充表单数据
      Object.keys(this.form).forEach(key => {
        if (this.enterprise[key] !== undefined) {
          this.form[key] = this.enterprise[key]
        }
      })
    }
  },
  methods: {
    // 提交表单
    submitForm() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.$emit('submit', { ...this.form })
        } else {
          return false
        }
      })
    },
    
    // 取消
    cancel() {
      this.$emit('cancel')
    }
  }
}
</script>

<style lang="scss" scoped>
.enterprise-form {
  padding: 10px;
}
</style>
