<template>
  <div class="enterprise-detail">
    <el-tabs v-model="activeTab">
      <el-tab-pane label="基本信息" name="basic">
        <div class="detail-section">
          <h3 class="section-title">企业基本信息</h3>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="info-item">
                <span class="label">企业名称：</span>
                <span class="value">{{ enterpriseData.name }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">统一社会信用代码：</span>
                <span class="value">{{ enterpriseData.creditCode }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">企业类型：</span>
                <span class="value">{{ enterpriseData.type }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">行业分类：</span>
                <span class="value">{{ enterpriseData.industry }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">企业规模：</span>
                <span class="value">{{ enterpriseData.scale }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">风险等级：</span>
                <span class="value">
                  <el-tag :type="getRiskLevelType(enterpriseData.riskLevel)" size="mini">{{ enterpriseData.riskLevel }}</el-tag>
                </span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">注册资本：</span>
                <span class="value">{{ enterpriseData.registeredCapital }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">成立日期：</span>
                <span class="value">{{ enterpriseData.establishDate }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">所属区域：</span>
                <span class="value">{{ enterpriseData.region }}</span>
              </div>
            </el-col>
            <el-col :span="24">
              <div class="info-item">
                <span class="label">注册地址：</span>
                <span class="value">{{ enterpriseData.address }}</span>
              </div>
            </el-col>
            <el-col :span="24">
              <div class="info-item">
                <span class="label">经营范围：</span>
                <span class="value">{{ enterpriseData.businessScope }}</span>
              </div>
            </el-col>
          </el-row>
        </div>
        
        <div class="detail-section">
          <h3 class="section-title">联系信息</h3>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="info-item">
                <span class="label">法定代表人：</span>
                <span class="value">{{ enterpriseData.legalPerson }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">联系电话：</span>
                <span class="value">{{ enterpriseData.phone }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">电子邮箱：</span>
                <span class="value">{{ enterpriseData.email }}</span>
              </div>
            </el-col>
          </el-row>
        </div>
      </el-tab-pane>
      
      <el-tab-pane label="风险信息" name="risk">
        <div class="detail-section">
          <h3 class="section-title">风险评估</h3>
          <el-row :gutter="20">
            <el-col :span="24">
              <div class="risk-chart">
                <div class="chart-placeholder">风险评估图表展示区域</div>
              </div>
            </el-col>
          </el-row>
          
          <h4 class="subsection-title">风险因素</h4>
          <el-table :data="riskFactors" style="width: 100%">
            <el-table-column prop="category" label="风险类别" width="180"></el-table-column>
            <el-table-column prop="description" label="风险描述"></el-table-column>
            <el-table-column prop="level" label="风险等级" width="120">
              <template slot-scope="scope">
                <el-tag :type="getRiskLevelType(scope.row.level)" size="mini">{{ scope.row.level }}</el-tag>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-tab-pane>
      
      <el-tab-pane label="监管记录" name="supervision">
        <div class="detail-section">
          <h3 class="section-title">监管记录</h3>
          <el-timeline>
            <el-timeline-item
              v-for="(record, index) in supervisionRecords"
              :key="index"
              :timestamp="record.date"
              :type="getTimelineItemType(record.type)"
            >
              <h4>{{ record.title }}</h4>
              <p>{{ record.content }}</p>
              <div v-if="record.result" class="record-result">
                <span class="label">处理结果：</span>
                <span class="value">{{ record.result }}</span>
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
export default {
  name: 'EnterpriseDetail',
  props: {
    enterpriseId: {
      type: [Number, String],
      required: true
    }
  },
  data() {
    return {
      activeTab: 'basic',
      enterpriseData: {},
      riskFactors: [],
      supervisionRecords: []
    }
  },
  created() {
    this.fetchEnterpriseDetail()
  },
  methods: {
    // 获取企业详情
    fetchEnterpriseDetail() {
      // 模拟数据，实际项目中应该调用API
      this.enterpriseData = {
        id: this.enterpriseId,
        name: `企业${this.enterpriseId}`,
        creditCode: `91110000${10000000 + parseInt(this.enterpriseId)}`,
        type: '有限责任公司',
        industry: '制造业',
        scale: '中型',
        riskLevel: '中风险',
        registeredCapital: '1000万元',
        establishDate: '2015-06-15',
        region: '通州区',
        address: '北京市通州区XX路XX号',
        businessScope: '计算机软硬件技术开发、技术转让、技术咨询、技术服务；数据处理；应用软件服务；销售计算机软硬件及辅助设备、电子产品、通讯设备。',
        legalPerson: '张三',
        phone: '010-12345678',
        email: '<EMAIL>'
      }
      
      this.riskFactors = [
        { category: '经营风险', description: '企业经营状况不稳定，近期有经营异常记录', level: '中风险' },
        { category: '安全风险', description: '企业存在安全隐患，需要加强安全管理', level: '高风险' },
        { category: '环保风险', description: '企业环保设施完善，环保意识较强', level: '低风险' },
        { category: '信用风险', description: '企业信用记录良好，无严重失信行为', level: '低风险' }
      ]
      
      this.supervisionRecords = [
        {
          date: '2023-04-15',
          type: 'warning',
          title: '安全检查',
          content: '发现企业存在安全隐患，责令限期整改',
          result: '已整改'
        },
        {
          date: '2022-11-20',
          type: 'success',
          title: '环保检查',
          content: '企业环保设施运行正常，无环保违规行为',
          result: '合格'
        },
        {
          date: '2022-08-05',
          type: 'danger',
          title: '投诉处理',
          content: '收到关于企业噪音扰民的投诉，进行现场调查',
          result: '责令整改并处罚'
        },
        {
          date: '2022-03-12',
          type: 'info',
          title: '日常巡查',
          content: '对企业进行日常巡查，检查经营情况',
          result: '正常'
        }
      ]
    },
    
    // 根据风险等级获取标签类型
    getRiskLevelType(level) {
      switch(level) {
        case '高风险': return 'danger'
        case '中风险': return 'warning'
        case '低风险': return 'success'
        default: return 'info'
      }
    },
    
    // 获取时间线项目类型
    getTimelineItemType(type) {
      switch(type) {
        case 'success': return 'success'
        case 'warning': return 'warning'
        case 'danger': return 'danger'
        default: return 'primary'
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.enterprise-detail {
  .detail-section {
    margin-bottom: 30px;
    
    .section-title {
      margin-top: 0;
      margin-bottom: 20px;
      padding-bottom: 10px;
      font-size: 18px;
      font-weight: 600;
      color: #303133;
      border-bottom: 1px solid #ebeef5;
    }
    
    .subsection-title {
      margin-top: 20px;
      margin-bottom: 15px;
      font-size: 16px;
      font-weight: 500;
      color: #303133;
    }
    
    .info-item {
      margin-bottom: 15px;
      
      .label {
        color: #909399;
        margin-right: 5px;
      }
      
      .value {
        color: #303133;
        word-break: break-all;
      }
    }
    
    .risk-chart {
      margin: 20px 0;
      
      .chart-placeholder {
        height: 300px;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: #f5f7fa;
        border-radius: 4px;
        color: #909399;
        font-size: 14px;
      }
    }
    
    .record-result {
      margin-top: 8px;
      font-size: 13px;
      
      .label {
        color: #909399;
      }
      
      .value {
        color: #303133;
      }
    }
  }
}
</style>
