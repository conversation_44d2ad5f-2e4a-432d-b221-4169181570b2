<template>
  <div class="maxkb-chat-container">
    <!-- 聊天头部 -->
    <div class="chat-header">
      <div class="header-left">
        <i class="el-icon-chat-dot-round"></i>
        <span class="chat-title">{{ title }}</span>
      </div>
      <div class="header-right">
        <el-button 
          type="text" 
          icon="el-icon-refresh" 
          @click="clearChat"
          title="清空对话"
        ></el-button>
        <el-button 
          type="text" 
          :icon="isFullscreen ? 'el-icon-copy-document' : 'el-icon-full-screen'" 
          @click="toggleFullscreen"
          :title="isFullscreen ? '退出全屏' : '全屏'"
        ></el-button>
      </div>
    </div>

    <!-- 聊天消息区域 -->
    <div class="chat-messages" ref="messagesContainer">
      <div class="messages-wrapper">
        <!-- 欢迎消息 -->
        <div v-if="messages.length === 0" class="welcome-message">
          <div class="welcome-content">
            <i class="el-icon-chat-dot-round welcome-icon"></i>
            <h3>{{ welcomeTitle }}</h3>
            <p>{{ welcomeText }}</p>
            <div class="quick-questions" v-if="quickQuestions.length > 0">
              <h4>快速提问：</h4>
              <div class="question-buttons">
                <el-button
                  v-for="(question, index) in quickQuestions"
                  :key="index"
                  size="small"
                  type="primary"
                  plain
                  @click="sendQuickQuestion(question)"
                >
                  {{ question }}
                </el-button>
              </div>
            </div>
          </div>
        </div>

        <!-- 消息列表 -->
        <div
          v-for="(message, index) in messages"
          :key="index"
          :class="['message-item', message.role]"
        >
          <div class="message-avatar">
            <i :class="message.role === 'user' ? 'el-icon-user' : 'el-icon-chat-dot-round'"></i>
          </div>
          <div class="message-content">
            <div class="message-text" v-html="formatMessage(message.content)"></div>
            <div class="message-time">{{ formatTime(message.timestamp) }}</div>
          </div>
        </div>

        <!-- 正在输入指示器 -->
        <div v-if="isTyping" class="message-item assistant typing">
          <div class="message-avatar">
            <i class="el-icon-chat-dot-round"></i>
          </div>
          <div class="message-content">
            <div class="typing-indicator">
              <span></span>
              <span></span>
              <span></span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 输入区域 -->
    <div class="chat-input">
      <div class="input-wrapper">
        <el-input
          v-model="inputMessage"
          type="textarea"
          :rows="inputRows"
          :placeholder="placeholder"
          :disabled="isLoading"
          @keydown.enter.exact="handleEnterKey"
          @keydown.enter.shift.exact="handleShiftEnter"
          ref="messageInput"
          resize="none"
          maxlength="2000"
          show-word-limit
        ></el-input>
        <div class="input-actions">
          <el-button
            type="primary"
            :loading="isLoading"
            :disabled="!inputMessage.trim()"
            @click="sendMessage"
            icon="el-icon-s-promotion"
          >
            发送
          </el-button>
        </div>
      </div>
      <div class="input-tips">
        <span class="tip">按 Enter 发送，Shift + Enter 换行</span>
        <span class="tip">{{ inputMessage.length }}/2000</span>
      </div>
    </div>
  </div>
</template>

<script>
import { sendMessage, sendStreamMessage, createConversation } from '@/api/maxkb'
import marked from 'marked'
import DOMPurify from 'dompurify'

export default {
  name: 'MaxKBChat',
  props: {
    title: {
      type: String,
      default: 'AI助手'
    },
    welcomeTitle: {
      type: String,
      default: '欢迎使用AI助手'
    },
    welcomeText: {
      type: String,
      default: '我是您的智能助手，可以回答各种问题，协助您完成工作。请输入您的问题开始对话。'
    },
    placeholder: {
      type: String,
      default: '请输入您的问题...'
    },
    quickQuestions: {
      type: Array,
      default: () => [
        '你好，请介绍一下自己',
        '你能帮我做什么？',
        '如何使用这个系统？'
      ]
    },
    useStream: {
      type: Boolean,
      default: true
    },
    maxMessages: {
      type: Number,
      default: 100
    }
  },
  data() {
    return {
      messages: [],
      inputMessage: '',
      isLoading: false,
      isTyping: false,
      conversationId: null,
      inputRows: 1,
      isFullscreen: false,
      streamController: null
    }
  },
  mounted() {
    this.initConversation()
    this.adjustInputHeight()
  },
  beforeDestroy() {
    if (this.streamController) {
      this.streamController()
    }
  },
  methods: {
    // 初始化会话
    async initConversation() {
      try {
        const response = await createConversation()
        if (response.code === 200) {
          this.conversationId = response.data.id
        }
      } catch (error) {
        console.error('创建会话失败:', error)
        this.$message.error('初始化AI助手失败')
      }
    },

    // 发送消息
    async sendMessage() {
      if (!this.inputMessage.trim() || this.isLoading) return

      const userMessage = this.inputMessage.trim()
      this.inputMessage = ''
      this.adjustInputHeight()

      // 添加用户消息
      this.addMessage('user', userMessage)

      this.isLoading = true
      this.isTyping = true

      try {
        if (this.useStream) {
          await this.sendStreamMessage(userMessage)
        } else {
          await this.sendNormalMessage(userMessage)
        }
      } catch (error) {
        console.error('发送消息失败:', error)
        this.addMessage('assistant', '抱歉，我遇到了一些问题，请稍后再试。')
        this.$message.error('发送消息失败')
      } finally {
        this.isLoading = false
        this.isTyping = false
      }
    },

    // 流式发送消息
    async sendStreamMessage(message) {
      let assistantMessage = ''
      let messageIndex = -1

      this.streamController = await sendStreamMessage({
        message,
        conversationId: this.conversationId,
        onMessage: (data) => {
          if (data.content) {
            assistantMessage += data.content
            
            if (messageIndex === -1) {
              messageIndex = this.addMessage('assistant', assistantMessage)
              this.isTyping = false
            } else {
              this.updateMessage(messageIndex, assistantMessage)
            }
            
            this.scrollToBottom()
          }
        },
        onError: (error) => {
          console.error('流式消息错误:', error)
          if (messageIndex === -1) {
            this.addMessage('assistant', '抱歉，我遇到了一些问题，请稍后再试。')
          }
        },
        onComplete: () => {
          this.streamController = null
        }
      })
    },

    // 普通发送消息
    async sendNormalMessage(message) {
      const response = await sendMessage({
        message,
        conversationId: this.conversationId,
        history: this.getMessageHistory()
      })

      if (response.code === 200) {
        this.addMessage('assistant', response.data.content || response.data.message)
      } else {
        throw new Error(response.message || '发送失败')
      }
    },

    // 添加消息
    addMessage(role, content) {
      const message = {
        role,
        content,
        timestamp: new Date()
      }
      
      this.messages.push(message)
      
      // 限制消息数量
      if (this.messages.length > this.maxMessages) {
        this.messages.splice(0, this.messages.length - this.maxMessages)
      }
      
      this.$nextTick(() => {
        this.scrollToBottom()
      })
      
      return this.messages.length - 1
    },

    // 更新消息
    updateMessage(index, content) {
      if (this.messages[index]) {
        this.messages[index].content = content
      }
    },

    // 获取消息历史
    getMessageHistory() {
      return this.messages.slice(-10).map(msg => ({
        role: msg.role,
        content: msg.content
      }))
    },

    // 快速提问
    sendQuickQuestion(question) {
      this.inputMessage = question
      this.sendMessage()
    },

    // 清空对话
    clearChat(skipConfirm = false) {
      if (skipConfirm) {
        this.messages = []
        this.initConversation()
        return
      }

      this.$confirm('确定要清空所有对话记录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.messages = []
        this.initConversation()
        this.$message.success('对话已清空')
      }).catch(() => {})
    },

    // 切换全屏
    toggleFullscreen() {
      this.isFullscreen = !this.isFullscreen
      this.$emit('fullscreen-change', this.isFullscreen)
    },

    // 处理Enter键
    handleEnterKey(event) {
      if (!event.shiftKey) {
        event.preventDefault()
        this.sendMessage()
      }
    },

    // 处理Shift+Enter
    handleShiftEnter() {
      // 允许换行
    },

    // 调整输入框高度
    adjustInputHeight() {
      this.$nextTick(() => {
        const lines = this.inputMessage.split('\n').length
        this.inputRows = Math.min(Math.max(lines, 1), 4)
      })
    },

    // 滚动到底部
    scrollToBottom() {
      this.$nextTick(() => {
        const container = this.$refs.messagesContainer
        if (container) {
          container.scrollTop = container.scrollHeight
        }
      })
    },

    // 格式化消息
    formatMessage(content) {
      // 使用marked解析Markdown
      const html = marked(content)
      // 使用DOMPurify清理HTML
      return DOMPurify.sanitize(html)
    },

    // 格式化时间
    formatTime(timestamp) {
      const now = new Date()
      const time = new Date(timestamp)
      const diff = now - time

      if (diff < 60000) { // 1分钟内
        return '刚刚'
      } else if (diff < 3600000) { // 1小时内
        return `${Math.floor(diff / 60000)}分钟前`
      } else if (diff < 86400000) { // 24小时内
        return time.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
      } else {
        return time.toLocaleString('zh-CN', { 
          month: '2-digit', 
          day: '2-digit', 
          hour: '2-digit', 
          minute: '2-digit' 
        })
      }
    }
  },
  watch: {
    inputMessage() {
      this.adjustInputHeight()
    }
  }
}
</script>

<style lang="scss" scoped>
.maxkb-chat-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-bottom: 1px solid #e1e8ed;

  .header-left {
    display: flex;
    align-items: center;

    i {
      font-size: 20px;
      margin-right: 8px;
    }

    .chat-title {
      font-size: 16px;
      font-weight: 600;
    }
  }

  .header-right {
    .el-button {
      color: white;
      
      &:hover {
        background-color: rgba(255, 255, 255, 0.1);
      }
    }
  }
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  background: #f8f9fa;

  .messages-wrapper {
    max-width: 800px;
    margin: 0 auto;
  }

  .welcome-message {
    text-align: center;
    padding: 40px 20px;

    .welcome-content {
      .welcome-icon {
        font-size: 48px;
        color: #667eea;
        margin-bottom: 16px;
      }

      h3 {
        margin: 0 0 8px 0;
        color: #333;
        font-size: 24px;
      }

      p {
        margin: 0 0 24px 0;
        color: #666;
        line-height: 1.6;
      }

      .quick-questions {
        h4 {
          margin: 0 0 12px 0;
          color: #333;
          font-size: 16px;
        }

        .question-buttons {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
          justify-content: center;

          .el-button {
            margin: 0;
          }
        }
      }
    }
  }

  .message-item {
    display: flex;
    margin-bottom: 20px;

    &.user {
      flex-direction: row-reverse;

      .message-content {
        background: #667eea;
        color: white;
        margin-right: 12px;
        margin-left: 60px;
      }

      .message-avatar {
        background: #667eea;
        color: white;
      }
    }

    &.assistant {
      .message-content {
        background: white;
        margin-left: 12px;
        margin-right: 60px;
      }

      .message-avatar {
        background: #f1f3f4;
        color: #667eea;
      }
    }

    .message-avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;

      i {
        font-size: 18px;
      }
    }

    .message-content {
      max-width: calc(100% - 120px);
      padding: 12px 16px;
      border-radius: 18px;
      position: relative;

      .message-text {
        line-height: 1.6;
        word-wrap: break-word;

        ::v-deep {
          p {
            margin: 0 0 8px 0;
            
            &:last-child {
              margin-bottom: 0;
            }
          }

          code {
            background: rgba(0, 0, 0, 0.1);
            padding: 2px 4px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
          }

          pre {
            background: rgba(0, 0, 0, 0.1);
            padding: 12px;
            border-radius: 8px;
            overflow-x: auto;
            margin: 8px 0;

            code {
              background: none;
              padding: 0;
            }
          }

          ul, ol {
            margin: 8px 0;
            padding-left: 20px;
          }

          blockquote {
            border-left: 4px solid #ddd;
            margin: 8px 0;
            padding-left: 12px;
            color: #666;
          }
        }
      }

      .message-time {
        font-size: 12px;
        color: rgba(255, 255, 255, 0.7);
        margin-top: 4px;
      }
    }

    &.assistant .message-content .message-time {
      color: #999;
    }

    &.typing .message-content {
      padding: 16px;
    }
  }

  .typing-indicator {
    display: flex;
    align-items: center;
    gap: 4px;

    span {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: #ccc;
      animation: typing 1.4s infinite ease-in-out;

      &:nth-child(1) { animation-delay: -0.32s; }
      &:nth-child(2) { animation-delay: -0.16s; }
    }
  }
}

.chat-input {
  padding: 20px;
  background: white;
  border-top: 1px solid #e1e8ed;

  .input-wrapper {
    display: flex;
    gap: 12px;
    align-items: flex-end;

    .el-textarea {
      flex: 1;
    }

    .input-actions {
      .el-button {
        height: 40px;
      }
    }
  }

  .input-tips {
    display: flex;
    justify-content: space-between;
    margin-top: 8px;
    font-size: 12px;
    color: #999;
  }
}

@keyframes typing {
  0%, 60%, 100% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-10px);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .chat-messages {
    padding: 12px;

    .message-item {
      .message-content {
        max-width: calc(100% - 80px);
      }

      &.user .message-content {
        margin-left: 40px;
      }

      &.assistant .message-content {
        margin-right: 40px;
      }
    }

    .welcome-message {
      padding: 20px 12px;

      .welcome-content {
        .quick-questions .question-buttons {
          flex-direction: column;
          align-items: center;
        }
      }
    }
  }

  .chat-input {
    padding: 12px;

    .input-wrapper {
      flex-direction: column;
      gap: 8px;

      .input-actions {
        align-self: flex-end;
      }
    }
  }
}
</style>
