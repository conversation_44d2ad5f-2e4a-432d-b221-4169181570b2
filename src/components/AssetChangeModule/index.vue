<template>
  <div class="asset-change-module">
    <el-row :gutter="20">
      <!-- 左侧：资产变更情况折线图 -->
      <el-col :span="8">
        <div class="chart-card">
          <div class="chart-title">资产变更情况</div>
          <div id="assetChangeChart" class="chart-container"></div>
        </div>
      </el-col>
      
      <!-- 中间：数字资产分布情况环状饼图 -->
      <el-col :span="8">
        <div class="chart-card">
          <div class="chart-title">数字资产分布情况</div>
          <div id="assetDistributionChart" class="chart-container"></div>
        </div>
      </el-col>
      
      <!-- 右侧：数字资产同比增长情况双柱状图 -->
      <el-col :span="8">
        <div class="chart-card">
          <div class="chart-title">数字资产同比增长情况</div>
          <div id="assetGrowthChart" class="chart-container"></div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'AssetChangeModule',
  data() {
    return {
      // 图表实例
      assetChangeChart: null,
      assetDistributionChart: null,
      assetGrowthChart: null,
      
      // 资产变更数据
      assetChangeData: {
        months: ['2024.6', '2024.7', '2024.8', '2024.9', '2024.10'],
        change: [120, 132, 101, 134, 90],
        increase: [220, 182, 191, 234, 290]
      },
      
      // 数字资产分布数据
      assetDistributionData: [
        { name: '宽带用户总数', value: 6000, itemStyle: { color: '#3481FF' } },
        { name: 'IDC托管用户', value: 2000, itemStyle: { color: '#1ABB78' } },
        { name: 'IP地址段', value: 1000, itemStyle: { color: '#FFC53D' } },
        { name: '接入网站', value: 500, itemStyle: { color: '#FF7A45' } },
        { name: '虚拟运营', value: 500, itemStyle: { color: '#722ED1' } }
      ],
      
      // 数字资产同比增长数据
      assetGrowthData: {
        categories: ['宽带用户', 'IP地址段', '云服务', '专线用户', '域名注册'],
        currentMonth: [30, 52, 70, 34, 90],
        lastYear: [40, 30, 50, 60, 70]
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initAssetChangeChart()
      this.initAssetDistributionChart()
      this.initAssetGrowthChart()
      
      // 监听窗口大小变化，调整图表大小
      window.addEventListener('resize', this.handleResize)
    })
  },
  beforeDestroy() {
    // 销毁图表实例，避免内存泄漏
    this.assetChangeChart && this.assetChangeChart.dispose()
    this.assetDistributionChart && this.assetDistributionChart.dispose()
    this.assetGrowthChart && this.assetGrowthChart.dispose()
    
    // 移除事件监听
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    // 初始化资产变更情况折线图
    initAssetChangeChart() {
      const chartDom = document.getElementById('assetChangeChart')
      if (!chartDom) return
      
      this.assetChangeChart = echarts.init(chartDom)
      
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985'
            }
          }
        },
        legend: {
          data: ['变更', '新增'],
          bottom: 0
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '15%',
          top: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: this.assetChangeData.months
        },
        yAxis: {
          type: 'value',
          min: 0,
          max: 500
        },
        series: [
          {
            name: '变更',
            type: 'line',
            smooth: true,
            lineStyle: {
              width: 3,
              color: '#3481FF'
            },
            itemStyle: {
              color: '#3481FF'
            },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: 'rgba(52, 129, 255, 0.3)' },
                { offset: 1, color: 'rgba(52, 129, 255, 0.1)' }
              ])
            },
            data: this.assetChangeData.change
          },
          {
            name: '新增',
            type: 'line',
            smooth: true,
            lineStyle: {
              width: 3,
              color: '#1ABB78'
            },
            itemStyle: {
              color: '#1ABB78'
            },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: 'rgba(26, 187, 120, 0.3)' },
                { offset: 1, color: 'rgba(26, 187, 120, 0.1)' }
              ])
            },
            data: this.assetChangeData.increase
          }
        ]
      }
      
      this.assetChangeChart.setOption(option)
    },
    
    // 初始化数字资产分布情况环状饼图
    initAssetDistributionChart() {
      const chartDom = document.getElementById('assetDistributionChart')
      if (!chartDom) return
      
      this.assetDistributionChart = echarts.init(chartDom)
      
      const totalValue = this.assetDistributionData.reduce((sum, item) => sum + item.value, 0)
      
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          right: '5%',
          top: 'center',
          formatter: function(name) {
            const item = this.assetDistributionData.find(item => item.name === name)
            return `● ${name.padEnd(20, '\u00A0')} ${item.value}`
          }.bind(this)
        },
        series: [
          {
            name: '数字资产分布',
            type: 'pie',
            radius: ['40%', '70%'],
            center: ['30%', '50%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: false
              }
            },
            labelLine: {
              show: false
            },
            data: this.assetDistributionData
          }
        ],
        graphic: {
          type: 'text',
          left: '30%',
          top: '50%',
          style: {
            text: `${totalValue}\n总量`,
            textAlign: 'center',
            fill: '#333',
            fontSize: 16,
            fontWeight: 'bold'
          }
        }
      }
      
      this.assetDistributionChart.setOption(option)
    },
    
    // 初始化数字资产同比增长情况双柱状图
    initAssetGrowthChart() {
      const chartDom = document.getElementById('assetGrowthChart')
      if (!chartDom) return
      
      this.assetGrowthChart = echarts.init(chartDom)
      
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          data: ['本月增长', '同比去年'],
          bottom: 0
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '15%',
          top: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: this.assetGrowthData.categories
        },
        yAxis: {
          type: 'value',
          min: 0,
          max: 100
        },
        series: [
          {
            name: '本月增长',
            type: 'bar',
            barWidth: '30%',
            itemStyle: {
              color: '#50B5FF'
            },
            data: this.assetGrowthData.currentMonth
          },
          {
            name: '同比去年',
            type: 'bar',
            barWidth: '30%',
            itemStyle: {
              color: '#FC7A8C'
            },
            data: this.assetGrowthData.lastYear
          }
        ]
      }
      
      this.assetGrowthChart.setOption(option)
    },
    
    // 处理窗口大小变化
    handleResize() {
      this.assetChangeChart && this.assetChangeChart.resize()
      this.assetDistributionChart && this.assetDistributionChart.resize()
      this.assetGrowthChart && this.assetGrowthChart.resize()
    }
  }
}
</script>

<style lang="scss" scoped>
.asset-change-module {
  margin-top: 20px;
  margin-bottom: 30px;
  
  .chart-card {
    background-color: #fff;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    height: 350px;
    
    .chart-title {
      font-size: 16px;
      font-weight: 500;
      color: #303133;
      margin-bottom: 15px;
      padding-bottom: 10px;
      border-bottom: 1px solid #EBEEF5;
    }
    
    .chart-container {
      height: calc(100% - 40px);
      width: 100%;
    }
  }
}
</style>
