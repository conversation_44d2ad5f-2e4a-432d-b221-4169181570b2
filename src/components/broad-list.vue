<template>
  <div class="broad-list-container" :class="{'horizontal': horizontal}">
    <div class="broad-list" :class="{'horizontal': horizontal}">
      <div
        v-for="(item, index) in items"
        :key="index"
        class="broad-list-item"
        @click="handleItemClick(item, index)"
        :class="{'active': activeIndex === index}"
        :style="{
          background: activeIndex === index ? item.backgroundColor_active : item.backgroundColor
        }"
      >
        <!-- 使用插槽允许自定义内容 -->
        <slot :item="item" :index="index">
          <div class="item-content">
            <div class="item-badge" v-if="item.badge" :class="{'active': activeIndex === index}">
              {{ item.badge }}
            </div>
            <div class="item-title" :class="{'active': activeIndex === index}">
              {{ item.title }}
            </div>
          </div>
        </slot>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'BroadList',
  props: {
    items: {
      type: Array,
      required: true,
      default: () => []
    },
    horizontal: {
      type: Boolean,
      default: false
    },
    // 允许外部控制选中项
    selectedIndex: {
      type: Number,
      default: null
    }
  },
  data() {
    return {
      activeIndex: null
    }
  },
  watch: {
    // 监听外部传入的选中项变化
    selectedIndex(newVal) {
      this.activeIndex = newVal;
    }
  },
  methods: {
    handleItemClick(item, index) {
      this.$emit('item-click', item, index);
    }
  }
}
</script>

<style lang="scss" scoped>
.broad-list-container {
  width: 100%;
  background-color: transparent;
  border-radius: 4px;

  .broad-list {
    display: flex;
    flex-direction: column;
    padding: 0;
    margin: 0;

    // 横向布局
    &.horizontal {
      flex-direction: row;
      flex-wrap: wrap;
      justify-content: space-between;

      &::after {
        content: "";
        flex: 0 0 calc(12.5% - 10px); // 8个卡片时的占位，保证最后一行左对齐
      }

      .broad-list-item {
        flex: 0 0 calc(12.5% - 10px); // 8个卡片，每个占12.5%的宽度，减去间距
        margin: 5px;
        border: none;
        border-radius: 8px;
        overflow: hidden;
        transition: all 0.3s ease;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
    }

    .broad-list-item {
      position: relative;
      padding: 16px 10px;
      cursor: pointer;
      transition: background-color 0.3s ease;

      &.active {
        .item-title {
          color: #ffffff;
        }
      }

      .item-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;

        .item-badge {
          font-size: 24px;
          font-weight: bold;
          margin-bottom: 8px;
          color: inherit;
          transition: color 0.3s ease;

          &.active {
            color: #ffffff;
          }
        }

        .item-title {
          font-size: 14px;
          font-weight: 500;
          color: #303133;
          transition: color 0.3s ease;

          &.active {
            color: #ffffff;
          }
        }
      }
    }
  }
}

// 响应式设计
@media screen and (max-width: 768px) {
  .broad-list-container {
    .broad-list {
      &.horizontal {
        .broad-list-item {
          flex: 0 0 calc(25% - 10px); // 移动端4个卡片一行
        }

        &::after {
          content: "";
          flex: 0 0 calc(25% - 10px); // 移动端占位
        }
      }

      .broad-list-item {
        padding: 12px 8px;

        .item-content {
          .item-badge {
            font-size: 20px;
          }

          .item-title {
            font-size: 12px;
          }
        }
      }
    }
  }
}
</style>
