<template>
  <div class="detail-section electronic-screens-section">
    <h3 class="section-title">
      关联电子屏
      <span class="count-badge">({{ screenData.length }})</span>
    </h3>

    <div class="electronic-screens-table">
      <el-table
        :data="screenData"
        style="width: 100%"
        border
        stripe
        height="450"
      >
        <!-- 固定在左侧的列 -->
        <el-table-column
          type="index"
          label="序号"
          width="60"
          fixed="left"
          align="center"
        ></el-table-column>

        <el-table-column
          prop="name"
          label="电子屏名称"
          width="180"
          fixed="left"
          show-overflow-tooltip
        ></el-table-column>

        <el-table-column
          prop="policeStation"
          label="所属派出所"
          width="150"
          fixed="left"
          show-overflow-tooltip
        ></el-table-column>

        <!-- 中间可滚动的列 -->
        <el-table-column
          prop="contactPerson"
          label="责任单位联系人"
          width="180"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span>{{ scope.row.contactPerson }}-{{ scope.row.contactPhone }}</span>
          </template>
        </el-table-column>

        <el-table-column
          prop="area"
          label="区域/项目"
          width="120"
          align="center"
        >
          <template slot-scope="scope">
            <el-tag
              v-if="scope.row.isKeyArea"
              size="mini"
              style="background-color: #4584FF; color: white; border-color: #4584FF;"
            >
              重点区域
            </el-tag>
            <el-tag
              v-else-if="scope.row.isSpecialProject"
              size="mini"
              style="background-color: #19BB77; color: white; border-color: #19BB77;"
            >
              专项项目
            </el-tag>
            <span v-else>{{ scope.row.area }}</span>
          </template>
        </el-table-column>

        <el-table-column
          prop="location"
          label="安装位置"
          width="200"
          show-overflow-tooltip
        ></el-table-column>

        <el-table-column
          prop="networkSystem"
          label="网络制式"
          width="120"
          align="center"
        ></el-table-column>

        <el-table-column
          prop="inspectionCount"
          label="巡检次数"
          width="100"
          align="center"
        ></el-table-column>

        <el-table-column
          prop="updateTime"
          label="更新时间"
          width="150"
          align="center"
        ></el-table-column>

        <!-- 固定在右侧的操作列 -->
        <el-table-column
          label="操作"
          width="120"
          fixed="right"
          align="center"
        >
          <template slot-scope="scope">
            <el-button
              type="text"
              size="mini"
              @click="handleView(scope.row)"
            >
              查看
            </el-button>
            <el-button
              type="text"
              size="mini"
              @click="handleEdit(scope.row)"
            >
              编辑
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ElectronicScreens',
  props: {
    screenData: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    handleView(row) {
      this.$emit('view-screen', row)
    },

    handleEdit(row) {
      this.$emit('edit-screen', row)
    }
  }
}
</script>

<style lang="scss" scoped>
.electronic-screens-section {
  .section-title {
    display: flex;
    align-items: center;
    margin-top: 0;
    margin-bottom: 20px;
    padding-bottom: 10px;
    font-size: 18px;
    font-weight: 600;
    color: #303133;
    border-bottom: 1px solid #ebeef5;

    .count-badge {
      display: inline-block;
      margin-left: 10px;
      font-size: 14px;
      color: #606266;
      font-weight: normal;
    }
  }

  .electronic-screens-table {
    margin-top: 15px;

    .el-table {
      .el-button {
        padding: 5px 8px;
        font-size: 12px;
      }

      .el-button + .el-button {
        margin-left: 5px;
      }

      // 设置表头背景色
      ::v-deep .el-table__header-wrapper {
        th {
          background-color: #EBF1FF !important;
        }
      }

      // 标签样式优化
      .el-tag {
        margin-bottom: 4px;
      }
    }
  }
}
</style>
