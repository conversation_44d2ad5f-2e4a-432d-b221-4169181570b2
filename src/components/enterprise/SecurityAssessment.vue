<template>
  <div class="detail-section security-assessment-section">
    <h3 class="section-title">企业网络安全评估</h3>
    <el-row :gutter="20" style="background-color: #4584FF;">
      <!-- 左侧：综合评分 -->
      <el-col :span="6">
        <div class="security-score-panel">
          <div class="score-title">企业网络安全综合评分</div>
          <div class="score-value" style="color:#FF8181;">
            {{ securityAssessment.totalScore }}
          </div>
          <div class="score-change">
            <span>较上月</span>
            <i :class="getChangeIcon(securityAssessment.lastMonthChange).icon"
               :style="{ color: getChangeIcon(securityAssessment.lastMonthChange).color }"></i>
            <span :style="{ color: getChangeIcon(securityAssessment.lastMonthChange).color }">
              {{ Math.abs(securityAssessment.lastMonthChange) }}
            </span>
          </div>
          <div class="score-compare">
            高出平均分 <span>{{ securityAssessment.aboveAverage }}</span>
          </div>
          <div class="score-stars">
            <i v-for="n in 5" :key="n" class="el-icon-star-on"
               :style="{ color: n <= securityAssessment.starCount ? '#F7BA2A' : '#EBEEF5' }"></i>
          </div>
          <div class="risk-level" :style="{ color: getSecurityRiskColor(securityAssessment.riskLevel) }">
            {{ securityAssessment.riskLevel }}
          </div>
        </div>
      </el-col>

      <!-- 中间：雷达图 -->
      <el-col :span="10">
        <div class="security-radar-chart">
          <div :id="chartId" style="width: 100%; height: 350px;"></div>
        </div>
      </el-col>

      <!-- 右侧：评估详情 -->
      <el-col :span="8">
        <div class="security-assessment-details">
          <div class="assessment-overview">
            <h4>整体评估</h4>
            <p>{{ enterpriseName }}属于{{ securityAssessment.riskLevel }}公司，各项指标表现良好，特别是资质与合规性方面，表现优异。但在人员与内部安全管理和舆情与社会影响方面需要注意。</p>
          </div>

          <div class="assessment-items">
            <div v-for="(item, index) in securityAssessment.radarData" :key="index" class="assessment-item">
              <div class="item-title">{{ item.name }}：</div>
              <div class="item-content">
                综合评分为<span class="item-score">{{ item.value }}</span>，
                较上月<span :style="{ color: getChangeIcon(item.lastMonthChange).color }">
                  {{ item.lastMonthChange > 0 ? '上升' : '降低' }} {{ Math.abs(item.lastMonthChange) }}
                </span>，
                <span :class="['item-status',
                  item.status === '表现优秀' ? 'status-good' :
                  item.status === '需持续关注' ? 'status-warning' : 'status-danger']">
                  {{ item.status }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'SecurityAssessment',
  props: {
    securityAssessment: {
      type: Object,
      default: () => ({})
    },
    enterpriseName: {
      type: String,
      default: ''
    },
    chartId: {
      type: String,
      default: 'securityRadarChart'
    }
  },
  data() {
    return {
      securityChart: null
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initSecurityRadarChart()
    })
  },
  beforeDestroy() {
    if (this.securityChart) {
      this.securityChart.dispose()
      this.securityChart = null
    }
  },
  methods: {
    // 初始化安全雷达图
    initSecurityRadarChart() {
      const chartDom = document.getElementById(this.chartId)
      if (!chartDom) return

      this.securityChart = echarts.init(chartDom)

      const indicator = this.securityAssessment.radarData?.map(item => ({
        name: item.name,
        max: 5
      })) || []

      const seriesData = [{
        value: this.securityAssessment.radarData?.map(item => item.value) || [],
        name: '安全评估',
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(255, 215, 0, 0.6)' },
            { offset: 1, color: 'rgba(255, 215, 0, 0.1)' }
          ])
        }
      }]

      const option = {
        tooltip: {
          trigger: 'item',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          textStyle: {
            color: '#fff'
          }
        },
        radar: {
          indicator: indicator,
          radius: '65%',
          splitNumber: 5,
          axisName: {
            color: '#fff',
            fontSize: 12,
            fontWeight: 'bold'
          },
          splitArea: {
            areaStyle: {
              color: ['rgba(255, 255, 255, 0.1)', 'rgba(255, 255, 255, 0.05)', 'rgba(255, 255, 255, 0.02)', 'rgba(255, 255, 255, 0.01)'],
              shadowColor: 'rgba(255, 255, 255, 0.1)',
              shadowBlur: 10
            }
          },
          axisLine: {
            lineStyle: {
              color: '#fff',
              width: 2
            }
          },
          splitLine: {
            lineStyle: {
              color: '#fff',
              width: 1,
              opacity: 0.6
            }
          }
        },
        series: [{
          type: 'radar',
          data: seriesData,
          symbol: 'circle',
          symbolSize: 8,
          lineStyle: {
            width: 3,
            color: '#FFD700'
          },
          itemStyle: {
            color: '#FFD700',
            borderColor: '#fff',
            borderWidth: 2
          },
          emphasis: {
            lineStyle: {
              width: 4,
              color: '#FFF700'
            },
            itemStyle: {
              color: '#FFF700',
              borderColor: '#fff',
              borderWidth: 3
            }
          }
        }]
      }

      this.securityChart.setOption(option)
    },

    // 获取变化图标
    getChangeIcon(change) {
      if (change > 0) {
        return { icon: 'el-icon-arrow-up', color: '#67C23A' }
      } else if (change < 0) {
        return { icon: 'el-icon-arrow-down', color: '#F56C6C' }
      } else {
        return { icon: 'el-icon-minus', color: '#909399' }
      }
    },

    // 获取安全风险等级颜色
    getSecurityRiskColor(riskLevel) {
      const colors = {
        '低风险': '#67C23A',
        '中风险': '#E6A23C',
        '高风险': '#F56C6C'
      }
      return colors[riskLevel] || '#909399'
    }
  }
}
</script>

<style lang="scss" scoped>
.security-assessment-section {
  .section-title {
    margin-top: 0;
    margin-bottom: 20px;
    padding-bottom: 10px;
    font-size: 18px;
    font-weight: 600;
    color: #303133;
    border-bottom: 1px solid #ebeef5;
  }

  .security-score-panel {
    border-radius: 4px;
    padding: 20px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    text-align: center;
    height: 350px;
    display: flex;
    flex-direction: column;
    justify-content: center;

    .score-title {
      font-size: 16px;
      color: #fff;
      margin-bottom: 15px;
    }

    .score-value {
      font-size: 48px;
      font-weight: bold;
      margin-bottom: 10px;
    }

    .score-change {
      margin-bottom: 10px;
      font-size: 14px;
      color: #fff;

      i {
        margin: 0 5px;
      }
    }

    .score-compare {
      margin-bottom: 15px;
      font-size: 14px;
      color: #fff;

      span {
        color: #409EFF;
        font-weight: bold;
      }
    }

    .score-stars {
      margin-bottom: 15px;
      font-size: 20px;

      i {
        margin: 0 2px;
      }
    }

    .risk-level {
      font-size: 18px;
      font-weight: bold;
    }
  }

  .security-radar-chart {
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    height: 350px;
  }

  .security-assessment-details {
    border-radius: 4px;
    padding: 20px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    height: 350px;
    overflow-y: auto;

    .assessment-overview {
      margin-bottom: 20px;

      h4 {
        font-size: 16px;
        color: #fff;
        margin-top: 0;
        margin-bottom: 10px;
        font-weight: 500;
      }

      p {
        color: #fff;
        line-height: 1.6;
        margin: 0;
        font-size: 18px;
      }
    }

    .assessment-items {
      .assessment-item {
        margin-bottom: 15px;

        &:last-child {
          margin-bottom: 0;
        }

        .item-title {
          font-weight: 500;
          color: #fff;
          margin-bottom: 5px;
        }

        .item-content {
          color: #fff;
          line-height: 1.6;
          
          .item-score {
            font-weight: bold;
            color: #fff;
            margin: 0 3px;
          }

          .item-status {
            font-weight: 500;
            margin-left: 3px;

            &.status-good {
              color: #67C23A;
            }

            &.status-warning {
              color: #E6A23C;
            }

            &.status-danger {
              color: #F56C6C;
            }
          }
        }
      }
    }
  }
}
</style>
