<template>
  <div class="related-data-lists">
    <!-- 动态渲染各种关联列表 -->
    <div v-for="listConfig in activeListConfigs" :key="listConfig.field" class="list-section">
      <el-divider>
        <i :class="listConfig.icon"></i>
        <span class="divider-title">{{ listConfig.title }}</span>
      </el-divider>
      
      <div class="list-container">
        <el-table
          :data="listData[listConfig.field] || []"
          :loading="loadingStates[listConfig.field]"
          stripe
          border
          style="width: 100%"
          empty-text="暂无数据"
        >
          <el-table-column
            v-for="column in listConfig.columns"
            :key="column.prop"
            :prop="column.prop"
            :label="column.label"
            :width="column.width"
            :min-width="column.minWidth"
            :formatter="column.formatter"
            show-overflow-tooltip
          >
            <template v-if="column.slot" slot-scope="scope">
              <span v-if="column.slot === 'status'" :style="{ color: getStatusColor(scope.row[column.prop]) }">
                {{ scope.row[column.prop] }}
              </span>
              <el-image
                v-else-if="column.slot === 'image'"
                :src="scope.row[column.prop]"
                :preview-src-list="[scope.row[column.prop]]"
                fit="cover"
                style="width: 50px; height: 50px;"
                :hide-on-click-modal="true"
              >
                <div slot="error" class="image-slot">
                  <i class="el-icon-picture-outline"></i>
                </div>
              </el-image>
              <span v-else>{{ scope.row[column.prop] }}</span>
            </template>
          </el-table-column>
        </el-table>
        
        <!-- 分页 -->
        <el-pagination
          v-if="(listData[listConfig.field] || []).length > 0"
          @size-change="(size) => handleSizeChange(listConfig.field, size)"
          @current-change="(page) => handleCurrentChange(listConfig.field, page)"
          :current-page="(pagination[listConfig.field] && pagination[listConfig.field].currentPage) || 1"
          :page-sizes="[10, 20, 50]"
          :page-size="(pagination[listConfig.field] && pagination[listConfig.field].pageSize) || 10"
          layout="total, sizes, prev, pager, next, jumper"
          :total="(pagination[listConfig.field] && pagination[listConfig.field].total) || 0"
          style="margin-top: 20px; text-align: center;"
        />
      </div>
    </div>
  </div>
</template>

<script>
import {
  getScreenList,
  getNetbarList,
  getLevelProtectList,
  getWebsiteList,
  getAppList,
  getAppletList,
  getOperatorList,
  getWifiSiteList
} from '@/api/enterprise'

export default {
  name: 'RelatedDataLists',
  props: {
    enterpriseData: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      listData: {},
      loadingStates: {},
      pagination: {},
      
      // 列表配置映射
      listConfigs: {
        screenNum: {
          field: 'screenNum',
          title: '关联电子屏列表',
          icon: 'el-icon-monitor',
          apiMethod: getScreenList,
          paramKey: 'credit_code',
          columns: [
            { prop: 'screenName', label: '电子屏名称/位置', minWidth: 150 },
            { prop: 'policeStation', label: '所属派出所', width: 120 },
            { prop: 'photo', label: '照片', width: 80, slot: 'image' },
            { prop: 'screenSize', label: '屏幕尺寸/类型', width: 120 },
            { prop: 'responsibleUnit', label: '责任单位', width: 120 },
            { prop: 'responsiblePerson', label: '责任单位联系人', width: 140 },
            { prop: 'keyArea', label: '重点区域', width: 100 },
            { prop: 'specialProject', label: '专项', width: 100 },
            { prop: 'belongingPlace', label: '归属场所', width: 120 },
            { prop: 'networkSystem', label: '连网系统', width: 120 },
            { prop: 'inspectionCount', label: '近三月检查数', width: 120 }
          ]
        },
        netbarNum: {
          field: 'netbarNum',
          title: '网吧列表',
          icon: 'el-icon-monitor',
          apiMethod: getNetbarList,
          paramKey: 'credit_code',
          columns: [
            { prop: 'creditCode', label: '统一社会信用代码', width: 180 },
            { prop: 'siteCode', label: '场所编号', width: 120 },
            { prop: 'siteName', label: '场所名称', minWidth: 150 },
            { prop: 'businessStatus', label: '营业状态', width: 100, slot: 'status' },
            { prop: 'area', label: '所属区域', width: 100 },
            { prop: 'policeStation', label: '所属派出所', width: 120 },
            { prop: 'contactPhone', label: '联系电话', width: 120 },
            { prop: 'address', label: '地址', minWidth: 200 }
          ]
        },
        levelprotectNum: {
          field: 'levelprotectNum',
          title: '等保备案列表',
          icon: 'el-icon-lock',
          apiMethod: getLevelProtectList,
          paramKey: 'credit_code',
          columns: [
            { prop: 'networkName', label: '网络名称', minWidth: 150 },
            { prop: 'unitName', label: '单位名称', minWidth: 150 },
            { prop: 'contactName', label: '联系人姓名', width: 120 },
            { prop: 'mobilePhone', label: '移动电话', width: 120 },
            { prop: 'recordNumber', label: '备案证明编号', width: 150 },
            { prop: 'protectionLevel', label: '保护等级', width: 100 },
            { prop: 'recordDate', label: '备案日期', width: 120 },
            { prop: 'businessType', label: '业务类型', width: 120 },
            { prop: 'dataSource', label: '数据来源', width: 120 },
            { prop: 'expertReview', label: '专家评审情况', width: 140 },
            { prop: 'status', label: '状态', width: 100, slot: 'status' }
          ]
        },
        websiteNum: {
          field: 'websiteNum',
          title: '网站备案列表',
          icon: 'el-icon-link',
          apiMethod: getWebsiteList,
          paramKey: 'credit_code',
          columns: [
            { prop: 'networkName', label: '网络名称', minWidth: 150 },
            { prop: 'unitName', label: '单位名称', minWidth: 150 },
            { prop: 'contactName', label: '联系人姓名', width: 120 },
            { prop: 'mobilePhone', label: '移动电话', width: 120 },
            { prop: 'recordNumber', label: '备案证明编号', width: 150 },
            { prop: 'protectionLevel', label: '保护等级', width: 100 },
            { prop: 'recordDate', label: '备案日期', width: 120 },
            { prop: 'businessType', label: '业务类型', width: 120 },
            { prop: 'dataSource', label: '数据来源', width: 120 },
            { prop: 'expertReview', label: '专家评审情况', width: 140 },
            { prop: 'status', label: '状态', width: 100, slot: 'status' }
          ]
        },
        appNum: {
          field: 'appNum',
          title: 'APP列表',
          icon: 'el-icon-mobile-phone',
          apiMethod: getAppList,
          paramKey: 'credit_code',
          columns: [
            { prop: 'registrationNumber', label: '注册登记号', width: 150 },
            { prop: 'appName', label: 'APP名称', minWidth: 150 },
            { prop: 'creditCode', label: '统一社会信用代码', width: 180 },
            { prop: 'createTime', label: '创建时间', width: 120 },
            { prop: 'logo', label: 'LOGO', width: 80, slot: 'image' },
            { prop: 'platform', label: '运行平台', width: 100 },
            { prop: 'marketValue', label: '市值', width: 100 },
            { prop: 'monthlyActiveUsers', label: '月活用户数量', width: 120 },
            { prop: 'totalUsers', label: '注册用户总量', width: 120 },
            { prop: 'updateTime', label: '更新时间', width: 120 },
            { prop: 'appLevel', label: 'APP分级', width: 100 },
            { prop: 'securityManagerName', label: '安全负责人姓名', width: 140 },
            { prop: 'securityManagerPhone', label: '安全负责人手机号', width: 150 }
          ]
        },
        appletNum: {
          field: 'appletNum',
          title: '小程序列表',
          icon: 'el-icon-mobile',
          apiMethod: getAppletList,
          paramKey: 'credit_code',
          columns: [
            { prop: 'registrationNumber', label: '注册登记号', width: 150 },
            { prop: 'appletName', label: '小程序名称', minWidth: 150 },
            { prop: 'creditCode', label: '统一社会信用代码', width: 180 },
            { prop: 'createTime', label: '创建时间', width: 120 },
            { prop: 'ipAddress', label: 'IP地址', width: 120 },
            { prop: 'miitRecord', label: '工信部备案', width: 120 },
            { prop: 'serverInfo', label: '托管服务器信息', width: 150 },
            { prop: 'monthlyActiveUsers', label: '月活用户数量', width: 120 },
            { prop: 'totalUsers', label: '注册用户总量', width: 120 },
            { prop: 'appletLevel', label: '小程序分级', width: 120 },
            { prop: 'securityManagerName', label: '安全负责人姓名', width: 140 },
            { prop: 'securityManagerPhone', label: '安全负责人手机号', width: 150 },
            { prop: 'emergencyContactName', label: '应急联络人姓名', width: 140 },
            { prop: 'status', label: '状态', width: 100, slot: 'status' }
          ]
        },
        operatorNum: {
          field: 'operatorNum',
          title: '运营商管理列表',
          icon: 'el-icon-connection',
          apiMethod: getOperatorList,
          paramKey: 'credit_code',
          columns: [
            { prop: 'creditCode', label: '统一社会信用代码', width: 180 },
            { prop: 'serviceType', label: '提供服务类型', width: 120 },
            { prop: 'infoSecurityManager', label: '信息安全负责人', width: 140 },
            { prop: 'infoSecurityPhone', label: '信息安全手机号码', width: 150 },
            { prop: 'networkSecurityManager', label: '网络安全负责人', width: 140 },
            { prop: 'networkSecurityPhone', label: '网络安全手机号码', width: 150 },
            { prop: 'networkWorkManager', label: '网络安全工作负责人', width: 160 },
            { prop: 'networkWorkPhone', label: '网络安全手机号码', width: 150 },
            { prop: 'dutyPhone', label: '值班电话', width: 120 }
          ]
        },
        nonbusinessNum: {
          field: 'nonbusinessNum',
          title: '非经营场所列表',
          icon: 'el-icon-office-building',
          apiMethod: getWifiSiteList,
          paramKey: 'org_code',
          columns: [
            { prop: 'siteCode', label: '场所编码', width: 120 },
            { prop: 'siteName', label: '场所名称', minWidth: 150 },
            { prop: 'socialCode', label: '社采编码', width: 120 },
            { prop: 'mobilePhone', label: '移动电话', width: 120 },
            { prop: 'legalPersonName', label: '法人姓名', width: 120 },
            { prop: 'legalPersonPhone', label: '法人电话', width: 120 },
            { prop: 'siteNature', label: '场所性质', width: 120 },
            { prop: 'province', label: '所属省', width: 100 },
            { prop: 'city', label: '所属城市', width: 100 },
            { prop: 'district', label: '所属分区', width: 100 },
            { prop: 'policeStation', label: '派出所', width: 120 },
            { prop: 'businessStartTime', label: '营业开始时间', width: 140 },
            { prop: 'businessEndTime', label: '营业结束时间', width: 140 }
          ]
        }
      }
    }
  },
  computed: {
    // 根据企业数据中的标签字段，筛选出需要显示的列表配置
    activeListConfigs() {
      const configs = []
      Object.keys(this.listConfigs).forEach(field => {
        const value = this.enterpriseData[field]
        if (value && value > 0) {
          configs.push(this.listConfigs[field])
        }
      })
      return configs
    }
  },
  watch: {
    enterpriseData: {
      handler(newData) {
        if (newData && newData.creditCode) {
          this.initializeData()
        }
      },
      immediate: true
    }
  },
  methods: {
    // 初始化数据
    initializeData() {
      this.activeListConfigs.forEach(config => {
        this.initializePagination(config.field)
        this.fetchListData(config)
      })
    },

    // 初始化分页数据
    initializePagination(field) {
      this.$set(this.pagination, field, {
        currentPage: 1,
        pageSize: 10,
        total: 0
      })
      this.$set(this.loadingStates, field, false)
      this.$set(this.listData, field, [])
    },

    // 获取列表数据
    async fetchListData(config) {
      const { field, apiMethod, paramKey } = config
      
      this.$set(this.loadingStates, field, true)
      
      try {
        const params = {
          pageNum: this.pagination[field].currentPage,
          pageSize: this.pagination[field].pageSize
        }

        // 根据不同的参数键设置请求参数
        if (paramKey === 'org_code') {
          params[paramKey] = this.enterpriseData.creditCode
        } else {
          params[paramKey] = this.enterpriseData.creditCode
        }
        
        const response = await apiMethod(params)
        
        if (response.code === 200) {
          this.$set(this.listData, field, response.rows || response.data || [])
          if (this.pagination[field]) {
            this.$set(this.pagination[field], 'total', response.total || 0)
          }
        } else {
          this.$message.error(`获取${config.title}失败: ${response.msg || '未知错误'}`)
          this.$set(this.listData, field, [])
        }
      } catch (error) {
        console.error(`获取${config.title}失败:`, error)
        this.$message.error(`获取${config.title}失败，请稍后重试`)
        this.$set(this.listData, field, [])
      } finally {
        this.$set(this.loadingStates, field, false)
      }
    },

    // 处理页码变化
    handleCurrentChange(field, page) {
      if (this.pagination[field]) {
        this.$set(this.pagination[field], 'currentPage', page)
        const config = this.listConfigs[field]
        this.fetchListData(config)
      }
    },

    // 处理每页条数变化
    handleSizeChange(field, size) {
      if (this.pagination[field]) {
        this.$set(this.pagination[field], 'pageSize', size)
        this.$set(this.pagination[field], 'currentPage', 1)
        const config = this.listConfigs[field]
        this.fetchListData(config)
      }
    },

    // 获取状态颜色
    getStatusColor(status) {
      const statusColors = {
        '正常': '#67C23A',
        '营业': '#67C23A',
        '有效': '#67C23A',
        '通过': '#67C23A',
        '异常': '#F56C6C',
        '停业': '#F56C6C',
        '无效': '#F56C6C',
        '未通过': '#F56C6C',
        '待审核': '#E6A23C',
        '审核中': '#E6A23C'
      }
      return statusColors[status] || '#909399'
    }
  }
}
</script>

<style lang="scss" scoped>
.related-data-lists {
  .list-section {
    margin-bottom: 30px;
    
    .divider-title {
      font-size: 16px;
      font-weight: 600;
      margin-left: 8px;
    }
    
    .list-container {
      margin-top: 20px;
      
      .el-table {
        border-radius: 4px;
        overflow: hidden;
      }
    }
  }
  
  .image-slot {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    background: #f5f7fa;
    color: #909399;
    font-size: 20px;
  }
}
</style>
