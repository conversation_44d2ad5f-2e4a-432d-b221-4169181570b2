<template>
  <div class="detail-section compliance-records-section">
    <h3 class="section-title">历史合规记录</h3>
    <el-row :gutter="20">
      <el-col :span="8" v-for="record in complianceRecords" :key="record.id">
        <div class="compliance-card">
          <div class="card-content">
            <div class="card-left">
              <div class="record-type">{{ record.type }}</div>
              <div class="record-stats">
                <span class="total-count">{{ record.totalCount }}</span>
                <el-tag size="mini" type="info" class="recent-period">{{ record.recentPeriod }}</el-tag>
              </div>
            </div>
            <div class="card-right">
              <!-- 第一个卡片：立即现场检查记录 -->
              <template v-if="record.id === 1">
                <div class="check-time">检查时间：{{ record.lastCheckTime }}</div>
                <div class="problem-stats">
                  <span class="problem-count">问题数量：{{ record.problemCount }}</span>
                  <div class="resolution-status">
                    <span class="status-label">整改情况：</span>
                    <el-tag v-if="record.unresolved > 0" size="mini" type="danger" class="status-tag">
                      未整改 {{ record.unresolved }}
                    </el-tag>
                    <el-tag v-if="record.resolved > 0" size="mini" type="success" class="status-tag">
                      已整改 {{ record.resolved }}
                    </el-tag>
                  </div>
                </div>
              </template>

              <!-- 第二个和第三个卡片：行政处罚记录和行政处置 -->
              <template v-else>
                <div class="action-time">{{ record.timeLabel }}：{{ record.lastTime }}</div>
                <div class="action-details">
                  <div class="action-reason">{{ record.reasonLabel }}：{{ record.reason }}</div>
                  <div class="action-result">{{ record.resultLabel }}：{{ record.result }}</div>
                </div>
              </template>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 历史合规记录详细列表 -->
    <div class="compliance-detail-list">
      <el-table
        :data="complianceDetailList"
        style="width: 100%"
        border
        :header-cell-style="{ background: '#EBF1FF', color: '#303133' }"
        :row-style="{ height: 'auto' }"
      >
        <el-table-column
          prop="decisionNumber"
          label="行政处罚决定书编号"
          width="200"
          align="center"
        >
        </el-table-column>
        <el-table-column
          prop="decisionDate"
          label="处罚决定时间"
          width="150"
          align="center"
        >
        </el-table-column>
        <el-table-column
          prop="caseDescription"
          label="简要案情"
          min-width="400"
        >
          <template slot-scope="scope">
            <div class="case-description">
              {{ scope.row.caseDescription }}
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ComplianceRecords',
  props: {
    complianceRecords: {
      type: Array,
      default: () => []
    },
    complianceDetailList: {
      type: Array,
      default: () => []
    }
  }
}
</script>

<style lang="scss" scoped>
.compliance-records-section {
  .section-title {
    margin-top: 0;
    margin-bottom: 20px;
    padding-bottom: 10px;
    font-size: 18px;
    font-weight: 600;
    color: #303133;
    border-bottom: 1px solid #ebeef5;
  }

  .compliance-card {
    height: 100%;
    background-color: #fff;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    border: 1px solid #ebeef5;

    .card-content {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      height: 100%;

      .card-left {
        flex: 1;
        margin-right: 20px;

        .record-type {
          font-size: 16px;
          font-weight: 500;
          color: #303133;
          margin-bottom: 15px;
          line-height: 1.4;
        }

        .record-stats {
          display: flex;
          align-items: center;
          justify-content: space-between;

          .total-count {
            font-size: 28px;
            font-weight: bold;
            color: #409EFF;
          }

          .recent-period {
            background-color: #f0f9ff;
            color: #409EFF;
            border-color: #409EFF;
          }
        }
      }

      .card-right {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        height: 100%;

        .check-time {
          font-size: 14px;
          color: #606266;
          margin-bottom: 15px;
        }

        .problem-stats {
          .problem-count {
            font-size: 14px;
            color: #303133;
            margin-bottom: 10px;
            display: block;
          }

          .resolution-status {
            display: flex;
            align-items: center;
            flex-wrap: wrap;

            .status-label {
              font-size: 14px;
              color: #606266;
              margin-right: 8px;
              white-space: nowrap;
            }

            .status-tag {
              margin-right: 5px;
              margin-bottom: 5px;

              &.el-tag--danger {
                background-color: #fef0f0;
                border-color: #fbc4c4;
                color: #f56c6c;
              }

              &.el-tag--success {
                background-color: #f0f9ff;
                border-color: #b3d8ff;
                color: #67c23a;
              }
            }
          }
        }

        // 行政处罚和行政处置样式
        .action-time {
          font-size: 14px;
          color: #606266;
          margin-bottom: 15px;
        }

        .action-details {
          .action-reason {
            font-size: 14px;
            color: #303133;
            margin-bottom: 8px;
            line-height: 1.4;
          }

          .action-result {
            font-size: 14px;
            color: #E6A23C;
            font-weight: 500;
            line-height: 1.4;
          }
        }
      }
    }

    &:hover {
      box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.1);
      transform: translateY(-2px);
      transition: all 0.3s ease;
    }
  }

  // 历史合规记录详细列表样式
  .compliance-detail-list {
    margin-top: 30px;

    .el-table {
      border-radius: 8px;
      overflow: hidden;

      .el-table__header-wrapper {
        .el-table__header {
          th {
            background-color: #EBF1FF !important;
            color: #303133 !important;
            font-weight: 600;
            font-size: 14px;
            padding: 15px 0;
          }
        }
      }

      .el-table__body-wrapper {
        .el-table__body {
          tr {
            &:hover {
              background-color: #f5f7fa;
            }

            td {
              padding: 20px 12px;
              border-bottom: 1px solid #ebeef5;

              .case-description {
                line-height: 1.6;
                color: #606266;
                font-size: 14px;
                text-align: justify;
                word-break: break-word;
                white-space: pre-wrap;
              }
            }
          }
        }
      }

      // 表格边框样式
      .el-table--border {
        border: 1px solid #ebeef5;

        &::after {
          background-color: #ebeef5;
        }

        &::before {
          background-color: #ebeef5;
        }
      }

      // 表格单元格边框
      .el-table td, .el-table th {
        border-right: 1px solid #ebeef5;
      }

      // 最后一列不显示右边框
      .el-table td:last-child, .el-table th:last-child {
        border-right: none;
      }
    }
  }
}
</style>
