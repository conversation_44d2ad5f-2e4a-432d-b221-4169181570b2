<template>
  <div class="detail-section enterprise-basic-section">
    <div class="section-header" @click="toggleExpanded">
      <h3 class="section-title">企业基础信息</h3>
      <div class="section-actions">
        <span class="count-badge">3</span>
        <i :class="expanded ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
      </div>
    </div>

    <div class="section-content" v-show="expanded">
      <el-row :gutter="20">
        <el-col :span="8">
          <div class="info-item">
            <span class="label">成立日期：</span>
            <span class="value">{{ enterpriseData.establishDate || '2015-06-15' }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <span class="label">注册资本：</span>
            <span class="value">{{ enterpriseData.registeredCapital || '1000万元' }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <span class="label">所属区域：</span>
            <span class="value">{{ enterpriseData.region }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <span class="label">负责人：</span>
            <span class="value">{{ enterpriseData.manager }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <span class="label">负责人电话：</span>
            <span class="value">{{ enterpriseData.managerPhone }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <span class="label">企业状态：</span>
            <span class="value" :style="{ color: getStateColor(enterpriseData.state) }">{{ enterpriseData.status }}</span>
          </div>
        </el-col>
        <el-col :span="24">
          <div class="info-item">
            <span class="label">经营范围：</span>
            <span class="value">{{ enterpriseData.businessScope || '计算机软硬件技术开发、技术转让、技术咨询、技术服务；数据处理；应用软件服务；销售计算机软硬件及辅助设备、电子产品、通讯设备。' }}</span>
          </div>
        </el-col>
      </el-row>

      <!-- 企业法人信息子模块 -->
      <person-info-section
        title="企业法人信息"
        :person-data="legalPerson"
        :expanded="legalPersonExpanded"
        @toggle="toggleLegalPerson"
      />

      <!-- 企业责任人信息子模块 -->
      <person-info-section
        title="企业责任人信息"
        :person-data="responsiblePerson"
        :expanded="responsiblePersonExpanded"
        :show-extra-fields="true"
        @toggle="toggleResponsiblePerson"
      />
    </div>
  </div>
</template>

<script>
import PersonInfoSection from './PersonInfoSection.vue'

export default {
  name: 'EnterpriseBasicInfo',
  components: {
    PersonInfoSection
  },
  props: {
    enterpriseData: {
      type: Object,
      default: () => ({})
    },
    legalPerson: {
      type: Object,
      default: () => ({})
    },
    responsiblePerson: {
      type: Object,
      default: () => ({})
    },
    expanded: {
      type: Boolean,
      default: true
    },
    legalPersonExpanded: {
      type: Boolean,
      default: true
    },
    responsiblePersonExpanded: {
      type: Boolean,
      default: true
    }
  },
  methods: {
    toggleExpanded() {
      this.$emit('toggle-expanded')
    },

    toggleLegalPerson() {
      this.$emit('toggle-legal-person')
    },

    toggleResponsiblePerson() {
      this.$emit('toggle-responsible-person')
    },

    // 获取状态颜色
    getStateColor(state) {
      const stateColors = {
        '存续': '#4584FF',
        '在业': '#67C23A',
        '吊销': '#F56C6C',
        '注销': '#909399',
        '迁出': '#E6A23C'
      }
      return stateColors[state] || '#909399'
    }
  }
}
</script>

<style lang="scss" scoped>
.detail-section {
  margin-bottom: 30px;

  &.enterprise-basic-section {
    border: 1px solid #ebeef5;
    border-radius: 4px;
    overflow: hidden;

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 15px 20px;
      background-color: #f5f7fa;
      cursor: pointer;
      border-bottom: none;

      &:hover {
        background-color: #eef1f6;
      }

      .section-title {
        margin: 0;
        padding: 0;
        border: none;
        font-size: 18px;
        font-weight: 600;
      }

      .section-actions {
        display: flex;
        align-items: center;

        .count-badge {
          display: inline-block;
          padding: 2px 8px;
          margin-right: 10px;
          background-color: #409EFF;
          color: #fff;
          border-radius: 10px;
          font-size: 12px;
        }

        i {
          font-size: 16px;
          color: #909399;
        }
      }
    }

    .section-content {
      padding: 20px;
      background-color: #fff;
    }
  }

  .info-item {
    margin-bottom: 15px;

    .label {
      color: #909399;
      margin-right: 5px;
    }

    .value {
      color: #303133;
      word-break: break-all;
    }
  }
}
</style>
