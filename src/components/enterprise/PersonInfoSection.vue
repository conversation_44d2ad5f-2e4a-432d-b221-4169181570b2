<template>
  <div class="subsection">
    <div class="subsection-header" @click="toggleExpanded">
      <h4 class="subsection-title">{{ title }}</h4>
      <div class="subsection-actions">
        <span class="count-badge">1</span>
        <i :class="expanded ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
      </div>
    </div>

    <div class="subsection-content" v-show="expanded">
      <div class="person-item">
        <el-row :gutter="20" class="person-info">
          <el-col :span="8">
            <div class="info-item">
              <span class="label">{{ title.includes('法人') ? '法人姓名' : '负责人' }}：</span>
              <span class="value">{{ personData.name }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="label">{{ title.includes('法人') ? '法人联系电话' : '负责人联系电话' }}：</span>
              <span class="value">{{ personData.phone }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="label">证件类型：</span>
              <span class="value">{{ personData.idType }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="label">证件号码：</span>
              <span class="value">{{ personData.idNumber }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <span class="label">证件有效期：</span>
              <span class="value">{{ personData.expiryDate }}</span>
            </div>
          </el-col>
          
          <!-- 责任人额外字段 -->
          <template v-if="showExtraFields">
            <el-col :span="8">
              <div class="info-item">
                <span class="label">办公室电话：</span>
                <span class="value">{{ personData.officePhone }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">电子邮件：</span>
                <span class="value">{{ personData.email }}</span>
              </div>
            </el-col>
            <el-col :span="24">
              <div class="info-item">
                <span class="label">常驻地址：</span>
                <span class="value">{{ personData.address }}</span>
              </div>
            </el-col>
          </template>
        </el-row>

        <div class="person-photos">
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="photo-item">
                <div class="photo-title">法人证件(人像)</div>
                <div class="photo-container">
                  <img v-if="personData.portraitPhoto" :src="personData.portraitPhoto" alt="法人证件(人像)" />
                  <i v-else class="el-icon-picture-outline"></i>
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="photo-item">
                <div class="photo-title">法人证件照(国徽)</div>
                <div class="photo-container">
                  <img v-if="personData.nationalEmblemPhoto" :src="personData.nationalEmblemPhoto" alt="法人证件照(国徽)" />
                  <i v-else class="el-icon-picture-outline"></i>
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="photo-item">
                <div class="photo-title">法人证件(手持)</div>
                <div class="photo-container">
                  <img v-if="personData.holdingPhoto" :src="personData.holdingPhoto" alt="法人证件(手持)" />
                  <i v-else class="el-icon-picture-outline"></i>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PersonInfoSection',
  props: {
    title: {
      type: String,
      required: true
    },
    personData: {
      type: Object,
      default: () => ({})
    },
    expanded: {
      type: Boolean,
      default: true
    },
    showExtraFields: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    toggleExpanded() {
      this.$emit('toggle')
    }
  }
}
</script>

<style lang="scss" scoped>
.subsection {
  margin-top: 25px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: hidden;

  .subsection-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    background-color: #f5f7fa;
    cursor: pointer;

    &:hover {
      background-color: #eef1f6;
    }

    .subsection-title {
      margin: 0;
      font-size: 16px;
      font-weight: 500;
    }

    .subsection-actions {
      display: flex;
      align-items: center;

      .count-badge {
        display: inline-block;
        padding: 2px 8px;
        margin-right: 10px;
        background-color: #409EFF;
        color: #fff;
        border-radius: 10px;
        font-size: 12px;
      }

      i {
        font-size: 16px;
        color: #909399;
      }
    }
  }

  .subsection-content {
    padding: 15px;
    background-color: #fff;

    .person-item {
      margin-bottom: 15px;

      &:last-child {
        margin-bottom: 0;
      }

      .person-info {
        margin-bottom: 15px;
      }

      .person-photos {
        margin-top: 20px;

        .photo-item {
          height: 100%;
          display: flex;
          flex-direction: column;
          background-color: #fff;
          border: 1px solid #ebeef5;
          border-radius: 4px;
          overflow: hidden;

          .photo-title {
            padding: 8px;
            text-align: center;
            font-weight: 500;
            background-color: #f5f7fa;
            border-bottom: 1px solid #ebeef5;
          }

          .photo-container {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            width: 100%;

            // 设置16:9的宽高比
            &:before {
              content: "";
              display: block;
              padding-top: 56.25%; // 16:9 = 9/16 = 0.5625 = 56.25%
            }

            img {
              position: absolute;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
              object-fit: contain;
              padding: 10px;
            }

            i {
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
              font-size: 60px;
              color: #909399;
            }
          }
        }
      }
    }
  }

  .info-item {
    margin-bottom: 15px;

    .label {
      color: #909399;
      margin-right: 5px;
    }

    .value {
      color: #303133;
      word-break: break-all;
    }
  }
}
</style>
