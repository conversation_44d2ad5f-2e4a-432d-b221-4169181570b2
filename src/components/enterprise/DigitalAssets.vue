<template>
  <div class="detail-section digital-asset-section">
    <h3 class="section-title">当前数字资产清单</h3>
    <div class="asset-cards-container">
      <div
        v-for="(asset, index) in digitalAssetList"
        :key="index"
        class="asset-card"
        :style="{
          backgroundColor: asset.color,
          borderColor: asset.borderColor,
          color: asset.textColor
        }"
      >
        <div class="asset-count">{{ asset.count }}</div>
        <div class="asset-name">{{ asset.name }}</div>
      </div>
    </div>
    <div class="asset-cards-detail">
      <ul>
        <li class="detail-item">
          <h5 class="cards-detail-tit">IP地址段（2）</h5>
          <div class="carousel-wrapper">
            <ul class="carousel-container" ref="ipCarousel">
              <li>
                <div class="label-fields-container">
                  <div class="label-field">
                    <label class="left-lable">起始IP：</label>
                    <span>***********</span>
                  </div>
                  <div class="label-field">
                    <label class="left-lable">终止IP：</label>
                    <span>***********98</span>
                  </div>
                  <div class="label-field">
                    <label class="left-lable">所属网络：</label>
                    <span>中国电信</span>
                  </div>
                  <div class="label-field">
                    <label class="left-lable">更新时间：</label>
                    <span>2024.4.28 12:42:36</span>
                  </div>
                </div>
              </li>
              <li>
                <div class="label-fields-container">
                  <div class="label-field">
                    <label class="left-lable">起始IP：</label>
                    <span>***********</span>
                  </div>
                  <div class="label-field">
                    <label class="left-lable">终止IP：</label>
                    <span>*************</span>
                  </div>
                  <div class="label-field">
                    <label class="left-lable">所属网络：</label>
                    <span>中国联通</span>
                  </div>
                  <div class="label-field">
                    <label class="left-lable">更新时间：</label>
                    <span>2024.4.28 19:55:22</span>
                  </div>
                </div>
              </li>
            </ul>
            <div class="carousel-indicators">
              <span
                v-for="(item, index) in 2"
                :key="index"
                :class="['indicator', { active: currentSlide === index }]"
                @click="goToSlide(index)"
              ></span>
            </div>
          </div>
        </li>
        <li class="detail-item">
          <h5 class="cards-detail-tit">IDC机房（0）</h5>
          <div class="carousel-wrapper">
            <ul class="carousel-container">
              <li>
                <div class="label-fields-container">
                  <div class="label-field">
                    <label class="left-lable">机房名称：</label>
                    <span>——</span>
                  </div>
                  <div class="label-field">
                    <label class="left-lable">机房负责人：</label>
                    <span>——</span>
                  </div>
                  <div class="label-field">
                    <label class="left-lable">机房地址：</label>
                    <span>——</span>
                  </div>
                  <div class="label-field">
                    <label class="left-lable">所属辖区：</label>
                    <span>——</span>
                  </div>
                  <div class="label-field">
                    <label class="left-lable">机房类型：</label>
                    <span>——</span>
                  </div>
                  <div class="label-field">
                    <label class="left-lable">服务器数量：</label>
                    <span>——</span>
                  </div>
                  <div class="label-field">
                    <label class="left-lable">机架数量：</label>
                    <span>——</span>
                  </div>
                  <div class="label-field">
                    <label class="left-lable">宽带总量：</label>
                    <span>——</span>
                  </div>
                  <div class="label-field">
                    <label class="left-lable">更新时间：</label>
                    <span>——</span>
                  </div>
                </div>
              </li>
            </ul>
          </div>
        </li>
        <li class="detail-item">
          <h5 class="cards-detail-tit">宽带用户总数（0）</h5>
          <div class="carousel-wrapper">
            <ul class="carousel-container">
              <li>
                <div class="label-fields-container">
                  <div class="label-field">
                    <label class="left-lable">开户人姓名：</label>
                    <span>——</span>
                  </div>
                  <div class="label-field">
                    <label class="left-lable">装机电话：</label>
                    <span>——</span>
                  </div>
                  <div class="label-field">
                    <label class="left-lable">装机地点：</label>
                    <span>——</span>
                  </div>
                  <div class="label-field">
                    <label class="left-lable">用户账号：</label>
                    <span>——</span>
                  </div>
                  <div class="label-field">
                    <label class="left-lable">更新时间：</label>
                    <span>——</span>
                  </div>
                </div>
              </li>
            </ul>
          </div>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DigitalAssets',
  props: {
    digitalAssetList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      currentSlide: 0,
      autoSlideTimer: null
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initCarouselContainers()
      this.startAutoSlide()
    })
  },
  beforeDestroy() {
    if (this.autoSlideTimer) {
      clearInterval(this.autoSlideTimer)
      this.autoSlideTimer = null
    }
  },
  methods: {
    // 初始化轮播容器
    initCarouselContainers() {
      const carousel = this.$refs.ipCarousel
      if (carousel && carousel.children.length > 1) {
        carousel.classList.add('carousel-active')
      }
    },

    // 开始自动轮播
    startAutoSlide() {
      this.autoSlideTimer = setInterval(() => {
        this.nextSlide()
      }, 3000)
    },

    // 下一张幻灯片
    nextSlide() {
      this.currentSlide = (this.currentSlide + 1) % 2
      this.updateSlidePosition()
    },

    // 跳转到指定幻灯片
    goToSlide(index) {
      this.currentSlide = index
      this.updateSlidePosition()
    },

    // 更新幻灯片位置
    updateSlidePosition() {
      this.$nextTick(() => {
        const carousel = this.$refs.ipCarousel
        if (carousel) {
          const translateX = -this.currentSlide * 50 // 每次移动50%，因为每个li占50%宽度
          carousel.style.transform = `translateX(${translateX}%)`
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.digital-asset-section {
  .section-title {
    margin-top: 0;
    margin-bottom: 20px;
    padding-bottom: 10px;
    font-size: 18px;
    font-weight: 600;
    color: #303133;
    border-bottom: 1px solid #ebeef5;
  }

  .asset-cards-container {
    display: grid;
    grid-template-columns: repeat(9, 1fr);
    gap: 15px;
    margin-top: 20px;

    .asset-card {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      padding: 20px 15px;
      border: 1px solid;
      box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
      transition: all 0.3s ease;
      min-height: 100px;
      position: relative;
      overflow: hidden;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 3px 12px rgba(0, 0, 0, 0.1);
      }

      .asset-name {
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 10px;
        text-align: center;
        line-height: 1.3;
      }

      .asset-count {
        font-size: 24px;
        font-weight: bold;
        text-align: center;
        line-height: 1;
      }

      // 添加微妙的渐变效果
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0) 50%);
        pointer-events: none;
      }
    }
  }

  // asset-cards-detail 容器样式
  .asset-cards-detail {
    margin-top: 30px;

    > ul {
      display: flex;
      gap: 20px;
      list-style: none;
      padding: 0;
      margin: 0;

      .detail-item {
        flex: 1;
        background-color: #F6F7FB;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);

        .cards-detail-tit {
          margin: 0 0 15px 0;
          font-size: 16px;
          font-weight: 600;
          color: #303133;
          border-bottom: 1px solid #E4E7ED;
          padding-bottom: 10px;
        }

        .carousel-wrapper {
          position: relative;
          overflow: hidden;

          .carousel-container {
            list-style: none;
            padding: 0;
            margin: 0;
            position: relative;

            // 默认情况下，非轮播模式
            &:not(.carousel-active) {
              li {
                .label-fields-container {
                  display: grid;
                  grid-template-columns: 1fr 1fr;
                  gap: 15px 20px;

                  .label-field {
                    display: flex;
                    align-items: center;
                    font-size: 14px;
                    margin-bottom: 0;

                    .left-lable {
                      min-width: 80px;
                      color: #606266;
                      font-weight: 500;
                      margin-right: 8px;
                      white-space: nowrap;
                    }

                    span {
                      color: #303133;
                      flex: 1;
                    }
                  }
                }
              }
            }

            // 轮播模式样式
            &.carousel-active {
              display: flex !important;
              transition: transform 0.5s ease-in-out;
              height: auto;
              min-height: 120px;
              width: 200%;

              li {
                min-width: 50% !important;
                flex-shrink: 0;
                box-sizing: border-box;
                display: block !important;
                visibility: visible !important;
                opacity: 1 !important;

                .label-fields-container {
                  display: grid !important;
                  grid-template-columns: 1fr 1fr;
                  gap: 15px 20px;
                  width: 100%;
                  padding: 0 10px;

                  .label-field {
                    display: flex !important;
                    align-items: center;
                    font-size: 14px;
                    margin-bottom: 0;

                    .left-lable {
                      min-width: 80px;
                      color: #606266;
                      font-weight: 500;
                      margin-right: 8px;
                      white-space: nowrap;
                    }

                    span {
                      color: #303133;
                      flex: 1;
                    }
                  }
                }
              }
            }
          }

          .carousel-indicators {
            display: flex;
            justify-content: center;
            margin-top: 15px;
            gap: 8px;

            .indicator {
              width: 8px;
              height: 8px;
              border-radius: 50%;
              background-color: #dcdfe6;
              cursor: pointer;
              transition: all 0.3s ease;

              &:hover {
                background-color: #c0c4cc;
              }

              &.active {
                background-color: #409EFF;
                transform: scale(1.2);
              }
            }
          }
        }
      }
    }
  }

  // 响应式设计
  @media (max-width: 1200px) {
    .asset-cards-container {
      grid-template-columns: repeat(5, 1fr);
      gap: 12px;

      .asset-card {
        padding: 18px 12px;
        min-height: 95px;

        .asset-name {
          font-size: 13px;
          margin-bottom: 8px;
        }

        .asset-count {
          font-size: 22px;
        }
      }
    }
  }

  @media (max-width: 768px) {
    .asset-cards-container {
      grid-template-columns: repeat(3, 1fr);
      gap: 10px;

      .asset-card {
        padding: 16px 10px;
        min-height: 85px;

        .asset-name {
          font-size: 12px;
          margin-bottom: 6px;
        }

        .asset-count {
          font-size: 20px;
        }
      }
    }

    .asset-cards-detail {
      > ul {
        flex-direction: column;
        gap: 15px;

        .detail-item {
          padding: 15px;

          .cards-detail-tit {
            font-size: 14px;
            margin-bottom: 12px;
          }

          .carousel-wrapper {
            .carousel-container {
              &:not(.carousel-active) li {
                .label-fields-container {
                  grid-template-columns: 1fr;
                  gap: 10px;

                  .label-field {
                    font-size: 13px;

                    .left-lable {
                      min-width: 70px;
                      font-size: 13px;
                    }
                  }
                }
              }

              &.carousel-active li {
                .label-fields-container {
                  grid-template-columns: 1fr;
                  gap: 10px;

                  .label-field {
                    font-size: 13px;

                    .left-lable {
                      min-width: 70px;
                      font-size: 13px;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
