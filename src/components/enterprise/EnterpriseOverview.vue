<template>
  <div class="enterprise-overview">
    <el-row :gutter="20">
      <!-- 左侧：企业照片 -->
      <el-col :span="4">
        <div class="overview-left">
          <div class="enterprise-photo">
            <img v-if="enterpriseData.photo" :src="enterpriseData.photo" alt="企业照片" />
            <i v-else class="el-icon-office-building"></i>
          </div>
        </div>
      </el-col>

      <!-- 中间：企业信息 -->
      <el-col :span="14">
        <div class="overview-middle">
          <!-- 第一行：企业名称和统一社会信用代码 -->
          <div class="enterprise-header">
            <h3 class="enterprise-name">{{ enterpriseData.name }}</h3>
            <div class="credit-code">
              <span class="label">统一社会信用代码：</span>
              <span class="value">{{ enterpriseData.creditCode }}</span>
            </div>
          </div>

          <!-- 第二行：企业标签 -->
          <div class="enterprise-tags">
            <el-tag
              v-for="(level, index) in enterpriseData.riskLevels"
              :key="index"
              plain
              size="medium"
              :style="{ color: getRiskLevelColor(level), borderColor: getRiskLevelColor(level), backgroundColor: getLightenedColor(level) }"
              class="risk-tag"
            >
              {{ getRiskLevelLabel(level) }} {{ level }}
            </el-tag>
          </div>

          <!-- 其他企业信息 -->
          <div class="enterprise-info">
            <el-row :gutter="20">
              <el-col :span="12">
                <div class="info-item">
                  <span class="label">企业类型：</span>
                  <span class="value">{{ enterpriseData.companyType }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="info-item">
                  <span class="label">公安备案号：</span>
                  <span class="value">{{ enterpriseData.policeRecordNumber }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="info-item">
                  <span class="label">企业规模：</span>
                  <span class="value">{{ enterpriseData.scale }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="info-item">
                  <span class="label">法定代表人：</span>
                  <span class="value">{{ enterpriseData.legalPerson }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="info-item">
                  <span class="label">联系电话：</span>
                  <span class="value">{{ enterpriseData.contactPhone }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="info-item">
                  <span class="label">企业状态：</span>
                  <span class="value" :style="{ color: getStateColor(enterpriseData.state) }">{{ enterpriseData.status }}</span>
                </div>
              </el-col>
              <el-col :span="24">
                <div class="info-item">
                  <span class="label">注册地址：</span>
                  <span class="value">{{ enterpriseData.address }}</span>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>
      </el-col>

      <!-- 右侧：营业执照照片 -->
      <el-col :span="6">
        <div class="overview-right">
          <div class="license-photo">
            <div class="photo-title">营业执照</div>
            <div class="photo-container">
              <img v-if="enterpriseData.licensePhoto" :src="enterpriseData.licensePhoto" alt="营业执照" />
              <i v-else class="el-icon-picture-outline"></i>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { lightenColor } from '@/utils/index'

export default {
  name: 'EnterpriseOverview',
  props: {
    enterpriseData: {
      type: Object,
      default: () => ({})
    },
    riskLevelOptions: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    /**
     * 根据传入的数据对象匹配标签
     * @param {Object} dataObj - 包含各种数量字段的数据对象
     * @param {Object} riskLevelOptions - 风险等级选项对象
     * @returns {Array} 返回匹配的标签数组，包含 label、value、tagColor
     */
    matchRiskTags(dataObj, riskLevelOptions = {
      'netbarNum': { label: '网吧单位', tagColor: '#FB6B2A' },
      'screenNum': { label: '电子屏单位', tagColor: '#60B8FF' },
      'levelprotectNum': { label: '等保备案单位', tagColor: '#44C991'},
      'websiteNum': { label: '网站备案单位', tagColor: '#F5BC6C' },
      'operatorNum': { label: '运营商单位', tagColor: '#24A8BB' },
      'nonbusinessNum': { label: '非经营单位', tagColor: '#D789D4' },
      'otherNum': { label: '其他', tagColor: '#95ABD4' }
    }) {
      const matchedTags = []

      // 遍历数据对象的每个字段
      Object.keys(dataObj).forEach(dataKey => {
        const dataValue = dataObj[dataKey]

        // 只处理大于0的数值
        if (dataValue > 0) {
          // 在riskLevelOptions中查找匹配的key
          Object.keys(riskLevelOptions).forEach(optionKey => {
            // 使用indexOf进行模糊匹配
            if (dataKey.indexOf(optionKey.replace('Num', '')) > -1 ||
                optionKey.indexOf(dataKey.replace('Num', '')) > -1) {
              const option = riskLevelOptions[optionKey]
              matchedTags.push({
                label: option.label,
                value: dataValue,
                tagColor: option.tagColor
              })
            }
          })
        }
      })

      return matchedTags
    },

    // 获取风险等级颜色
    getRiskLevelColor(level) {
      const option = this.riskLevelOptions.find(opt => opt.value === level)
      return option ? option.tagColor : '#909399'
    },

    // 获取风险等级标签
    getRiskLevelLabel(level) {
      const option = this.riskLevelOptions.find(opt => opt.value === level)
      return option ? option.label : '未知'
    },

    // 获取浅色背景
    getLightenedColor(level) {
      const color = this.getRiskLevelColor(level)
      return lightenColor(color, 0.9)
    },

    // 获取状态颜色
    getStateColor(state) {
      const stateColors = {
        '存续': '#4584FF',
        '在业': '#67C23A',
        '吊销': '#F56C6C',
        '注销': '#909399',
        '迁出': '#E6A23C'
      }
      return stateColors[state] || '#909399'
    }
  }
}
</script>

<style lang="scss" scoped>
.enterprise-overview {
  padding: 20px;
  margin-bottom: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;

  .overview-left {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .enterprise-photo {
      width: 100%;
      height: 150px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #fff;
      border: 1px solid #ebeef5;
      border-radius: 4px;
      overflow: hidden;

      img {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
      }

      i {
        font-size: 60px;
        color: #909399;
      }
    }
  }

  .overview-middle {
    .enterprise-header {
      display: flex;
      align-items: center;
      margin-bottom: 15px;

      .enterprise-name {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        margin-right: 20px;
      }

      .credit-code {
        font-size: 20px;
        color: #4584FF;
      }
    }

    .enterprise-tags {
      display: flex;
      flex-wrap: wrap;
      margin-bottom: 15px;
      padding-bottom: 15px;
      border-bottom: 1px solid #ebeef5;

      .risk-tag {
        margin-right: 8px;
        margin-bottom: 5px;
      }
    }

    .enterprise-info {
      .info-item {
        margin-bottom: 10px;
        
        .label {
          color: #909399;
          margin-right: 5px;
        }

        .value {
          color: #303133;
          word-break: break-all;
        }
      }
    }
  }

  .overview-right {
    height: 100%;

    .license-photo {
      height: 100%;
      display: flex;
      flex-direction: column;
      background-color: #fff;
      border: 1px solid #ebeef5;
      border-radius: 4px;
      overflow: hidden;

      .photo-title {
        padding: 8px;
        text-align: center;
        font-weight: 500;
        background-color: #f5f7fa;
        border-bottom: 1px solid #ebeef5;
      }

      .photo-container {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 10px;
        position: relative;
        width: 100%;

        // 设置16:9的宽高比
        &:before {
          content: "";
          display: block;
          padding-top: 56.25%; // 16:9 = 9/16 = 0.5625 = 56.25%
        }

        img {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          object-fit: contain;
          padding: 10px;
        }

        i {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          font-size: 60px;
          color: #909399;
        }
      }
    }
  }
}
</style>
