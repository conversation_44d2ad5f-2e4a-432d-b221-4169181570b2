<template>
  <div class="monthly-status-module">
    <div class="monthly-container">
      <!-- 左侧部分 -->
      <div class="left-section">
        <!-- 第一个模块：月度总更新 -->
        <div class="monthly-total-module">
          <div class="total-number">{{ monthlyData.totalUpdates }}</div>
          <div class="total-label">月度总更新</div>
          <div class="total-change" :class="getChangeClass(monthlyData.totalChange)">
            同比{{ monthlyData.totalChange > 0 ? '上升' : '下降' }}{{ Math.abs(monthlyData.totalChange) }}%
          </div>
        </div>

        <!-- 第二个模块：三个环状图表 -->
        <div class="ring-charts-module">
          <div
            v-for="(item, index) in ringChartsData"
            :key="index"
            class="ring-chart-item"
          >
            <div class="ring-chart-container">
              <div class="ring-chart" :ref="`ringChart${index}`"></div>
            </div>
            <div class="chart-info">
              <div class="chart-number-row">
                <span class="chart-number">{{ item.number }}</span>
                <span class="chart-change" :class="getChangeClass(item.change)">
                  同比{{ getChangeSymbol(item.change) }}{{ Math.abs(item.change) }}%
                </span>
              </div>
              <div class="chart-label">{{ item.label }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧部分 -->
      <div class="right-section">
        <!-- 左侧三个柱状图 -->
        <div class="bar-charts-group">
          <div
            v-for="(item, index) in leftBarChartsData"
            :key="`left-${index}`"
            class="bar-chart-item"
          >
            <div class="bar-chart-info">
              <div class="bar-label">{{ item.label }}</div>
              <div class="bar-stats">
                <span class="bar-percentage">{{ item.percentage }}%</span>
                <span class="bar-count">{{ item.count }}</span>
                <span class="bar-change" :class="getChangeClass(item.change)">
                  同比 {{ getChangeSymbol(item.change) }} {{ Math.abs(item.change) }}%
                </span>
              </div>
            </div>
            <div class="bar-chart-container">
              <div class="bar-background">
                <div
                  class="bar-fill"
                  :style="{ width: item.percentage + '%' }"
                ></div>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧三个柱状图 -->
        <div class="bar-charts-group">
          <div
            v-for="(item, index) in rightBarChartsData"
            :key="`right-${index}`"
            class="bar-chart-item"
          >
            <div class="bar-chart-info">
              <div class="bar-label">{{ item.label }}</div>
              <div class="bar-stats">
                <span class="bar-percentage">{{ item.percentage }}%</span>
                <span class="bar-count">{{ item.count }}</span>
                <span class="bar-change" :class="getChangeClass(item.change)">
                  同比 {{ getChangeSymbol(item.change) }} {{ Math.abs(item.change) }}%
                </span>
              </div>
            </div>
            <div class="bar-chart-container">
              <div class="bar-background">
                <div
                  class="bar-fill"
                  :style="{ width: item.percentage + '%' }"
                ></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'MonthlyStatusModule',
  data() {
    return {
      monthlyData: {
        totalUpdates: 188,
        totalChange: -1.8
      },
      ringChartsData: [
        {
          percentage: 79,
          number: 101,
          change: 2,
          label: '宽带用户'
        },
        {
          percentage: 59,
          number: 35,
          change: -2,
          label: 'CDN服务'
        },
        {
          percentage: 99,
          number: 28,
          change: 2,
          label: '域名注册'
        }
      ],
      leftBarChartsData: [
        {
          label: 'IDC机房',
          percentage: 36,
          count: 20,
          change: -1
        },
        {
          label: '云服务',
          percentage: 28,
          count: 16,
          change: -0.1
        },
        {
          label: '专线用户',
          percentage: 24,
          count: 163,
          change: 2
        }
      ],
      rightBarChartsData: [
        {
          label: 'IP地址段',
          percentage: 16,
          count: 4,
          change: -2
        },
        {
          label: '上级接入商',
          percentage: 4,
          count: 2,
          change: -0.1
        },
        {
          label: 'IDC托管用户',
          percentage: 1,
          count: 1,
          change: -6
        }
      ],
      ringCharts: []
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initRingCharts()
    })
  },
  beforeDestroy() {
    // 销毁图表实例
    this.ringCharts.forEach(chart => {
      if (chart) {
        chart.dispose()
      }
    })
    this.ringCharts = []
  },
  methods: {
    // 获取变化样式类
    getChangeClass(change) {
      return change > 0 ? 'change-up' : 'change-down'
    },

    // 获取变化符号
    getChangeSymbol(change) {
      return change > 0 ? '↑' : '↓'
    },

    // 初始化环状图表
    initRingCharts() {
      this.ringChartsData.forEach((item, index) => {
        const chartRef = this.$refs[`ringChart${index}`]
        if (chartRef && chartRef[0]) {
          const chart = echarts.init(chartRef[0])

          const option = {
            series: [
              {
                type: 'pie',
                radius: ['70%', '90%'],
                center: ['50%', '50%'],
                startAngle: 90,
                data: [
                  {
                    value: item.percentage,
                    itemStyle: {
                      color: '#409EFF'
                    }
                  },
                  {
                    value: 100 - item.percentage,
                    itemStyle: {
                      color: '#E4E7ED'
                    }
                  }
                ],
                label: {
                  show: true,
                  position: 'center',
                  formatter: `${item.percentage}%`,
                  fontSize: 16,
                  fontWeight: 'bold',
                  color: '#303133'
                },
                labelLine: {
                  show: false
                },
                emphasis: {
                  disabled: true
                },
                animation: false
              }
            ]
          }

          chart.setOption(option)
          this.ringCharts.push(chart)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.monthly-status-module {
  .monthly-container {
    display: flex;
    gap: 20px;

    .left-section {
      flex: 1;
      display: flex;
      align-items: center;
      gap: 40px;

      .monthly-total-module {
        text-align: center;
        padding: 20px;
        background-color: #f8f9fa;
        border-radius: 8px;
        min-width: 200px;

        .total-number {
          font-size: 48px;
          font-weight: bold;
          color: #303133;
          line-height: 1;
          margin-bottom: 10px;
        }

        .total-label {
          font-size: 16px;
          color: #606266;
          margin-bottom: 8px;
        }

        .total-change {
          font-size: 14px;
          font-weight: 500;

          &.change-up {
            color: #F56C6C;
          }

          &.change-down {
            color: #67C23A;
          }
        }
      }

      .ring-charts-module {
        display: flex;
        gap: 30px;
        flex: 1;

        .ring-chart-item {
          flex: 1;
          text-align: center;

          .ring-chart-container {
            width: 100px;
            height: 100px;
            margin: 0 auto 15px;

            .ring-chart {
              width: 100%;
              height: 100%;
            }
          }

          .chart-info {
            .chart-number-row {
              display: flex;
              align-items: center;
              justify-content: center;
              gap: 8px;
              margin-bottom: 5px;

              .chart-number {
                font-size: 20px;
                font-weight: bold;
                color: #303133;
              }

              .chart-change {
                font-size: 12px;

                &.change-up {
                  color: #F56C6C;
                }

                &.change-down {
                  color: #67C23A;
                }
              }
            }

            .chart-label {
              font-size: 14px;
              color: #606266;
            }
          }
        }
      }
    }

    .right-section {
      flex: 1;
      padding: 20px;
      background-color: #f8f9fa;
      border-radius: 8px;
      display: flex;
      gap: 30px;

      .bar-charts-group {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 20px;

        .bar-chart-item {
          display: flex;
          align-items: center;
          gap: 15px;

          .bar-chart-info {
            min-width: 120px;

            .bar-label {
              font-size: 14px;
              color: #303133;
              font-weight: 500;
              margin-bottom: 5px;
            }

            .bar-stats {
              display: flex;
              align-items: center;
              gap: 8px;
              font-size: 12px;

              .bar-percentage {
                font-weight: bold;
                color: #347AFF;
                min-width: 30px;
              }

              .bar-count {
                color: #606266;
                min-width: 20px;
              }

              .bar-change {
                &.change-up {
                  color: #F56C6C;
                }

                &.change-down {
                  color: #67C23A;
                }
              }
            }
          }

          .bar-chart-container {
            flex: 1;
            height: 8px;

            .bar-background {
              width: 100%;
              height: 100%;
              background-color: #E9EEF4;
              border-radius: 4px;
              overflow: hidden;

              .bar-fill {
                height: 100%;
                background-color: #347AFF;
                border-radius: 4px;
                transition: width 0.3s ease;
              }
            }
          }
        }
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .monthly-container {
      flex-direction: column;

      .left-section {
        flex-direction: column;
        gap: 20px;

        .monthly-total-module {
          min-width: auto;
        }

        .ring-charts-module {
          flex-direction: column;
          gap: 15px;

          .ring-chart-item {
            display: flex;
            align-items: center;
            gap: 15px;
            text-align: left;

            .ring-chart-container {
              width: 80px;
              height: 80px;
              margin: 0;
            }

            .chart-info {
              flex: 1;

              .chart-number-row {
                justify-content: flex-start;
              }
            }
          }
        }
      }

      .right-section {
        flex-direction: column;
        gap: 20px;

        .bar-charts-group {
          gap: 15px;

          .bar-chart-item {
            .bar-chart-info {
              min-width: 100px;

              .bar-label {
                font-size: 13px;
              }

              .bar-stats {
                font-size: 11px;
                gap: 6px;

                .bar-percentage {
                  min-width: 25px;
                }

                .bar-count {
                  min-width: 18px;
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
