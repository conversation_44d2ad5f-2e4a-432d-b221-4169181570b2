import { login, logout, getInfo, refreshToken } from '@/api/login'
import { getToken, setToken, setExpiresIn, removeToken } from '@/utils/auth'
import { isEmpty } from "@/utils/validate"
import defAva from '@/assets/images/profile.jpg'
import * as mockApi from '/mock/index'
import {
  adaptLoginResponse,
  adaptUserInfoResponse,
  handleApiError,
  logApiDebug
} from '@/utils/apiAdapter'
const user = {
  state: {
    token: getToken(),
    id: '',
    name: '',
    nickName: '',
    avatar: '',
    roles: [],
    permissions: []
  },

  mutations: {
    SET_TOKEN: (state, token) => {
      state.token = token
    },
    SET_EXPIRES_IN: (state, time) => {
      state.expires_in = time
    },
    SET_ID: (state, id) => {
      state.id = id
    },
    SET_NAME: (state, name) => {
      state.name = name
    },
    SET_NICK_NAME: (state, nickName) =>{
      state.nickName = nickName
    },
    SET_AVATAR: (state, avatar) => {
      state.avatar = avatar
    },
    SET_ROLES: (state, roles) => {
      state.roles = roles
    },
    SET_PERMISSIONS: (state, permissions) => {
      state.permissions = permissions
    }
  },

  actions: {
    // 登录
    Login({ commit }, userInfo) {
      const username = userInfo.username.trim()
      const password = userInfo.password
      return new Promise((resolve, reject) => {
        // 只在Mock启用时调用mockApi
        if (process.env.VUE_APP_ENABLE_MOCK === 'true') {
          mockApi.userMock.login()
        }

        login(username, password).then(res => {
          try {
            logApiDebug('Login', { username }, res)

            // 使用适配器处理登录响应
            const loginData = adaptLoginResponse(res)

            setToken(loginData.token)
            commit('SET_TOKEN', loginData.token)
            setExpiresIn(loginData.expiresIn)
            commit('SET_EXPIRES_IN', loginData.expiresIn)

            resolve()
          } catch (error) {
            const errorMessage = handleApiError(error, { api: 'Login', username })
            reject(new Error(errorMessage))
          }
        }).catch(error => {
          const errorMessage = handleApiError(error, { api: 'Login', username })
          reject(new Error(errorMessage))
        })
      })
    },

    // 获取用户信息
    GetInfo({ commit }) {
      return new Promise((resolve, reject) => {
        // 只在Mock启用时调用mockApi
        if (process.env.VUE_APP_ENABLE_MOCK === 'true') {
          mockApi.userMock.getInfo()
        }

        getInfo().then(res => {
          try {
            logApiDebug('GetInfo', {}, res)

            // 使用适配器处理用户信息响应
            const userInfoData = adaptUserInfoResponse(res)

            // 安全地获取avatar，提供默认值
            let avatar = userInfoData.user.avatar

            // 如果avatar为空或无效，使用默认头像
            if (!avatar || isEmpty(avatar)) {
              avatar = defAva
            } else if (!avatar.startsWith('http') && !avatar.startsWith('/') && !avatar.startsWith('data:')) {
              // 如果是相对路径，添加下载文件的前缀
              const downloadFileUrl = process.env.VUE_APP_BASE_API + "/file/download/"
              avatar = downloadFileUrl + avatar
            }

            // 处理角色和权限
            if (userInfoData.roles && userInfoData.roles.length > 0) {
              commit('SET_ROLES', userInfoData.roles)
              commit('SET_PERMISSIONS', userInfoData.permissions)
            } else {
              commit('SET_ROLES', ['ROLE_DEFAULT'])
              commit('SET_PERMISSIONS', [])
            }

            // 设置用户信息
            commit('SET_ID', userInfoData.user.userId)
            commit('SET_NAME', userInfoData.user.userName)
            commit('SET_NICK_NAME', userInfoData.user.nickName)
            commit('SET_AVATAR', avatar)

            resolve(res)
          } catch (error) {
            const errorMessage = handleApiError(error, { api: 'GetInfo' })
            reject(new Error(errorMessage))
          }
        }).catch(error => {
          const errorMessage = handleApiError(error, { api: 'GetInfo' })
          reject(new Error(errorMessage))
        })
      })
    },

    // 刷新token
    RefreshToken({commit, state}) {
      return new Promise((resolve, reject) => {
        refreshToken(state.token).then(res => {
          setExpiresIn(res.data)
          commit('SET_EXPIRES_IN', res.data)
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    },
    
    // 退出系统
    LogOut({ commit, state }) {
      return new Promise((resolve) => {
        // 只在Mock启用时调用mockApi
        if (process.env.VUE_APP_ENABLE_MOCK === 'true') {
          mockApi.userMock.logout()
        }

        logout(state.token).then(() => {
          commit('SET_TOKEN', '')
          commit('SET_ROLES', [])
          commit('SET_PERMISSIONS', [])
          removeToken()
          resolve()
        }).catch(error => {
          console.error('Logout API Error:', error)
          // 即使退出接口失败，也要清除本地token
          commit('SET_TOKEN', '')
          commit('SET_ROLES', [])
          commit('SET_PERMISSIONS', [])
          removeToken()
          resolve() // 总是resolve，避免退出失败
        })
      })
    },

    // 前端 登出
    FedLogOut({ commit }) {
      return new Promise(resolve => {
        commit('SET_TOKEN', '')
        removeToken()
        resolve()
      })
    }
  }
}

export default user
