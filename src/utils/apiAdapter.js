/**
 * API响应适配器
 * 用于处理不同后端返回的数据格式，统一前端处理逻辑
 */

/**
 * 通用API响应处理函数
 * @param {Object} response - API响应对象
 * @param {Object} options - 处理选项
 * @returns {Object} 处理后的数据
 */
export function handleApiResponse(response, options = {}) {
  const { debug = false, expectedFields = [] } = options
  
  if (debug) {
    console.log('API Response:', response)
  }
  
  // 检查响应是否存在
  if (!response) {
    throw new Error('API响应为空')
  }
  
  // 处理不同的响应结构
  let data = response.data || response
  
  // 检查是否有错误码
  if (data.code !== undefined && data.code !== 200 && data.code !== 0) {
    const errorMessage = data.message || data.msg || data.error || '请求失败'
    throw new Error(`API错误 (${data.code}): ${errorMessage}`)
  }
  
  // 检查必需字段
  if (expectedFields.length > 0) {
    const missingFields = expectedFields.filter(field => {
      return !hasNestedProperty(data, field)
    })
    
    if (missingFields.length > 0) {
      console.warn('缺少预期字段:', missingFields)
    }
  }
  
  return data
}

/**
 * 用户登录响应适配器
 * @param {Object} response - 登录API响应
 * @returns {Object} 标准化的登录数据
 */
export function adaptLoginResponse(response) {
  const data = handleApiResponse(response, { debug: true })
  
  // 支持多种token字段名
  const token = data.access_token || data.token || data.accessToken || data.authToken
  const expiresIn = data.expires_in || data.expiresIn || data.expire || 3600
  const refreshToken = data.refresh_token || data.refreshToken
  
  if (!token) {
    throw new Error('登录响应中未找到有效的token')
  }
  
  return {
    token,
    expiresIn,
    refreshToken,
    originalData: data
  }
}

/**
 * 用户信息响应适配器
 * @param {Object} response - 用户信息API响应
 * @returns {Object} 标准化的用户数据
 */
export function adaptUserInfoResponse(response) {
  const data = handleApiResponse(response, { debug: true })
  
  // 处理不同的用户数据结构
  let userData = data.user || data.data || data.userInfo || data
  
  if (!userData) {
    throw new Error('响应中未找到用户数据')
  }
  
  // 标准化用户字段
  const userInfo = {
    userId: userData.userId || userData.id || userData.user_id || '',
    userName: userData.userName || userData.username || userData.name || userData.user_name || '',
    nickName: userData.nickName || userData.nickname || userData.nick_name || userData.displayName || '',
    avatar: userData.avatar || userData.avatarUrl || userData.avatar_url || userData.photo || '',
    email: userData.email || userData.mail || '',
    phone: userData.phone || userData.mobile || userData.phoneNumber || '',
    sex: userData.sex || userData.gender || '',
    dept: userData.dept || userData.department || null,
    originalData: userData
  }
  
  // 处理角色和权限
  const roles = data.roles || userData.roles || []
  const permissions = data.permissions || userData.permissions || data.perms || userData.perms || []
  
  return {
    user: userInfo,
    roles: Array.isArray(roles) ? roles : [],
    permissions: Array.isArray(permissions) ? permissions : [],
    originalData: data
  }
}

/**
 * 检查对象是否有嵌套属性
 * @param {Object} obj - 要检查的对象
 * @param {string} path - 属性路径，如 'user.name'
 * @returns {boolean} 是否存在该属性
 */
function hasNestedProperty(obj, path) {
  const keys = path.split('.')
  let current = obj
  
  for (const key of keys) {
    if (current === null || current === undefined || !(key in current)) {
      return false
    }
    current = current[key]
  }
  
  return true
}

/**
 * 安全获取嵌套属性值
 * @param {Object} obj - 源对象
 * @param {string} path - 属性路径
 * @param {*} defaultValue - 默认值
 * @returns {*} 属性值或默认值
 */
export function safeGet(obj, path, defaultValue = undefined) {
  if (!hasNestedProperty(obj, path)) {
    return defaultValue
  }
  
  const keys = path.split('.')
  let current = obj
  
  for (const key of keys) {
    current = current[key]
  }
  
  return current
}

/**
 * API错误处理器
 * @param {Error} error - 错误对象
 * @param {Object} context - 上下文信息
 */
export function handleApiError(error, context = {}) {
  console.error('API Error:', error)
  console.error('Context:', context)
  
  // 根据错误类型返回用户友好的错误信息
  if (error.response) {
    // 服务器响应了错误状态码
    const status = error.response.status
    const data = error.response.data
    
    switch (status) {
      case 401:
        return '登录已过期，请重新登录'
      case 403:
        return '没有权限访问该资源'
      case 404:
        return '请求的资源不存在'
      case 500:
        return '服务器内部错误'
      case 502:
      case 503:
      case 504:
        return '服务器暂时不可用，请稍后重试'
      default:
        return data?.message || data?.msg || `请求失败 (${status})`
    }
  } else if (error.request) {
    // 请求已发出但没有收到响应
    return '网络连接失败，请检查网络设置'
  } else {
    // 其他错误
    return error.message || '未知错误'
  }
}

/**
 * 创建API调试信息
 * @param {string} apiName - API名称
 * @param {Object} params - 请求参数
 * @param {Object} response - 响应数据
 */
export function createApiDebugInfo(apiName, params, response) {
  return {
    api: apiName,
    timestamp: new Date().toISOString(),
    params,
    response,
    mockEnabled: process.env.VUE_APP_ENABLE_MOCK === 'true',
    environment: process.env.NODE_ENV,
    baseUrl: process.env.VUE_APP_BASE_API
  }
}

/**
 * 打印API调试信息
 * @param {string} apiName - API名称
 * @param {Object} params - 请求参数
 * @param {Object} response - 响应数据
 */
export function logApiDebug(apiName, params, response) {
  if (process.env.NODE_ENV === 'development') {
    const debugInfo = createApiDebugInfo(apiName, params, response)
    console.group(`🔍 API Debug: ${apiName}`)
    console.log('📤 Request Params:', params)
    console.log('📥 Response:', response)
    console.log('⚙️ Environment:', {
      mock: debugInfo.mockEnabled,
      env: debugInfo.environment,
      baseUrl: debugInfo.baseUrl
    })
    console.groupEnd()
  }
}
