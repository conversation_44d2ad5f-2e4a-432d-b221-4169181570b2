/**
 * Mock拦截器
 * 用于在开发环境中拦截API请求并返回mock数据
 * 在生产环境中可以通过注释掉main.js中的引用来禁用
 */
import MockAdapter from 'axios-mock-adapter'
import * as enterpriseMock from 'mock/enterprise'
import * as userMock from 'mock/user'
import service from '@/utils/request'

// 打印一条消息，表明mock拦截器已启用
console.log('%c[Mock] Mock拦截器已启用，API请求将返回模拟数据', 'color: #44C991; font-weight: bold;');

// 打印所有可用的mock方法
console.log('%c[Mock] 可用的mock方法 (enterprise):', 'color: #44C991; font-weight: bold;', Object.keys(enterpriseMock));
console.log('%c[Mock] 可用的mock方法 (user):', 'color: #44C991; font-weight: bold;', Object.keys(userMock));

// 创建一个mock适配器实例，应用于service实例
console.log('%c[Mock] 创建mock适配器，应用于service实例', 'color: #44C991; font-weight: bold;');
console.log('%c[Mock] service.defaults.baseURL:', 'color: #44C991; font-weight: bold;', service.defaults.baseURL);

const mockAdapter = new MockAdapter(service, {
  delayResponse: 800, // 模拟网络延迟
  onNoMatch: (request) => {
    console.log('%c[Mock] 未匹配到请求:', 'color: #FF6B6B; font-weight: bold;', request.url);
    return 'passthrough';
  } // 未匹配到的请求将被传递给真实服务器
})

// 企业列表接口
mockAdapter.onGet(new RegExp('/enterprise/list.*')).reply(config => {
  console.log('Mock API: 拦截到 getEnterpriseList 请求');

  // 从URL中提取查询参数
  const url = new URL(config.url, window.location.origin)
  const categoryType = url.searchParams.get('categoryType') || 'all'
  const pageNum = parseInt(url.searchParams.get('pageNum')) || 1
  const pageSize = parseInt(url.searchParams.get('pageSize')) || 10
  const keyword = url.searchParams.get('keyword') || ''
  const searchType = url.searchParams.get('searchType') || 'all'

  console.log('Mock API: getEnterpriseList 参数:', { categoryType, pageNum, pageSize, keyword, searchType });

  // 获取mock数据
  const allData = enterpriseMock.getEnterpriseList(categoryType)

  // 筛选数据
  let filteredData = allData
  if (keyword) {
    filteredData = allData.filter(item => {
      switch (searchType) {
        case 'name':
          return item.name.toLowerCase().includes(keyword.toLowerCase())
        case 'creditCode':
          return item.creditCode.toLowerCase().includes(keyword.toLowerCase())
        case 'legalPerson':
          return (item.legalPerson || '').toLowerCase().includes(keyword.toLowerCase())
        case 'all':
        default:
          return item.name.toLowerCase().includes(keyword.toLowerCase()) ||
            item.creditCode.toLowerCase().includes(keyword.toLowerCase()) ||
            (item.legalPerson || '').toLowerCase().includes(keyword.toLowerCase())
      }
    })
  }

  // 计算分页
  const total = filteredData.length
  const startIndex = (pageNum - 1) * pageSize
  const endIndex = startIndex + pageSize
  const pagedData = filteredData.slice(startIndex, endIndex)

  console.log(`Mock API: getEnterpriseList 返回 ${pagedData.length} 条数据，总计 ${total} 条`);

  // 返回mock响应
  return [200, {
    code: 200,
    msg: '操作成功',
    data: {
      total,
      list: pagedData
    }
  }]
})

// 企业详情接口
mockAdapter.onGet(new RegExp('/enterprise/detail/\\d+')).reply(config => {
  console.log('Mock API: 拦截到 getEnterpriseDetail 请求');

  // 从URL中提取企业ID
  const id = parseInt(config.url.match(/\/detail\/(\d+)/)[1])
  console.log('Mock API: getEnterpriseDetail 企业ID:', id);

  // 获取所有企业数据
  const allEnterprises = enterpriseMock.getEnterpriseList('all')

  // 查找对应ID的企业
  const enterprise = allEnterprises.find(item => item.id === id) || allEnterprises[0]

  // 返回mock响应
  return [200, {
    code: 200,
    msg: '操作成功',
    data: enterprise
  }]
})

// 获取风险级别选项接口
mockAdapter.onGet(new RegExp('/enterprise/riskLevelOptions')).reply((config) => {
  console.log('Mock API: 拦截到 getRiskLevelOptions 请求', config.url);
  const riskLevelOptions = [
    { value: '1', label: '网吧单位', tagColor: '#FB6B2A' },
    { value: '2', label: '电子屏单位', tagColor: '#60B8FF' },
    { value: '3', label: '等保备案单位', tagColor: '#44C991' },
    { value: '4', label: '网站备案单位', tagColor: '#F5BC6C' },
    { value: '5', label: '运营商单位', tagColor: '#24A8BB' },
    { value: '6', label: '非经营单位', tagColor: '#D789D4' },
    { value: '7', label: '其他', tagColor: '#95ABD4' }
  ]

  return [200, {
    code: 200,
    msg: '操作成功',
    data: riskLevelOptions
  }]
})

// 获取企业分类统计数据接口
mockAdapter.onGet(new RegExp('/enterprise/categoryStats')).reply((config) => {
  console.log('Mock API: 拦截到 getEnterpriseCategoryStats 请求', config.url);
  const categoryStats = [
    { id: 'all', name: '全部单位', count: 1564, color: '#409EFF', icon: 'el-icon-office-building' },
    { id: 'screen', name: '电子屏单位', count: 564, color: '#60B8FF', icon: 'el-icon-monitor' },
    { id: 'security', name: '等保备案单位', count: 364, color: '#44C991', icon: 'el-icon-lock' },
    { id: 'website', name: '网站备案单位', count: 264, color: '#F5BC6C', icon: 'el-icon-link' },
    { id: 'operator', name: '运营商单位', count: 464, color: '#24A8BB', icon: 'el-icon-connection' },
    { id: 'netbar', name: '网吧单位', count: 264, color: '#FB6B2A', icon: 'el-icon-mouse' },
    { id: 'nonbusiness', name: '非经营单位', count: 44, color: '#D789D4', icon: 'el-icon-document' },
    { id: 'other', name: '其他', count: 1564 - 564 - 364 - 264 - 464 - 264 - 44, color: '#95ABD4', icon: 'el-icon-more' }
  ]

  return [200, {
    code: 200,
    msg: '操作成功',
    data: categoryStats
  }]
})

// 添加企业接口
mockAdapter.onPost(new RegExp('/enterprise/add.*')).reply(() => {
  console.log('Mock API: 拦截到 addEnterprise 请求');
  return [200, {
    code: 200,
    msg: '添加成功',
    data: null
  }]
})

// 更新企业接口
mockAdapter.onPut(new RegExp('/enterprise/update.*')).reply(() => {
  console.log('Mock API: 拦截到 updateEnterprise 请求');
  return [200, {
    code: 200,
    msg: '更新成功',
    data: null
  }]
})

// 删除企业接口
mockAdapter.onDelete(new RegExp('/enterprise/delete/.*')).reply(() => {
  console.log('Mock API: 拦截到 deleteEnterprise 请求');
  return [200, {
    code: 200,
    msg: '删除成功',
    data: null
  }]
})

// 批量删除企业接口
mockAdapter.onDelete(new RegExp('/enterprise/batchDelete.*')).reply(() => {
  console.log('Mock API: 拦截到 batchDeleteEnterprise 请求');
  return [200, {
    code: 200,
    msg: '批量删除成功',
    data: null
  }]
})

// ==================== 用户相关接口 ====================

// 登录接口
mockAdapter.onPost('/auth/login').reply((config) => {
  console.log('Mock API: 拦截到 login 请求');
  const { username, password } = JSON.parse(config.data);
  console.log('Mock API: login 参数:', { username, password });

  return [200, {
    code: 200,
    msg: null,
    data: {
      access_token: "eyJhbGciOiJIUzUxMiJ9.eyJ1c2VyX2lkIjoxLCJ1c2VyX2tleSI6IjUyY2YwNTM5LWE1ODEtNDBlMy04OTdiLWI1YzY3NjM0MDkzZCIsInVzZXJuYW1lIjoiYWRtaW4ifQ.82EDxkwa6_t8EwLs15o-1i5g288BI1YvR-V2q1QZQEExypHFjslQZrxkpfhbzWNpzOuISw_PamuhnwjVqABUWQ",
      expires_in: 60
    }
  }]
})

// 登出接口
mockAdapter.onDelete('/auth/logout').reply(() => {
  console.log('Mock API: 拦截到 logout 请求');
  return [200, {
    code: 200,
    msg: null,
    data: {}
  }]
})

// 获取用户信息接口
mockAdapter.onGet('/system/user/getInfo').reply(() => {
  console.log('Mock API: 拦截到 getInfo 请求');
  return [200, {
    msg: "操作成功",
    code: 200,
    permissions: [
      "*:*:*"
    ],
    roles: [
      "admin"
    ],
    user: {
      createBy: "admin",
      createTime: "2024-06-30 11:27:11",
      updateBy: null,
      updateTime: null,
      remark: "管理员",
      userId: 1,
      deptId: 103,
      userName: "admin",
      nickName: "大王",
      email: "<EMAIL>",
      phonenumber: "15888888888",
      sex: "1",
      avatar: "",
      password: "$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2",
      status: "0",
      delFlag: "0",
      loginIp: "*************",
      loginDate: "2025-05-15T17:51:45.000+08:00",
      dept: {
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null,
        deptId: 103,
        parentId: 101,
        ancestors: "0,100,101",
        deptName: "研发部门",
        orderNum: 1,
        leader: "大王",
        phone: null,
        email: null,
        status: "0",
        delFlag: null,
        parentName: null,
        children: []
      },
      roles: [
        {
          createBy: null,
          createTime: null,
          updateBy: null,
          updateTime: null,
          remark: null,
          roleId: 1,
          roleName: "超级管理员",
          roleKey: "admin",
          roleSort: 1,
          dataScope: "1",
          menuCheckStrictly: false,
          deptCheckStrictly: false,
          status: "0",
          delFlag: null,
          flag: false,
          menuIds: null,
          deptIds: null,
          permissions: null,
          admin: true
        }
      ],
      roleIds: null,
      postIds: null,
      roleId: null,
      admin: true
    }
  }]
})

// 获取路由接口
mockAdapter.onGet('/system/menu/getRouters').reply(() => {
  console.log('Mock API: 拦截到 getRouters 请求');
  return [200, {
    msg: "操作成功",
    code: 200,
    data: [
      {
        name: "System",
        path: "/system",
        hidden: false,
        redirect: "noRedirect",
        component: "Layout",
        alwaysShow: true,
        meta: {
          title: "系统管理",
          icon: "system",
          noCache: false,
          link: null
        },
        children: [
          {
            name: "User",
            path: "user",
            hidden: false,
            component: "system/user/index",
            meta: {
              title: "用户管理",
              icon: "user",
              noCache: false,
              link: null
            }
          },
          {
            name: "Role",
            path: "role",
            hidden: false,
            component: "system/role/index",
            meta: {
              title: "角色管理",
              icon: "peoples",
              noCache: false,
              link: null
            }
          }
        ]
      },
      {
        name: "Enterprise",
        path: "/enterprise",
        hidden: false,
        redirect: "noRedirect",
        component: "Layout",
        alwaysShow: true,
        meta: {
          title: "企业管理",
          icon: "tree",
          noCache: false,
          link: null
        },
        children: [
          {
            name: "EnterpriseList",
            path: "index",
            hidden: false,
            component: "enterprise/index",
            meta: {
              title: "企业列表",
              icon: "list",
              noCache: false,
              link: null
            }
          },
          {
            name: "EnterpriseDetail",
            path: "detail/:id",
            hidden: true,
            component: "enterprise/detail",
            meta: {
              title: "企业详情",
              icon: "form",
              noCache: false,
              link: null
            }
          }
        ]
      }
    ]
  }]
})

// 获取验证码接口
mockAdapter.onGet('/code').reply(() => {
  console.log('Mock API: 拦截到 getCodeImg 请求');
  return [200, {
    code: 200,
    msg: null,
    img: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAHgAAAAkCAIAAADNSmkJAAAFKklEQVR4Xu2aW0wUVxiAZ2Z32V12F3aRmyBesNgaq9KkpGlMxUbTpjVtGlOtJk1fTNM+mNg+tX0xfdAYY9K+mKZNm8ZL08ZL2/ShDcVUDQhSFSleqCjIrrjLLruzs7vTc+bsrMMyBMdddg7yfzzAzJk5/3z/f/5z5szA4P9lYtZ+wGwTWfsBs00E0TNPZNEzTwTRM09k0TNPBNHTT8Jh6NSpU9evX5/0qlQqPXz48J49e7Kzs6e8Kgj0er1SqQx/ZVn2+vXrV65cef/+/YRXIyMjDx06tGvXroyMjPBXlmWvXbt25cqVhw8fhr+Gw+H9+/fv3r07MzMzXLh169alS5f6+/vDhXA4vG/fvj179mRlZYULt2/fvnjxYl9fX7gQCoX27t27d+/enJyccOHOnTsXLlzo7e0NF4LB4Mcff7xv377c3NxwIRAIXLx48d69e+HCpk2bvvnmm7KyMsGFJpPp/PnzPT094YLP5/voo48OHDiQl5cXLnR3d587d667uztc8Hq9Bw4cOHjwYH5+frjQ1dV19uzZR48ehQsej2f//v2ffPJJQUFBuNDZ2Xnmh9M/P/4pXHC73fv27Tt06FBhYWG48PDhw9OnT3d0dIQLLpdr7969hw8fLioqChc6OjpOnTrV3t4eLjidzo8//viTTz8tLi4OF9rb20+ePNnW1hYuOByOjz766MiRI5s3bw4X2traWltbW1tbwwW73f7hhx9+/sUXJSUl4UJra2tLS0tra2u4YLPZDh48ePTo0dLS0nDh1q1bzc3NLS0t4YLVav3ggw+OHTu2ZcuWcKG5ubmpqampqSlcsFgsH3/+2fETX5aVlYULN27caGxsbGxsDBfMZvOHH3549OjR8vLycKGxsbGhoaGhoSFcMJlM77///vHjxysqKsKFhoaG+vr6+vr6cMFoNL733nvHjx+vrKwMF+rr6+vq6urq6sIFg8Hw7rvvnjhxoqqqKlyoq6urra2tra0NF/R6/TvvvPP1119XVVWFCzU1NdXV1dXV1eGCTqd7++23T548uXXr1nChurq6qqqqqqoqXNBqtW+99dbJb7/dtm1buFBVVVVZWVlZWRkuaLw+z+joqEQiEVwYGRlRq9WCCzabzev1qtVqwYXh4WGNRiO4YLVafT6fWq0WXBgaGtJqtYILFovF7/er1WrBhcHBQZ1OJ7hgNpsDgYBarRZcGBgY0Ov1ggtmszkYDKpUKsGF/v5+g8EguGAymUKhkEqlElzo6+szGo2CCyaTKRwOK5VKwYXe3l6TySS4MDAwgDFWKBSCC48fPzaZTIIL/f39GGOFQiG40NPTMzg4KLjQ19eHMZbL5YIL3d3dQ0NDggu9vb0YY7lcLrjQ1dU1PDwsuNDT04MxlslkggudnZ0jIyOCC93d3RhjmUwmuNDR0TEyMiK48OjRI4yxVCoVXGhvbx8dHRVc6OrqwhhLpVLBhba2trGxMcGFzs5OjLFEIhFcaG1tHR8fF1zo6OjAGEskEsGFlpaWYDAouNDe3o4xlkgkggvNzc0TExOCC21tbRhjsVgsuNDU1DQ5OSm48PDhQ4yxWCwWXGhsbJycnBRcaG1txRiLxWLBhYaGhkAgILjQ0tKCMRaJRIIL9fX1gUBAcKG5uRljLBKJBBdqa2sDgYDgQlNTE8ZYJBIJLtTU1Ph8PsGFxsZGjLFIJBJcuHnzps/nE1xoaGjAGIvFYsGF69ev+/1+wYX6+nqMsVgsFly4du2a3+8XXKirq8MYi8ViwYWrV68GAgHBhdraWoyxRCIRXLhy5crExITgQk1NDcZYIpEILly+fHliYkJw4ebNmxhjiUQiuHDp0qXJyUnBhfr6eoyxVCoVXLh48eLk5KTgQl1dHcZYKpUKLvz000+Tk5OCCzU1NRhjqVQquHDhwoWpqSmRSPQvptVNQcrXnqsAAAAASUVORK5CYII=",
    uuid: "7e8c0a00-0e7a-4c0e-a9b0-5c600a7a0000"
  }]
})

export default mockAdapter
