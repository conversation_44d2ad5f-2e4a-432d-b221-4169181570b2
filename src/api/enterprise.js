import request from '@/utils/request'

/**
 * 企业管理相关接口
 *
 * 注意：这些API路径需要与后端API路径保持一致
 * 在开发环境中，这些请求会被 mockInterceptor.js 拦截并返回模拟数据
 * 在生产环境中，这些请求会直接发送到真实后端API
 */

// 获取企业列表数据
export function getEnterpriseList(params) {
  return request({
    url: '/system/sysEnterprise/list',
    method: 'get',
    params
  })
}

// 获取企业详情
export function getEnterpriseDetail(id) {
  return request({
    url: `/system/sysEnterprise/detail`,
    method: 'get',
    params: { id }
  })
}

// 添加企业
export function addEnterprise(data) {
  return request({
    url: '/enterprise/add',
    method: 'post',
    data: data
  })
}



// 删除企业
export function deleteEnterprise(id) {
  return request({
    url: `/enterprise/delete/${id}`,
    method: 'delete'
  })
}

// 批量删除企业
export function batchDeleteEnterprise(ids) {
  return request({
    url: '/enterprise/batchDelete',
    method: 'delete',
    data: { ids }
  })
}

// 获取企业风险级别选项
export function getRiskLevelOptions() {
  return request({
    url: '/enterprise/riskLevelOptions',
    method: 'get'
  })
}

// 获取企业分类统计数据
export function getEnterpriseCategoryStats() {
  return request({
    url: '/system/sysEnterprise/stat',
    method: 'get'
  })
}

// 更新企业信息（用于编辑抽屉）
export function updateEnterprise(data) {
  return request({
    url: `/enterprise/${data.id}`,
    method: 'put',
    data
  })
}

// 获取关联电子屏列表
export function getScreenList(params) {
  return request({
    url: '/netbar/siteBase/list',
    method: 'get',
    params
  })
}

// 获取网吧列表
export function getNetbarList(params) {
  return request({
    url: '/netbar/siteBase/list',
    method: 'get',
    params
  })
}

// 获取等保备案列表
export function getLevelProtectList(params) {
  return request({
    url: '/levelprotect/levelprotect/list',
    method: 'get',
    params
  })
}

// 获取网站备案列表
export function getWebsiteList(params) {
  return request({
    url: '/website/website/list',
    method: 'get',
    params
  })
}

// 获取APP列表
export function getAppList(params) {
  return request({
    url: '/website/app/list',
    method: 'get',
    params
  })
}

// 获取小程序列表
export function getAppletList(params) {
  return request({
    url: '/website/applet/list',
    method: 'get',
    params
  })
}

// 获取运营商列表
export function getOperatorList(params) {
  return request({
    url: '/operator/operatorInfo/list',
    method: 'get',
    params
  })
}

// 获取非经营场所列表
export function getWifiSiteList(params) {
  return request({
    url: '/wifi/site/list',
    method: 'get',
    params
  })
}