# API 接口说明

## 概述

本项目使用了两种API调用方式：

1. **开发环境**：使用mock拦截器模拟API响应
2. **生产环境**：连接到真实后端API

## Mock API

在开发环境中，所有API请求都会被 `src/utils/mockInterceptor.js` 拦截并返回模拟数据。这样可以在后端API尚未准备好的情况下进行前端开发。

## 切换到真实后端API

当后端API准备好后，只需要进行以下步骤即可切换到真实后端API：

1. 在 `src/main.js` 文件中注释掉mock拦截器的引入：

```javascript
// 注释掉这一行
// import '@/utils/mockInterceptor'
```

2. 确保 `.env.production` 文件中的 `VUE_APP_BASE_API` 配置正确指向后端API地址：

```
VUE_APP_BASE_API = '/prod-api'  // 或者其他真实后端API地址
```

3. 如果需要，可以在 `vue.config.js` 中配置代理，以解决开发环境中的跨域问题：

```javascript
devServer: {
  proxy: {
    '/api': {
      target: 'http://your-backend-api-url',
      changeOrigin: true,
      pathRewrite: {
        '^/api': ''
      }
    }
  }
}
```

## API接口文件

所有API接口都定义在 `src/api/` 目录下的文件中，按功能模块进行组织：

- `src/api/enterprise.js` - 企业管理相关接口
- `src/api/login.js` - 登录认证相关接口
- 其他模块...

## 注意事项

1. API路径需要与后端API路径保持一致
2. 在生产环境构建前，确保已注释掉mock拦截器的引入
3. 如果后端API的数据结构与mock数据结构不一致，需要相应调整前端代码
