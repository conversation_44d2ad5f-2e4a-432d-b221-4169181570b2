'use strict'
const path = require('path')

function resolve(dir) {
  return path.join(__dirname, dir)
}

const CompressionPlugin = require('compression-webpack-plugin')

const name = process.env.VUE_APP_TITLE || '若依管理系统' // 网页标题

const port = process.env.port || process.env.npm_config_port || 84 // 端口

// vue.config.js 配置说明
//官方vue.config.js 参考文档 https://cli.vuejs.org/zh/config/#css-loaderoptions
// 这里只列一部分，具体配置参考文档
module.exports = {
  // 部署生产环境和开发环境下的URL。
  // 默认情况下，Vue CLI 会假设你的应用是被部署在一个域名的根路径上
  // 例如 https://www.ruoyi.vip/。如果应用被部署在一个子路径上，你就需要用这个选项指定这个子路径。例如，如果你的应用被部署在 https://www.ruoyi.vip/admin/，则设置 baseUrl 为 /admin/。
  publicPath: process.env.NODE_ENV === "production" ? "/" : "/",
  // 在npm run build 或 yarn build 时 ，生成文件的目录名称（要和baseUrl的生产环境路径一致）（默认dist）
  outputDir: 'dist',
  // 用于放置生成的静态资源 (js、css、img、fonts) 的；（项目打包之后，静态资源会放在这个文件夹下）
  assetsDir: 'static',
  // 如果你不需要生产环境的 source map，可以将其设置为 false 以加速生产环境构建。
  productionSourceMap: false,
  transpileDependencies: ['quill'],
  // webpack-dev-server 相关配置
  devServer: {
    host: '0.0.0.0',
    port: port,
    open: true,
    hot: true,
    hotOnly: false,
    liveReload: true,
    watchOptions: {
      poll: false, // 禁用轮询，使用文件系统事件
      ignored: /node_modules/, // 忽略 node_modules 文件夹
      aggregateTimeout: 300 // 延迟重新构建的时间
    },
    proxy: {
      // detail: https://cli.vuejs.org/config/#devserver-proxy
      [process.env.VUE_APP_BASE_API]: {
                target: `http://***********:8082/prod-api`,
                ws: true,
                changeOrigin: true, // 请求跨域时，需配置此项
                pathRewrite: {
                    ['^' + process.env.VUE_APP_BASE_API]: ''
                }
            },
    },
    disableHostCheck: true
  },
  css: {
    loaderOptions: {
      sass: {
        sassOptions: { outputStyle: "expanded" }
      }
    }
  },
  configureWebpack: {
    name: name,
    resolve: {
      alias: {
        '@': resolve('src'),
        'mock': resolve('mock')
      }
    },
    // 开发环境性能优化
    ...(process.env.NODE_ENV === 'development' ? {
      cache: {
        type: 'filesystem', // 使用文件系统缓存
        buildDependencies: {
          config: [__filename] // 当配置文件改变时，缓存失效
        }
      },
      optimization: {
        removeAvailableModules: false,
        removeEmptyChunks: false,
        splitChunks: false
      }
    } : {}),
    plugins: [
      // http://doc.ruoyi.vip/ruoyi-vue/other/faq.html#使用gzip解压缩静态文件
      // 只在生产环境启用压缩插件
      ...(process.env.NODE_ENV === 'production' ? [
        new CompressionPlugin({
          cache: false,                                  // 不启用文件缓存
          test: /\.(js|css|html|jpe?g|png|gif|svg)?$/i,  // 压缩文件格式
          filename: '[path][base].gz[query]',            // 压缩后的文件名
          algorithm: 'gzip',                             // 使用gzip压缩
          minRatio: 0.8,                                 // 压缩比例，小于 80% 的文件不会被压缩
          deleteOriginalAssets: false                    // 压缩后删除原文件
        })
      ] : [])
    ],
    // 在开发服务器模式下，使用 hash 而不是 contenthash
    output: process.argv.includes('serve') ? {
      filename: 'static/js/[name].[hash:8].js',
      chunkFilename: 'static/js/[name].[hash:8].js'
    } : undefined
  },
  chainWebpack(config) {
    config.plugins.delete('preload') // TODO: need test
    config.plugins.delete('prefetch') // TODO: need test

    // 在开发服务器模式下，修改文件名配置
    if (process.argv.includes('serve')) {
      // 只在生产模式的开发服务器中修改 CSS 文件名配置
      if (process.env.VUE_APP_BASE_API === '/prod-api') {
        // 检查插件是否存在再进行配置
        if (config.plugins.has('extract-css')) {
          config.plugin('extract-css').tap(args => {
            args[0].filename = 'static/css/[name].[hash:8].css'
            args[0].chunkFilename = 'static/css/[name].[hash:8].css'
            return args
          })
        }
      }
    }

    // set svg-sprite-loader
    config.module
      .rule('svg')
      .exclude.add(resolve('src/assets/icons'))
      .end()
    config.module
      .rule('icons')
      .test(/\.svg$/)
      .include.add(resolve('src/assets/icons'))
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'icon-[name]'
      })
      .end()

    // 只在真正的生产构建时应用这些配置，不在开发服务器时应用
    config.when(process.env.NODE_ENV === 'production' && process.argv.includes('build'), config => {
          config
            .plugin('ScriptExtHtmlWebpackPlugin')
            .after('html')
            .use('script-ext-html-webpack-plugin', [{
            // `runtime` must same as runtimeChunk name. default is `runtime`
              inline: /runtime\..*\.js$/
            }])
            .end()

          config.optimization.splitChunks({
            chunks: 'all',
            cacheGroups: {
              libs: {
                name: 'chunk-libs',
                test: /[\\/]node_modules[\\/]/,
                priority: 10,
                chunks: 'initial' // only package third parties that are initially dependent
              },
              elementUI: {
                name: 'chunk-elementUI', // split elementUI into a single package
                test: /[\\/]node_modules[\\/]_?element-ui(.*)/, // in order to adapt to cnpm
                priority: 20 // the weight needs to be larger than libs and app or it will be packaged into libs or app
              },
              commons: {
                name: 'chunk-commons',
                test: resolve('src/components'), // can customize your rules
                minChunks: 3, //  minimum common number
                priority: 5,
                reuseExistingChunk: true
              }
            }
          })
          config.optimization.runtimeChunk('single')
    })
  }
}
