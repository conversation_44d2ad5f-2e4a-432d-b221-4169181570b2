import Mock from 'mockjs'
import Cookies from 'js-cookie'
let sortNameList = ['0000', '1111', '2222', '3333', '4444', '5555', '6666', '7777', '8888', '9999']
    // 分类管理 start
export const fetchTreeList = () => {
    Mock.mock('/tmes/mat/materialSort/initMaterialSortList', {
        "rows": [{
                "approveStatus": "",
                "createName": "",
                "createTime": null,
                "f1": "",
                "f2": "",
                "f3": "",
                "f4": "",
                "f5": "",
                "f6": "",
                "factoryId": "0",
                "id": "110",
                "keyword": "",
                "leaf": "1",
                "orderBy": "",
                "parentName": "",
                "parentTreeLevel": "",
                "remark": "",
                "root_flag": "Y",
                "sortCode": "MAT0000",
                "sortName": "分类",
                "sortType": "MAT",
                "updateName": "",
                "updateTime": null,
                "useStatus": ""
            },
            {
                "approveStatus": "",
                "createName": "",
                "createTime": null,
                "f1": "",
                "f2": "",
                "f3": "",
                "f4": "",
                "f5": "",
                "f6": "",
                "factoryId": "0",
                "id": "111",
                "keyword": "",
                "leaf": "1",
                "orderBy": "",
                "parentId": "110",
                "parentName": "",
                "parentTreeLevel": "",
                "remark": "",
                "root_flag": "N",
                "sortCode": "A0000",
                "sortName": "标准件",
                "sortType": "MAT",
                "updateName": "",
                "updateTime": null,
                "useStatus": ""
            },
            {
                "approveStatus": "",
                "createName": "",
                "createTime": null,
                "f1": "",
                "f2": "",
                "f3": "",
                "f4": "",
                "f5": "",
                "f6": "",
                "factoryId": "0",
                "id": "112",
                "keyword": "",
                "leaf": "1",
                "orderBy": "",
                "parentId": "110",
                "parentName": "",
                "parentTreeLevel": "",
                "remark": "",
                "root_flag": "N",
                "sortCode": "B0000",
                "sortName": "日常用品",
                "sortType": "MAT",
                "updateName": "",
                "updateTime": null,
                "useStatus": ""
            },
            {
                "approveStatus": "",
                "createName": "",
                "createTime": null,
                "f1": "",
                "f2": "",
                "f3": "",
                "f4": "",
                "f5": "",
                "f6": "",
                "factoryId": "0",
                "id": "113",
                "keyword": "",
                "leaf": "1",
                "orderBy": "",
                "parentId": "110",
                "parentName": "",
                "parentTreeLevel": "",
                "remark": "",
                "root_flag": "N",
                "sortCode": "C0000",
                "sortName": "化学品",
                "sortType": "MAT",
                "updateName": "",
                "updateTime": null,
                "useStatus": ""
            },
            {
                "approveStatus": "",
                "createName": "",
                "createTime": null,
                "f1": "",
                "f2": "",
                "f3": "",
                "f4": "",
                "f5": "",
                "f6": "",
                "factoryId": "0",
                "id": "114",
                "keyword": "",
                "leaf": "1",
                "orderBy": "",
                "parentId": "111",
                "parentName": "",
                "parentTreeLevel": "",
                "remark": "",
                "root_flag": "N",
                "sortCode": "A0100",
                "sortName": "A",
                "sortType": "MAT",
                "updateName": "",
                "updateTime": null,
                "useStatus": ""
            },
            {
                "approveStatus": "",
                "createName": "",
                "createTime": null,
                "f1": "",
                "f2": "",
                "f3": "",
                "f4": "",
                "f5": "",
                "f6": "",
                "factoryId": "0",
                "id": "115",
                "keyword": "",
                "leaf": "1",
                "orderBy": "",
                "parentId": "114",
                "parentName": "",
                "parentTreeLevel": "",
                "remark": "",
                "root_flag": "N",
                "sortCode": "A0101",
                "sortName": "B",
                "sortType": "MAT",
                "updateName": "",
                "updateTime": null,
                "useStatus": ""
            },
            {
                "approveStatus": "",
                "createName": "",
                "createTime": null,
                "f1": "",
                "f2": "",
                "f3": "",
                "f4": "",
                "f5": "",
                "f6": "",
                "factoryId": "0",
                "id": "131",
                "keyword": "",
                "leaf": "1",
                "orderBy": "",
                "parentId": "111",
                "parentName": "",
                "parentTreeLevel": "",
                "remark": "",
                "root_flag": "N",
                "sortCode": "A0200",
                "sortName": "螺丝",
                "sortType": "MAT",
                "updateName": "",
                "updateTime": null,
                "useStatus": ""
            },
            {
                "approveStatus": "",
                "createName": "",
                "createTime": null,
                "f1": "",
                "f2": "",
                "f3": "",
                "f4": "",
                "f5": "",
                "f6": "",
                "factoryId": "0",
                "id": "132",
                "keyword": "",
                "leaf": "1",
                "orderBy": "",
                "parentId": "112",
                "parentName": "",
                "parentTreeLevel": "",
                "remark": "",
                "root_flag": "N",
                "sortCode": "B0100",
                "sortName": "纸张",
                "sortType": "MAT",
                "updateName": "",
                "updateTime": null,
                "useStatus": ""
            }
        ],
        "total": "8",
        "code": "200",
        "msg": "请求成功",
        "timestamp": "1688975675497"
    })
}
let treeList = (para) => {
    return Mock.mock({
        "rows|1-10": [{
            "approveStatus": "",
            "createName": "",
            "createTime": null,
            "f1": "",
            "f2": "",
            "f3": "",
            "f4": "",
            "f5": "",
            "f6": "",
            "factoryId": "0",
            "keyword": "",
            "leaf": "1",
            "orderBy": "",
            "parentId": para.parentId,
            "id|+1": (para.parentId * 1 + 1),
            "parentName": "",
            "parentTreeLevel": "",
            "remark": "",
            "root_flag": "N",
            "sortCode": `${Mock.Random.character('upper')}${Mock.Random.integer(0,9999)}`,
            "sortName": '@cword(3, 8)',
            "sortType": "MAT",
            "updateName": "",
            "updateTime": null,
            "useStatus": ""
        }, ]
    })
}
export const queryTreeLeafList = (para) => {
    let rows = treeList(para).rows
    Mock.mock('/tmes/mat/materialSort/queryMaterialSortList', {
        "rows": rows,
        "total": rows.length,
        "code": "200",
        "msg": "请求成功",
        "timestamp": "*************"
    })
}
export const addTreeLeaf = () => {
    Mock.mock('/tmes/mat/materialSort/insert', {
        "code": "200",
        "msg": "请求成功",
        "timestamp": '@datetime',
    })
}
export const updateTreeLeaf = (para) => {
    Mock.mock('/tmes/mat/materialSort/update', {
        "msg": "",
        "code": "200",
        "data": {
            "approveStatus": "",
            "createBy": "75",
            "createName": Cookies.get('account'),
            "createTime": '@datetime',
            "f1": "",
            "f2": "",
            "f3": "",
            "f4": "",
            "f5": "",
            "f6": "",
            "factoryId": "0",
            "keyword": "",
            "leaf": "1",
            "orderBy": "",
            "parentName": "",
            "root_flag": "N",
            "sortType": "MAT",
            "updateBy": "75",
            "updateName": Cookies.get('account'),
            "updateTime": '@datetime',
            "useStatus": "",
            ...para
        },
        "timestamp": "*************"
    })
}
export const deleteTreeLeaf = () => {
        Mock.mock('/tmes/mat/materialSort/delete', {
            "code": "200",
            "msg": "请求成功",
            "timestamp": '@datetime',
        })
    }
    // 分类模版管理 end

// 分类模版管理 start
export const mtList = (para) => {
    Mock.mock('/tmes/mat/materialSortTemplate/queryMaterialSortTemplateBySortId', {
        "rows": [{
                "approveStatus": "",
                "createName": "",
                "createTime": null,
                "dataLength": "255",
                "dataType": "String",
                "f1": "",
                "f2": "",
                "f3": "",
                "f4": "",
                "f5": "",
                "f6": "",
                "factoryId": "0",
                "fieldCode": "F1",
                "fieldName": "材质",
                "id": "40",
                "keyword": "",
                "orderBy": "",
                "plateType": "",
                "queueId": "0", // 排序
                "remark": "",
                "sortCode": "C0000",
                "sortId": para.sortId,
                "sortName": "化学品",
                "updateName": "",
                "updateTime": null,
                "useStatus": ""
            },
            {
                "approveStatus": "",
                "createName": "",
                "createTime": null,
                "dataLength": "255",
                "dataType": "string",
                "enable": "1",
                "f1": "",
                "f2": "",
                "f3": "",
                "f4": "",
                "f5": "",
                "f6": "",
                "factoryId": "0",
                "fieldCode": "F1",
                "fieldName": "啦啦啦",
                "id": "44",
                "keyword": "",
                "orderBy": "",
                "plateType": "",
                "queueId": "0",
                "remark": "",
                "sortCode": "C0000",
                "sortId": para.sortId,
                "sortName": "化学品",
                "updateName": "",
                "updateTime": null,
                "useStatus": ""
            },
            {
                "approveStatus": "",
                "createName": "",
                "createTime": null,
                "dataLength": "255",
                "dataType": "string",
                "enable": "1",
                "f1": "",
                "f2": "",
                "f3": "",
                "f4": "",
                "f5": "",
                "f6": "",
                "factoryId": "0",
                "fieldCode": "F1",
                "fieldName": "567啦啦啦",
                "id": "45",
                "keyword": "",
                "orderBy": "",
                "plateType": "",
                "queueId": "0",
                "remark": "",
                "sortCode": "C0000",
                "sortId": para.sortId,
                "sortName": "化学品",
                "updateName": "",
                "updateTime": null,
                "useStatus": ""
            },
            {
                "approveStatus": "",
                "createName": "",
                "createTime": null,
                "dataLength": "255",
                "dataType": "string",
                "enable": "1",
                "f1": "",
                "f2": "",
                "f3": "",
                "f4": "",
                "f5": "",
                "f6": "",
                "factoryId": "0",
                "fieldCode": "F1",
                "fieldName": "啦啦啦",
                "id": "46",
                "keyword": "",
                "orderBy": "",
                "plateType": "",
                "queueId": "0",
                "remark": "",
                "sortCode": "C0000",
                "sortId": para.sortId,
                "sortName": "化学品",
                "updateName": "",
                "updateTime": null,
                "useStatus": ""
            },
            {
                "approveStatus": "",
                "createName": "",
                "createTime": null,
                "dataLength": "255",
                "dataType": "String",
                "enable": "1",
                "f1": "",
                "f2": "",
                "f3": "",
                "f4": "",
                "f5": "",
                "f6": "",
                "factoryId": "0",
                "fieldCode": "F2",
                "fieldName": "单位",
                "id": "41",
                "keyword": "",
                "orderBy": "",
                "plateType": "",
                "queueId": "1",
                "remark": "",
                "sortCode": "C0000",
                "sortId": para.sortId,
                "sortName": "化学品",
                "updateName": "",
                "updateTime": null,
                "useStatus": ""
            },
            {
                "approveStatus": "",
                "createName": "",
                "createTime": null,
                "dataLength": "255",
                "dataType": "string",
                "enable": "1",
                "f1": "",
                "f2": "",
                "f3": "",
                "f4": "",
                "f5": "",
                "f6": "",
                "factoryId": "0",
                "fieldCode": "F1",
                "fieldName": "lalala",
                "id": "47",
                "keyword": "",
                "orderBy": "",
                "plateType": "",
                "queueId": "4",
                "remark": "",
                "sortCode": "C0000",
                "sortId": "113",
                "sortName": "化学品",
                "updateName": "",
                "updateTime": null,
                "useStatus": ""
            }
        ],
        "total": "6",
        "code": "200",
        "msg": "请求成功",
        "timestamp": "1689145827925"
    })
}
export const mtInsert = () => {
    Mock.mock('/tmes/mat/materialSortTemplate/insert', {
        "code": "200",
        "msg": "请求成功",
        "timestamp": '@datetime',
    })
}
export const mtUpdate = () => {
    Mock.mock('/tmes/mat/materialSortTemplate/update', {
        "code": "200",
        "msg": "请求成功",
        "timestamp": '@datetime',
    })
}

export const mtDelete = () => {
    Mock.mock('/tmes/mat/materialSortTemplate/delete', {
        "code": "200",
        "msg": "请求成功",
        "timestamp": '@datetime',
    })
}

export const mtChangeFieldSequence = (para) => {
        Mock.mock('/tmes/mat/materialSortTemplate/changeFieldSequence', {
            "code": "200",
            "msg": "请求成功",
            "timestamp": '@datetime',
        })
    }
    // 分类模版管理 end

// 物料信息管理 start
// 查询加载数据(加载树)


export const mrQuerySortList = () => {
    Mock.mock('/tmes/mat/materialRecord/queryMaterialSortList', {
        "rows": [{
                "approveStatus": "",
                "createName": "",
                "createTime": null,
                "f1": "",
                "f2": "",
                "f3": "",
                "f4": "",
                "f5": "",
                "f6": "",
                "factoryId": "0",
                "id": "110",
                "keyword": "",
                "leaf": "1",
                "orderBy": "",
                "parentName": "",
                "parentTreeLevel": "",
                "remark": "",
                "root_flag": "Y",
                "sortCode": "MAT0000",
                "sortName": "分类",
                "sortType": "MAT",
                "updateName": "",
                "updateTime": null,
                "useStatus": ""
            },
            {
                "approveStatus": "",
                "createName": "",
                "createTime": null,
                "f1": "",
                "f2": "",
                "f3": "",
                "f4": "",
                "f5": "",
                "f6": "",
                "factoryId": "0",
                "id": "111",
                "keyword": "",
                "leaf": "1",
                "orderBy": "",
                "parentId": "110",
                "parentName": "",
                "parentTreeLevel": "",
                "remark": "",
                "root_flag": "N",
                "sortCode": "A0000",
                "sortName": "标准件",
                "sortType": "MAT",
                "updateName": "",
                "updateTime": null,
                "useStatus": ""
            },
            {
                "approveStatus": "",
                "createName": "",
                "createTime": null,
                "f1": "",
                "f2": "",
                "f3": "",
                "f4": "",
                "f5": "",
                "f6": "",
                "factoryId": "0",
                "id": "112",
                "keyword": "",
                "leaf": "1",
                "orderBy": "",
                "parentId": "110",
                "parentName": "",
                "parentTreeLevel": "",
                "remark": "",
                "root_flag": "N",
                "sortCode": "B0000",
                "sortName": "日常用品",
                "sortType": "MAT",
                "updateName": "",
                "updateTime": null,
                "useStatus": ""
            },
            {
                "approveStatus": "",
                "createName": "",
                "createTime": null,
                "f1": "",
                "f2": "",
                "f3": "",
                "f4": "",
                "f5": "",
                "f6": "",
                "factoryId": "0",
                "id": "113",
                "keyword": "",
                "leaf": "1",
                "orderBy": "",
                "parentId": "110",
                "parentName": "",
                "parentTreeLevel": "",
                "remark": "",
                "root_flag": "N",
                "sortCode": "C0000",
                "sortName": "化学品",
                "sortType": "MAT",
                "updateName": "",
                "updateTime": null,
                "useStatus": ""
            },
            {
                "approveStatus": "",
                "createName": "",
                "createTime": null,
                "f1": "",
                "f2": "",
                "f3": "",
                "f4": "",
                "f5": "",
                "f6": "",
                "factoryId": "0",
                "id": "114",
                "keyword": "",
                "leaf": "1",
                "orderBy": "",
                "parentId": "111",
                "parentName": "",
                "parentTreeLevel": "",
                "remark": "",
                "root_flag": "N",
                "sortCode": "A0100",
                "sortName": "A",
                "sortType": "MAT",
                "updateName": "",
                "updateTime": null,
                "useStatus": ""
            },
            {
                "approveStatus": "",
                "createName": "",
                "createTime": null,
                "f1": "",
                "f2": "",
                "f3": "",
                "f4": "",
                "f5": "",
                "f6": "",
                "factoryId": "0",
                "id": "115",
                "keyword": "",
                "leaf": "1",
                "orderBy": "",
                "parentId": "114",
                "parentName": "",
                "parentTreeLevel": "",
                "remark": "",
                "root_flag": "N",
                "sortCode": "A0101",
                "sortName": "B",
                "sortType": "MAT",
                "updateName": "",
                "updateTime": null,
                "useStatus": ""
            },
            {
                "approveStatus": "",
                "createName": "",
                "createTime": null,
                "f1": "",
                "f2": "",
                "f3": "",
                "f4": "",
                "f5": "",
                "f6": "",
                "factoryId": "0",
                "id": "131",
                "keyword": "",
                "leaf": "1",
                "orderBy": "",
                "parentId": "111",
                "parentName": "",
                "parentTreeLevel": "",
                "remark": "",
                "root_flag": "N",
                "sortCode": "A0200",
                "sortName": "螺丝",
                "sortType": "MAT",
                "updateName": "",
                "updateTime": null,
                "useStatus": ""
            },
            {
                "approveStatus": "",
                "createName": "",
                "createTime": null,
                "f1": "",
                "f2": "",
                "f3": "",
                "f4": "",
                "f5": "",
                "f6": "",
                "factoryId": "0",
                "id": "132",
                "keyword": "",
                "leaf": "1",
                "orderBy": "",
                "parentId": "112",
                "parentName": "",
                "parentTreeLevel": "",
                "remark": "",
                "root_flag": "N",
                "sortCode": "B0100",
                "sortName": "纸张",
                "sortType": "MAT",
                "updateName": "",
                "updateTime": null,
                "useStatus": ""
            }
        ],
        "total": "8",
        "code": "200",
        "msg": "请求成功",
        "timestamp": "1689148850050"
    })
}

let mrTreeList = (para) => {
    return Mock.mock({
        "rows|1-6": [{
                "approveStatus": "init",
                "bomType": "",
                "createName": "管理员",
                "createTime": "2023-07-03 16:34:52",
                "f0": "",
                "f1": "8407",
                "f2": "KG",
                "f3": "",
                "f4": "",
                "f5": "",
                "f6": "",
                "f7": "",
                "f8": "",
                "factoryId": "0",
                "id": `${Mock.Random.integer(111,199)}`,
                "keyword": "",
                "materialCode": `${Mock.Random.character('upper')}${Mock.Random.integer(0,99999)}`,
                "materialName": '@cword(3, 8)',
                "materialSortId": para.materialSortId,
                "materialSortName": para.materialSortName,
                "orderBy": "",
                "parentName": "",
                "proInsId": "",
                "remark": '@cword(3, 20)',
                "updateName": "",
                "updateTime": null,
                "useStatus": "use"
            }

        ]
    })
}
export const mrQueryLeafList = (para) => {
    let rows = mrTreeList(para).rows
    Mock.mock(`/tmes/mat/materialRecord/queryMaterialRecordList?materialSortId=${para.materialSortId}`, {
        "rows": rows,
        "total": rows.length,
        "code": "200",
        "msg": "请求成功",
        "timestamp": "*************"
    })
}

export const mrInsert = () => {
    Mock.mock('/tmes/mat/materialRecord/insert', {
        "code": "200",
        "msg": "请求成功",
        "timestamp": '@datetime',
    })
}
export const mrUpdate = () => {
    Mock.mock('/tmes/mat/materialRecord/update', {
        "code": "200",
        "msg": "请求成功",
        "timestamp": '@datetime',
    })
}


export const mrDelete = () => {
        Mock.mock('/tmes/mat/materialRecord/delete', {
            "code": "200",
            "msg": "请求成功",
            "timestamp": '@datetime',
        })
    }
    // 物料信息管理 end
