import Mock from 'mockjs'

// 模拟maxKB AI助手的响应数据
const mockResponses = {
  normal: [
    '您好！我是AI智能助手，很高兴为您服务。我可以帮助您解答各种问题，协助您完成工作任务。请告诉我您需要什么帮助？',
    '我是基于先进的人工智能技术构建的智能助手，具备自然语言理解、知识检索、逻辑推理等能力。我可以：\n\n1. **回答问题** - 解答各种专业和生活问题\n2. **协助工作** - 帮助处理文档、分析数据、制定计划\n3. **学习指导** - 提供学习建议和资源推荐\n4. **创意支持** - 协助创作、设计和创新思考\n\n请随时向我提问！',
    '这个系统是一个现代化的企业管理平台，主要功能包括：\n\n## 核心模块\n- **企业档案管理** - 一企一档功能\n- **风险评估** - 智能风险分析\n- **合规监管** - 合规检查和记录\n- **数据分析** - 可视化数据展示\n\n## 技术特色\n- 响应式设计，支持多设备访问\n- 实时数据更新\n- 智能搜索和筛选\n- 安全可靠的数据保护\n\n您想了解哪个具体功能呢？',
    '我推荐以下学习资源：\n\n## 📚 在线学习平台\n- **慕课网** - 实战项目丰富\n- **极客时间** - 技术深度好\n- **B站** - 免费资源多\n\n## 📖 技术文档\n- **MDN** - Web开发必备\n- **Vue.js官方文档** - 前端框架学习\n- **GitHub** - 开源项目学习\n\n## 🛠️ 实践建议\n1. 多动手实践项目\n2. 参与开源贡献\n3. 技术社区交流\n\n需要具体某个技术栈的学习路径吗？'
  ],
  knowledge: [
    '根据知识库信息，系统主要包含以下功能模块：\n\n## 🏢 企业管理模块\n- 企业档案管理\n- 企业信息维护\n- 企业状态监控\n\n## 🔍 风险评估模块\n- 风险等级评定\n- 风险因素分析\n- 风险预警机制\n\n## 📋 合规管理模块\n- 合规检查记录\n- 整改跟踪\n- 合规报告生成\n\n## 📊 数据分析模块\n- 统计分析\n- 趋势预测\n- 可视化展示\n\n您想了解哪个模块的详细信息？',
    '企业管理流程如下：\n\n## 📝 企业档案建立\n1. **基础信息录入** - 企业名称、统一社会信用代码等\n2. **法人信息** - 法定代表人详细信息\n3. **责任人信息** - 企业责任人联系方式\n4. **业务信息** - 经营范围、注册资本等\n\n## 🔄 信息维护更新\n- 定期信息核查\n- 变更信息及时更新\n- 档案完整性检查\n\n## 📊 状态监控\n- 企业运营状态跟踪\n- 风险等级动态评估\n- 异常情况预警\n\n需要了解具体操作步骤吗？',
    '数据安全保障措施：\n\n## 🔐 技术保障\n- **数据加密** - 传输和存储全程加密\n- **访问控制** - 基于角色的权限管理\n- **审计日志** - 完整的操作记录\n\n## 🛡️ 管理保障\n- **备份策略** - 定期自动备份\n- **灾难恢复** - 快速恢复机制\n- **安全培训** - 定期安全意识培训\n\n## 📋 合规保障\n- **等保认证** - 符合国家等保要求\n- **隐私保护** - 严格的隐私保护政策\n- **安全审计** - 定期安全评估\n\n您对哪个方面的安全措施想了解更多？',
    '系统支持的主要操作包括：\n\n## 📊 数据操作\n- **查询检索** - 多条件组合查询\n- **数据导入** - 批量数据导入\n- **数据导出** - 多格式数据导出\n\n## 📝 信息管理\n- **新增记录** - 添加新的企业档案\n- **编辑修改** - 更新企业信息\n- **删除操作** - 安全删除机制\n\n## 📈 分析功能\n- **统计分析** - 多维度数据统计\n- **报表生成** - 自动生成各类报表\n- **趋势分析** - 数据趋势预测\n\n## ⚙️ 系统管理\n- **用户管理** - 用户权限配置\n- **系统配置** - 参数设置\n- **日志管理** - 操作日志查看\n\n需要具体操作指导吗？'
  ],
  analysis: [
    '根据当前数据分析，企业风险分布情况如下：\n\n## 📊 风险等级分布\n- **低风险企业**: 45% (712家)\n- **中风险企业**: 35% (548家)\n- **高风险企业**: 20% (304家)\n\n## 🎯 主要风险因素\n1. **合规风险** - 占比32%\n2. **经营风险** - 占比28%\n3. **技术风险** - 占比25%\n4. **其他风险** - 占比15%\n\n## 📈 趋势分析\n- 整体风险水平较上月下降3.2%\n- 高风险企业数量减少12家\n- 合规整改效果显著\n\n建议重点关注高风险企业的整改进度。',
    '## 📋 月度数据报告\n\n### 📊 核心指标\n- **企业总数**: 1,564家 (↑2.3%)\n- **新增企业**: 36家\n- **注销企业**: 8家\n- **活跃度**: 94.2%\n\n### 🔍 合规情况\n- **合规率**: 87.5% (↑1.8%)\n- **待整改**: 195项\n- **已整改**: 342项\n- **整改率**: 63.7%\n\n### 📈 增长趋势\n- 企业数量稳步增长\n- 合规水平持续提升\n- 风险控制效果良好\n\n### 💡 建议措施\n1. 加强新增企业审核\n2. 推进待整改项目\n3. 完善风险预警机制\n\n详细数据可查看附件报表。',
    '## 🗺️ 区域发展对比分析\n\n### 📍 各区域企业分布\n- **通州区**: 486家 (31.1%)\n- **朝阳区**: 423家 (27.0%)\n- **海淀区**: 387家 (24.7%)\n- **其他区域**: 268家 (17.2%)\n\n### 📊 发展指标对比\n| 区域 | 企业数量 | 增长率 | 合规率 | 风险等级 |\n|------|----------|--------|--------|---------|\n| 通州区 | 486 | +3.2% | 89.1% | 中等 |\n| 朝阳区 | 423 | +2.8% | 85.3% | 中等 |\n| 海淀区 | 387 | +4.1% | 91.2% | 较低 |\n\n### 🎯 发展特点\n- **通州区**: 数量最多，增长稳定\n- **海淀区**: 合规率最高，风险最低\n- **朝阳区**: 发展潜力大，需加强监管\n\n建议针对不同区域制定差异化管理策略。',
    '## 📈 合规率统计分析\n\n### 📊 总体合规情况\n- **整体合规率**: 87.5%\n- **较上月变化**: +1.8%\n- **年度目标**: 90%\n- **完成进度**: 97.2%\n\n### 🔍 分类合规率\n- **等保备案**: 92.3%\n- **网站备案**: 89.7%\n- **APP备案**: 85.1%\n- **小程序备案**: 83.4%\n\n### 📋 问题分析\n1. **主要问题**: 备案信息不完整\n2. **影响因素**: 政策理解不到位\n3. **改进方向**: 加强培训指导\n\n### 💡 提升建议\n- 定期开展合规培训\n- 建立合规检查机制\n- 完善奖惩制度\n- 提供技术支持\n\n预计下月合规率可达89.2%。',
    '## 📊 资产增长预测分析\n\n### 📈 历史增长趋势\n- **2023年**: 数字资产增长23.5%\n- **2024年Q1**: 增长6.8%\n- **2024年Q2**: 增长7.2%\n- **2024年Q3**: 增长8.1%\n\n### 🔮 预测模型\n基于机器学习算法分析：\n- **Q4预测增长**: 8.5%\n- **2025年预测**: 年增长率25-30%\n- **置信度**: 85%\n\n### 📊 增长驱动因素\n1. **政策支持** - 权重35%\n2. **技术进步** - 权重28%\n3. **市场需求** - 权重25%\n4. **投资增加** - 权重12%\n\n### 🎯 关键指标预测\n- **IP地址段**: 预计增长15%\n- **域名注册**: 预计增长20%\n- **IDC机房**: 预计增长12%\n\n建议提前做好资源配置和风险防控。'
  ]
}

// 生成随机响应
function getRandomResponse(mode = 'normal') {
  const responses = mockResponses[mode] || mockResponses.normal
  return responses[Math.floor(Math.random() * responses.length)]
}

// 模拟流式响应
function simulateStreamResponse(message, callback) {
  const response = getRandomResponse()
  const words = response.split('')
  let index = 0
  
  const interval = setInterval(() => {
    if (index < words.length) {
      callback({
        content: words[index],
        type: 'content'
      })
      index++
    } else {
      callback({
        type: 'done'
      })
      clearInterval(interval)
    }
  }, 50) // 每50ms发送一个字符
}

// Mock API
const mockApi = [
  // 发送消息
  {
    url: /\/api\/application\/.*\/chat$/,
    type: 'post',
    response: config => {
      const { message, conversation_id } = JSON.parse(config.body)
      
      // 根据消息内容判断模式
      let mode = 'normal'
      if (message.includes('知识库') || message.includes('系统') || message.includes('功能')) {
        mode = 'knowledge'
      } else if (message.includes('分析') || message.includes('数据') || message.includes('统计')) {
        mode = 'analysis'
      }
      
      return {
        code: 200,
        message: 'success',
        data: {
          content: getRandomResponse(mode),
          conversation_id: conversation_id || Mock.Random.guid(),
          message_id: Mock.Random.guid(),
          timestamp: new Date().toISOString()
        }
      }
    }
  },
  
  // 创建会话
  {
    url: /\/api\/application\/.*\/conversation$/,
    type: 'post',
    response: () => {
      return {
        code: 200,
        message: 'success',
        data: {
          id: Mock.Random.guid(),
          created_at: new Date().toISOString()
        }
      }
    }
  },
  
  // 获取会话历史
  {
    url: /\/api\/application\/.*\/conversation\/.*$/,
    type: 'get',
    response: () => {
      return {
        code: 200,
        message: 'success',
        data: {
          messages: []
        }
      }
    }
  },
  
  // 删除会话
  {
    url: /\/api\/application\/.*\/conversation\/.*$/,
    type: 'delete',
    response: () => {
      return {
        code: 200,
        message: 'success'
      }
    }
  },
  
  // 获取应用信息
  {
    url: /\/api\/application\/.*$/,
    type: 'get',
    response: () => {
      return {
        code: 200,
        message: 'success',
        data: {
          id: Mock.Random.guid(),
          name: 'AI智能助手',
          description: '基于maxKB构建的智能问答系统',
          status: 'active',
          created_at: new Date().toISOString()
        }
      }
    }
  },
  
  // 获取会话列表
  {
    url: /\/api\/application\/.*\/conversation$/,
    type: 'get',
    response: () => {
      return {
        code: 200,
        message: 'success',
        data: []
      }
    }
  }
]

// 注册mock接口 - 已禁用，允许真实的maxKB API调用
// mockApi.forEach(api => {
//   Mock.mock(api.url, api.type, api.response)
// })

console.log('%c[maxKB Mock] maxKB Mock拦截器已禁用，将使用真实的maxKB API', 'color: #FF6B6B; font-weight: bold;')

export default mockApi
