// 查询字段配置

import Mock from 'mockjs'
export const queryFactorySysOptionList = (params) => {
  console.log(params)
  let rowsObj = {
    ROUTE: [{
      "columnCode": "",
      "columnId": "0",
      "columnName": "",
      "createName": "",
      "createTime": null,
      "f0": "",
      "f1": "",
      "f2": "",
      "f3": "",
      "f4": "",
      "f5": "",
      "f6": "",
      "f7": "",
      "factoryId": "0",
      "id": "67",
      "keyword": "",
      "menuId": "0",
      "menuName": "",
      "optionCode": "铣",
      "optionName": "铣",
      "orderBy": "",
      "queueId": "0",
      "remark": "",
      "updateName": "",
      "updateTime": null
    },
      {
        "columnCode": "",
        "columnId": "0",
        "columnName": "",
        "createName": "",
        "createTime": null,
        "f0": "",
        "f1": "",
        "f2": "",
        "f3": "",
        "f4": "",
        "f5": "",
        "f6": "",
        "f7": "",
        "factoryId": "0",
        "id": "68",
        "keyword": "",
        "menuId": "0",
        "menuName": "",
        "optionCode": "车",
        "optionName": "车",
        "orderBy": "",
        "queueId": "0",
        "remark": "",
        "updateName": "",
        "updateTime": null
      },
      {
        "columnCode": "",
        "columnId": "0",
        "columnName": "",
        "createName": "",
        "createTime": null,
        "f0": "",
        "f1": "",
        "f2": "",
        "f3": "",
        "f4": "",
        "f5": "",
        "f6": "",
        "f7": "",
        "factoryId": "0",
        "id": "69",
        "keyword": "",
        "menuId": "0",
        "menuName": "",
        "optionCode": "钳",
        "optionName": "钳",
        "orderBy": "",
        "queueId": "0",
        "remark": "",
        "updateName": "",
        "updateTime": null
      },
      {
        "columnCode": "",
        "columnId": "0",
        "columnName": "",
        "createName": "",
        "createTime": null,
        "f0": "",
        "f1": "",
        "f2": "",
        "f3": "",
        "f4": "",
        "f5": "",
        "f6": "",
        "f7": "",
        "factoryId": "0",
        "id": "101",
        "keyword": "",
        "menuId": "0",
        "menuName": "",
        "optionCode": "ST0021",
        "optionName": "工站1",
        "orderBy": "",
        "queueId": "0",
        "remark": "",
        "updateName": "",
        "updateTime": null
      },
      {
        "columnCode": "",
        "columnId": "0",
        "columnName": "",
        "createName": "",
        "createTime": null,
        "f0": "",
        "f1": "",
        "f2": "",
        "f3": "",
        "f4": "",
        "f5": "",
        "f6": "",
        "f7": "",
        "factoryId": "0",
        "id": "102",
        "keyword": "",
        "menuId": "0",
        "menuName": "",
        "optionCode": "ST0022",
        "optionName": "工站2",
        "orderBy": "",
        "queueId": "0",
        "remark": "",
        "updateName": "",
        "updateTime": null
      },
      {
        "columnCode": "",
        "columnId": "0",
        "columnName": "",
        "createName": "",
        "createTime": null,
        "f0": "",
        "f1": "",
        "f2": "",
        "f3": "",
        "f4": "",
        "f5": "",
        "f6": "",
        "f7": "",
        "factoryId": "0",
        "id": "103",
        "keyword": "",
        "menuId": "0",
        "menuName": "",
        "optionCode": "ST0023",
        "optionName": "工站3",
        "orderBy": "",
        "queueId": "0",
        "remark": "",
        "updateName": "",
        "updateTime": null
      },
      {
        "columnCode": "",
        "columnId": "0",
        "columnName": "",
        "createName": "",
        "createTime": null,
        "f0": "",
        "f1": "",
        "f2": "",
        "f3": "",
        "f4": "",
        "f5": "",
        "f6": "",
        "f7": "",
        "factoryId": "0",
        "id": "104",
        "keyword": "",
        "menuId": "0",
        "menuName": "",
        "optionCode": "ST0024",
        "optionName": "工站4",
        "orderBy": "",
        "queueId": "0",
        "remark": "",
        "updateName": "",
        "updateTime": null
      },
      {
        "columnCode": "",
        "columnId": "0",
        "columnName": "",
        "createName": "",
        "createTime": null,
        "f0": "",
        "f1": "",
        "f2": "",
        "f3": "",
        "f4": "",
        "f5": "",
        "f6": "",
        "f7": "",
        "factoryId": "0",
        "id": "105",
        "keyword": "",
        "menuId": "0",
        "menuName": "",
        "optionCode": "ST0025",
        "optionName": "工站5",
        "orderBy": "",
        "queueId": "0",
        "remark": "",
        "updateName": "",
        "updateTime": null
      },
      {
        "columnCode": "",
        "columnId": "0",
        "columnName": "",
        "createName": "",
        "createTime": null,
        "f0": "",
        "f1": "",
        "f2": "",
        "f3": "",
        "f4": "",
        "f5": "",
        "f6": "",
        "f7": "",
        "factoryId": "0",
        "id": "106",
        "keyword": "",
        "menuId": "0",
        "menuName": "",
        "optionCode": "ST0026",
        "optionName": "工站6",
        "orderBy": "",
        "queueId": "0",
        "remark": "",
        "updateName": "",
        "updateTime": null
      },
      {
        "columnCode": "",
        "columnId": "0",
        "columnName": "",
        "createName": "",
        "createTime": null,
        "f0": "",
        "f1": "",
        "f2": "",
        "f3": "",
        "f4": "",
        "f5": "",
        "f6": "",
        "f7": "",
        "factoryId": "0",
        "id": "107",
        "keyword": "",
        "menuId": "0",
        "menuName": "",
        "optionCode": "ST0027",
        "optionName": "工站7",
        "orderBy": "",
        "queueId": "0",
        "remark": "",
        "updateName": "",
        "updateTime": null
      }
    ],
    LINE: [{
      "columnCode": "",
      "columnId": "0",
      "columnName": "",
      "createName": "",
      "createTime": null,
      "f0": "0",
      "f1": "",
      "f2": "",
      "f3": "",
      "f4": "",
      "f5": "",
      "f6": "",
      "f7": "",
      "factoryId": "0",
      "id": "3",
      "keyword": "",
      "menuId": "0",
      "menuName": "",
      "optionCode": "5",
      "optionName": "1号线",
      "orderBy": "",
      "queueId": "0",
      "remark": "",
      "updateName": "",
      "updateTime": null
    },
      {
        "columnCode": "",
        "columnId": "0",
        "columnName": "",
        "createName": "",
        "createTime": null,
        "f0": "",
        "f1": "",
        "f2": "",
        "f3": "",
        "f4": "",
        "f5": "",
        "f6": "",
        "f7": "",
        "factoryId": "0",
        "id": "86",
        "keyword": "",
        "menuId": "0",
        "menuName": "",
        "optionCode": "6",
        "optionName": "2号线",
        "orderBy": "",
        "queueId": "0",
        "remark": "",
        "updateName": "",
        "updateTime": null
      },
      {
        "columnCode": "",
        "columnId": "0",
        "columnName": "",
        "createName": "",
        "createTime": null,
        "f0": "",
        "f1": "",
        "f2": "",
        "f3": "",
        "f4": "",
        "f5": "",
        "f6": "",
        "f7": "",
        "factoryId": "0",
        "id": "96",
        "keyword": "",
        "menuId": "0",
        "menuName": "",
        "optionCode": "3",
        "optionName": "3号线",
        "orderBy": "",
        "queueId": "0",
        "remark": "",
        "updateName": "",
        "updateTime": null
      }
    ],
    DEVICE: [{
      "columnCode": "",
      "columnId": "0",
      "columnName": "",
      "createName": "",
      "createTime": null,
      "f0": "0",
      "f1": "",
      "f2": "",
      "f3": "",
      "f4": "",
      "f5": "",
      "f6": "",
      "f7": "",
      "factoryId": "0",
      "id": "26",
      "keyword": "",
      "menuId": "0",
      "menuName": "",
      "optionCode": "95",
      "optionName": "95",
      "orderBy": "",
      "queueId": "0",
      "remark": "",
      "updateName": "",
      "updateTime": null
    },
      {
        "columnCode": "",
        "columnId": "0",
        "columnName": "",
        "createName": "",
        "createTime": null,
        "f0": "0",
        "f1": "",
        "f2": "",
        "f3": "",
        "f4": "",
        "f5": "",
        "f6": "",
        "f7": "",
        "factoryId": "0",
        "id": "29",
        "keyword": "",
        "menuId": "0",
        "menuName": "",
        "optionCode": "96",
        "optionName": "96",
        "orderBy": "",
        "queueId": "0",
        "remark": "",
        "updateName": "",
        "updateTime": null
      },
      {
        "columnCode": "",
        "columnId": "0",
        "columnName": "",
        "createName": "",
        "createTime": null,
        "f0": "0",
        "f1": "",
        "f2": "",
        "f3": "",
        "f4": "",
        "f5": "",
        "f6": "",
        "f7": "",
        "factoryId": "0",
        "id": "62",
        "keyword": "",
        "menuId": "0",
        "menuName": "",
        "optionCode": "97",
        "optionName": "97",
        "orderBy": "",
        "queueId": "0",
        "remark": "",
        "updateName": "",
        "updateTime": null
      },
      {
        "columnCode": "",
        "columnId": "0",
        "columnName": "",
        "createName": "",
        "createTime": null,
        "f0": "",
        "f1": "",
        "f2": "",
        "f3": "",
        "f4": "",
        "f5": "",
        "f6": "",
        "f7": "",
        "factoryId": "0",
        "id": "63",
        "keyword": "",
        "menuId": "0",
        "menuName": "",
        "optionCode": "98",
        "optionName": "98",
        "orderBy": "",
        "queueId": "0",
        "remark": "",
        "updateName": "",
        "updateTime": null
      },
      {
        "columnCode": "",
        "columnId": "0",
        "columnName": "",
        "createName": "",
        "createTime": null,
        "f0": "",
        "f1": "",
        "f2": "",
        "f3": "",
        "f4": "",
        "f5": "",
        "f6": "",
        "f7": "",
        "factoryId": "0",
        "id": "64",
        "keyword": "",
        "menuId": "0",
        "menuName": "",
        "optionCode": "99",
        "optionName": "99",
        "orderBy": "",
        "queueId": "0",
        "remark": "",
        "updateName": "",
        "updateTime": null
      },
      {
        "columnCode": "",
        "columnId": "0",
        "columnName": "",
        "createName": "",
        "createTime": null,
        "f0": "",
        "f1": "",
        "f2": "",
        "f3": "",
        "f4": "",
        "f5": "",
        "f6": "",
        "f7": "",
        "factoryId": "0",
        "id": "65",
        "keyword": "",
        "menuId": "0",
        "menuName": "",
        "optionCode": "100",
        "optionName": "100",
        "orderBy": "",
        "queueId": "0",
        "remark": "",
        "updateName": "",
        "updateTime": null
      },
      {
        "columnCode": "",
        "columnId": "0",
        "columnName": "",
        "createName": "",
        "createTime": null,
        "f0": "0",
        "f1": "",
        "f2": "",
        "f3": "",
        "f4": "",
        "f5": "",
        "f6": "",
        "f7": "",
        "factoryId": "0",
        "id": "66",
        "keyword": "",
        "menuId": "0",
        "menuName": "",
        "optionCode": "101",
        "optionName": "101",
        "orderBy": "",
        "queueId": "0",
        "remark": "",
        "updateName": "",
        "updateTime": null
      }
    ],
    WORKER: [],

  }
  // console.log(rowsObj[params.columnCode])
  let optionData = rowsObj[params.columnCode]
  Mock.mock('/option/value/queryFactorySysOptionList', {
    "msg": "",
    "total": rowsObj[params.columnCode].length,
    "code": "200",
    "rows": optionData,
    "timestamp": "1689065087228"
  })
}

export const action = (action, method, params) => {
  Mock.mock(action, {
    "rows": [{
      "approveStatus": "",
      "centerCode": "6",
      "centerName": "2号线",
      "createBy": "75",
      "createName": "695前端开发",
      "createTime": "2023-06-09 15:13:07",
      "createTime_end": null,
      "createTime_start": null,
      "deviceCode": "5",
      "doHours": "3.0",
      "doQty": "3",
      "empCode": "",
      "empName": "",
      "enable": "1",
      "f0": "",
      "f1": "",
      "f2": "",
      "f3": "",
      "f4": "",
      "f5": "",
      "f6": "",
      "f7": "",
      "f8": "",
      "factoryId": "0",
      "finishTime": null,
      "flowId": "2",
      "id": "611",
      "keyword": "",
      "materialCode": "111",
      "materialItera": "",
      "materialName": "www",
      "materialVer": "",
      "orderBy": "",
      "orderCode": "G230609001",
      "orderPriority": "3",
      "orderStatus": "INIT",
      "orderTime": "2023-06-09 00:00:00",
      "orderType": "NEW",
      "planFinish": null,
      "planStart": null,
      "ppOrderCode": "100107203-0008",
      "remark": "1234567890",
      "requestTime": "2023-06-23 00:00:00",
      "routeCode": "铣",
      "routeName": "铣",
      "startTime": null,
      "typeName": "新制",
      "updateBy": "75",
      "updateName": "695前端开发",
      "updateTime": "2023-06-09 15:13:07",
      "useStatus": ""
    },
      {
        "approveStatus": "",
        "centerCode": "5",
        "centerName": "1号线",
        "createBy": "73",
        "createName": "一号线",
        "createTime": "2023-05-31 09:39:00",
        "createTime_end": null,
        "createTime_start": null,
        "deviceCode": "95",
        "doHours": "1.1",
        "doQty": "100",
        "empCode": "",
        "empName": "",
        "f0": "",
        "f1": "",
        "f2": "",
        "f3": "",
        "f4": "",
        "f5": "",
        "f6": "100",
        "f7": "100",
        "f8": "4",
        "factoryId": "0",
        "finishTime": null,
        "flowId": "20",
        "id": "581",
        "keyword": "",
        "materialCode": "3070100069",
        "materialItera": "",
        "materialName": "盒盖",
        "materialVer": "",
        "orderBy": "",
        "orderCode": "100107202-0010",
        "orderPriority": "3",
        "orderStatus": "ONGO",
        "orderTime": "2023-05-31 09:00:00",
        "orderType": "NEW",
        "planFinish": null,
        "planStart": null,
        "ppOrderCode": "",
        "remark": "",
        "requestTime": "2023-06-10 23:00:00",
        "routeCode": "铣",
        "routeName": "铣",
        "startTime": "2023-06-02 16:46:09",
        "typeName": "新制",
        "updateBy": "75",
        "updateName": "695前端开发",
        "updateTime": "2023-06-08 10:36:44",
        "useStatus": ""
      },
      {
        "approveStatus": "",
        "centerCode": "5",
        "centerName": "1号线",
        "createBy": "73",
        "createName": "一号线",
        "createTime": "2023-05-29 18:53:18",
        "createTime_end": null,
        "createTime_start": null,
        "deviceCode": "98",
        "doHours": "1.1",
        "doQty": "100",
        "empCode": "",
        "empName": "",
        "f0": "",
        "f1": "",
        "f2": "",
        "f3": "",
        "f4": "",
        "f5": "",
        "f6": "100",
        "f7": "100",
        "f8": "55",
        "factoryId": "0",
        "finishTime": null,
        "flowId": "40",
        "id": "579",
        "keyword": "",
        "materialCode": "3070100071",
        "materialItera": "",
        "materialName": "电磁铁保护罩",
        "materialVer": "",
        "orderBy": "",
        "orderCode": "100107203-0011",
        "orderPriority": "3",
        "orderStatus": "ONGO",
        "orderTime": "2023-05-29 18:50:00",
        "orderType": "NEW",
        "planFinish": null,
        "planStart": null,
        "ppOrderCode": "",
        "remark": "",
        "requestTime": "2023-06-20 18:50:00",
        "routeCode": "铣",
        "routeName": "铣",
        "startTime": "2023-05-30 13:20:07",
        "typeName": "新制",
        "updateBy": "73",
        "updateName": "一号线",
        "updateTime": "2023-05-30 13:20:07",
        "useStatus": ""
      },
      {
        "approveStatus": "",
        "centerCode": "5",
        "centerName": "1号线",
        "createBy": "73",
        "createName": "一号线",
        "createTime": "2023-05-25 14:17:09",
        "createTime_end": null,
        "createTime_start": null,
        "deviceCode": "99",
        "doHours": "1.1",
        "doQty": "20",
        "empCode": "",
        "empName": "",
        "f0": "",
        "f1": "",
        "f2": "",
        "f3": "",
        "f4": "",
        "f5": "",
        "f6": "20",
        "f7": "20",
        "f8": "",
        "factoryId": "0",
        "finishTime": null,
        "flowId": "20",
        "id": "577",
        "keyword": "",
        "materialCode": "3070100069",
        "materialItera": "",
        "materialName": "盒盖",
        "materialVer": "",
        "orderBy": "",
        "orderCode": "100107202-006",
        "orderPriority": "3",
        "orderStatus": "PLAN",
        "orderTime": "2023-05-25 14:10:00",
        "orderType": "NEW",
        "planFinish": null,
        "planStart": null,
        "ppOrderCode": "",
        "remark": "",
        "requestTime": "2023-06-10 14:10:00",
        "routeCode": "铣",
        "routeName": "铣",
        "startTime": null,
        "typeName": "新制",
        "updateBy": "73",
        "updateName": "一号线",
        "updateTime": "2023-05-25 14:17:40",
        "useStatus": ""
      },
      {
        "approveStatus": "",
        "centerCode": "5",
        "centerName": "1号线",
        "createBy": "73",
        "createName": "一号线",
        "createTime": "2023-05-22 13:08:44",
        "createTime_end": null,
        "createTime_start": null,
        "deviceCode": "101",
        "doHours": "1.1",
        "doQty": "100",
        "empCode": "",
        "empName": "",
        "f0": "",
        "f1": "",
        "f2": "",
        "f3": "",
        "f4": "",
        "f5": "",
        "f6": "100",
        "f7": "100",
        "f8": "72",
        "factoryId": "0",
        "finishTime": null,
        "flowId": "20",
        "id": "576",
        "keyword": "",
        "materialCode": "3070100071",
        "materialItera": "",
        "materialName": "电磁铁保护罩",
        "materialVer": "",
        "orderBy": "",
        "orderCode": "100107203-0010",
        "orderPriority": "3",
        "orderStatus": "ONGO",
        "orderTime": "2023-05-22 13:00:00",
        "orderType": "NEW",
        "planFinish": null,
        "planStart": null,
        "ppOrderCode": "",
        "remark": "",
        "requestTime": "2023-06-10 13:00:00",
        "routeCode": "铣",
        "routeName": "铣",
        "startTime": "2023-05-23 17:09:05",
        "typeName": "新制",
        "updateBy": "73",
        "updateName": "一号线",
        "updateTime": "2023-05-23 17:09:05",
        "useStatus": ""
      },
      {
        "approveStatus": "",
        "centerCode": "5",
        "centerName": "1号线",
        "createBy": "67",
        "createName": "赵硕",
        "createTime": "2023-05-11 01:01:14",
        "createTime_end": null,
        "createTime_start": null,
        "deviceCode": "98",
        "doHours": "1.2",
        "doQty": "100",
        "empCode": "",
        "empName": "",
        "f0": "",
        "f1": "",
        "f2": "",
        "f3": "",
        "f4": "",
        "f5": "",
        "f6": "100",
        "f7": "100",
        "f8": "99",
        "factoryId": "0",
        "finishTime": null,
        "flowId": "40",
        "id": "575",
        "keyword": "",
        "materialCode": "3070100071",
        "materialItera": "",
        "materialName": "电磁铁保护罩",
        "materialVer": "",
        "orderBy": "",
        "orderCode": "100107203-000",
        "orderPriority": "3",
        "orderStatus": "ONGO",
        "orderTime": "2023-05-11 08:00:00",
        "orderType": "NEW",
        "planFinish": null,
        "planStart": null,
        "ppOrderCode": "",
        "remark": "",
        "requestTime": "2023-05-20 23:55:00",
        "routeCode": "铣",
        "routeName": "铣",
        "startTime": "2023-05-11 10:13:12",
        "typeName": "新制",
        "updateBy": "67",
        "updateName": "赵硕",
        "updateTime": "2023-05-11 10:13:12",
        "useStatus": ""
      },
      {
        "approveStatus": "",
        "centerCode": "5",
        "centerName": "1号线",
        "createBy": "67",
        "createName": "赵硕",
        "createTime": "2023-04-23 19:40:38",
        "createTime_end": null,
        "createTime_start": null,
        "deviceCode": "96",
        "doHours": "2.0",
        "doQty": "100",
        "empCode": "",
        "empName": "",
        "f0": "",
        "f1": "",
        "f2": "",
        "f3": "",
        "f4": "",
        "f5": "",
        "f6": "100",
        "f7": "100",
        "f8": "37",
        "factoryId": "0",
        "finishTime": null,
        "flowId": "20",
        "id": "568",
        "keyword": "",
        "materialCode": "3070100069",
        "materialItera": "",
        "materialName": "盒盖",
        "materialVer": "",
        "orderBy": "",
        "orderCode": "100107202-0007",
        "orderPriority": "3",
        "orderStatus": "ONGO",
        "orderTime": "2023-04-23 20:05:00",
        "orderType": "NEW",
        "planFinish": null,
        "planStart": null,
        "ppOrderCode": "",
        "remark": "",
        "requestTime": "2023-05-02 23:35:00",
        "routeCode": "铣",
        "routeName": "铣",
        "startTime": "2023-04-24 10:02:38",
        "typeName": "新制",
        "updateBy": "71",
        "updateName": "何慧钧",
        "updateTime": "2023-04-24 10:02:38",
        "useStatus": ""
      },
      {
        "approveStatus": "",
        "centerCode": "5",
        "centerName": "1号线",
        "createBy": "67",
        "createName": "赵硕",
        "createTime": "2023-04-21 09:00:13",
        "createTime_end": null,
        "createTime_start": null,
        "deviceCode": "97",
        "doHours": "1.5",
        "doQty": "100",
        "empCode": "",
        "empName": "",
        "f0": "",
        "f1": "",
        "f2": "",
        "f3": "",
        "f4": "",
        "f5": "",
        "f6": "100",
        "f7": "100",
        "f8": "66",
        "factoryId": "0",
        "finishTime": null,
        "flowId": "20",
        "id": "567",
        "keyword": "",
        "materialCode": "3070100071",
        "materialItera": "",
        "materialName": "电磁铁保护罩",
        "materialVer": "",
        "orderBy": "",
        "orderCode": "100107203-0009",
        "orderPriority": "3",
        "orderStatus": "ONGO",
        "orderTime": "2023-04-21 10:10:00",
        "orderType": "NEW",
        "planFinish": null,
        "planStart": null,
        "ppOrderCode": "",
        "remark": "",
        "requestTime": "2023-05-04 23:55:00",
        "routeCode": "铣",
        "routeName": "铣",
        "startTime": "2023-04-21 20:49:10",
        "typeName": "新制",
        "updateBy": "67",
        "updateName": "赵硕",
        "updateTime": "2023-04-21 20:49:10",
        "useStatus": ""
      },
      {
        "approveStatus": "",
        "centerCode": "5",
        "centerName": "1号线",
        "createBy": "67",
        "createName": "赵硕",
        "createTime": "2023-04-21 08:55:40",
        "createTime_end": null,
        "createTime_start": null,
        "deviceCode": "96",
        "doHours": "2.0",
        "doQty": "50",
        "empCode": "",
        "empName": "",
        "f0": "",
        "f1": "",
        "f2": "",
        "f3": "",
        "f4": "",
        "f5": "",
        "f6": "10",
        "f7": "10",
        "f8": "10",
        "factoryId": "0",
        "finishTime": null,
        "flowId": "20",
        "id": "566",
        "keyword": "",
        "materialCode": "3070100069",
        "materialItera": "",
        "materialName": "盒盖",
        "materialVer": "",
        "orderBy": "",
        "orderCode": "100107202-003",
        "orderPriority": "3",
        "orderStatus": "ONGO",
        "orderTime": "2023-04-21 10:50:00",
        "orderType": "NEW",
        "planFinish": null,
        "planStart": null,
        "ppOrderCode": "",
        "remark": "",
        "requestTime": "2023-04-30 23:50:00",
        "routeCode": "铣",
        "routeName": "铣",
        "startTime": "2023-04-22 08:53:37",
        "typeName": "新制",
        "updateBy": "67",
        "updateName": "赵硕",
        "updateTime": "2023-04-22 08:53:37",
        "useStatus": ""
      },
      {
        "approveStatus": "",
        "centerCode": "5",
        "centerName": "1号线",
        "createBy": "67",
        "createName": "赵硕",
        "createTime": "2023-04-20 14:20:58",
        "createTime_end": null,
        "createTime_start": null,
        "deviceCode": "99",
        "doHours": "1.5",
        "doQty": "50",
        "empCode": "",
        "empName": "",
        "f0": "",
        "f1": "",
        "f2": "",
        "f3": "",
        "f4": "",
        "f5": "",
        "f6": "50",
        "f7": "50",
        "f8": "49",
        "factoryId": "0",
        "finishTime": null,
        "flowId": "20",
        "id": "564",
        "keyword": "",
        "materialCode": "3070100069",
        "materialItera": "",
        "materialName": "盒盖",
        "materialVer": "",
        "orderBy": "",
        "orderCode": "100107202-005",
        "orderPriority": "3",
        "orderStatus": "ONGO",
        "orderTime": "2023-04-21 10:05:00",
        "orderType": "NEW",
        "planFinish": null,
        "planStart": null,
        "ppOrderCode": "",
        "remark": "",
        "requestTime": "2023-04-28 23:10:00",
        "routeCode": "铣",
        "routeName": "铣",
        "startTime": "2023-04-21 12:41:27",
        "typeName": "新制",
        "updateBy": "67",
        "updateName": "赵硕",
        "updateTime": "2023-04-21 12:41:27",
        "useStatus": ""
      }
    ],
    "current": "1",
    "size": "10",
    "pages": "1",
    "total": "10",
    "code": "200",
    "msg": "请求成功",
    "timestamp": "1689068061513"
  })
}
