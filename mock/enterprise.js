// 获取企业列表数据
export const getEnterpriseList = (categoryType = 'all') => {
  console.log('Mock API: getEnterpriseList', categoryType)

  // 模拟不同分类的企业数量
  const totalCount = 1564
  const screenCount = 564
  const securityCount = 364
  const websiteCount = 264
  const operatorCount = 464
  const netbarCount = 264
  const nonbusinessCount = 44

  // 根据分类类型返回不同的数据量
  let count = totalCount
  switch(categoryType) {
    case 'screen': count = screenCount; break
    case 'security': count = securityCount; break
    case 'website': count = websiteCount; break
    case 'operator': count = operatorCount; break
    case 'netbar': count = netbarCount; break
    case 'nonbusiness': count = nonbusinessCount; break
    case 'other': count = totalCount - screenCount - securityCount - websiteCount - operatorCount - netbarCount - nonbusinessCount; break
  }

  // 生成模拟数据
  const mockData = []
  for (let i = 1; i <= count; i++) {
    // 根据索引生成不同的分类属性
    const hasElectronicScreen = categoryType === 'all' ? i % 3 === 0 : categoryType === 'screen'
    const hasSecurityRecord = categoryType === 'all' ? i % 5 === 0 : categoryType === 'security'
    const hasWebsiteRecord = categoryType === 'all' ? i % 6 === 0 : categoryType === 'website'
    const hasOperator = categoryType === 'all' ? i % 4 === 0 : categoryType === 'operator'
    const hasNetbar = categoryType === 'all' ? i % 7 === 0 : categoryType === 'netbar'
    const isNonBusiness = categoryType === 'all' ? i % 36 === 0 : categoryType === 'nonbusiness'

    // 生成随机的风险级别标签
    const riskLevels = generateRandomRiskLevels()

    mockData.push({
      id: i,
      name: `企业${i}`,
      creditCode: `9144030071526726X${i % 10}`,
      creditCodeLabel: '统一社会信用编码:',
      industry: ['制造业', '服务业', '建筑业', '金融业', '其他'][i % 5], // 隐藏此字段
      policeRecordNumber: `京公网安备1101${i.toString().padStart(4, '0')}号`,
      scale: ['大型', '中型', '小型', '微型'][i % 4],
      riskLevels,
      region: ['通州区', '朝阳区', '海淀区', '丰台区', '其他'][i % 5],
      legalPerson: `张${i % 10 + 1}`,
      legalPersonLabel: '法人及联系电话:',
      contactPhone: `1888888${i.toString().padStart(4, '0')}`,
      companyType: '公司',
      companyTypeLabel: '公司性质:',
      manager: `张${i % 10 + 1}`,
      managerLabel: '负责人及联系电话:',
      managerPhone: `1888888${i.toString().padStart(4, '0')}`,
      status: i % 3 === 0 ? '无效' : '有效',
      state: i % 3 === 0 ? 'invalid' : 'valid',
      statusLabel: '状态:',
      relatedOthers: i % 5 + 1,
      relatedOthersLabel: '其他:',
      address: `北京市${['通州区', '朝阳区', '海淀区', '丰台区', '其他'][i % 5]}观音庵南街1号院1号楼22层2181${i % 10}`,
      addressLabel: '单位注册地址:',
      // 分类属性
      hasElectronicScreen,
      hasSecurityRecord,
      hasWebsiteRecord,
      hasOperator,
      hasNetbar,
      isNonBusiness,
      // 额外信息
      establishDate: `201${i % 10}-0${(i % 12) + 1}-${(i % 28) + 1}`,
      registeredCapital: `${(i % 10) * 100 + 100}万元`,
      businessScope: '计算机软硬件技术开发、技术转让、技术咨询、技术服务；数据处理；应用软件服务；销售计算机软硬件及辅助设备、电子产品、通讯设备。',
      riskScore: Math.floor(Math.random() * 100),
      // 企业照片和营业执照照片
      photo: i % 5 === 0 ? null : `https://picsum.photos/id/${i + 100}/200/200`,
      licensePhoto: i % 4 === 0 ? null : `https://picsum.photos/id/${i + 200}/300/400`
    })
  }

  return mockData
}

// 获取企业详情
export const getEnterpriseDetail = (id) => {
  // 获取所有企业数据
  const allEnterprises = getEnterpriseList('all')

  // 查找对应ID的企业
  const enterprise = allEnterprises.find(item => item.id === id) || allEnterprises[0]

  return enterprise
}

// 生成随机的风险级别标签
export function generateRandomRiskLevels() {
  const riskLevelOptions = [
    { value: '1', label: '网吧单位', tagColor: '#FB6B2A' },
    { value: '2', label: '电子屏单位', tagColor: '#60B8FF' },
    { value: '3', label: '等保备案单位', tagColor: '#44C991' },
    { value: '4', label: '网站备案单位', tagColor: '#F5BC6C' },
    { value: '5', label: '运营商单位', tagColor: '#24A8BB' },
    { value: '6', label: '非经营单位', tagColor: '#D789D4' },
    { value: '7', label: '其他', tagColor: '#95ABD4' }
  ]

  const levels = []
  const count = Math.floor(Math.random() * 7) + 1 // 1-7个标签

  // 创建一个包含所有可能值的数组
  const allValues = riskLevelOptions.map(option => option.value)

  // 随机选择count个不重复的值
  for (let i = 0; i < count; i++) {
    if (allValues.length === 0) break

    const randomIndex = Math.floor(Math.random() * allValues.length)
    levels.push(allValues[randomIndex])
    allValues.splice(randomIndex, 1) // 移除已选择的值，确保不重复
  }

  return levels
}

// 添加企业
export const addEnterprise = () => {
  return null
}

// 更新企业
export const updateEnterprise = () => {
  return null
}

// 删除企业
export const deleteEnterprise = () => {
  return null
}

// 批量删除企业
export const batchDeleteEnterprise = () => {
  return null
}

// 获取企业风险级别选项
export const getRiskLevelOptions = () => {
  const riskLevelOptions = [
    { value: '1', label: '网吧单位', tagColor: '#FB6B2A' },
    { value: '2', label: '电子屏单位', tagColor: '#60B8FF' },
    { value: '3', label: '等保备案单位', tagColor: '#44C991' },
    { value: '4', label: '网站备案单位', tagColor: '#F5BC6C' },
    { value: '5', label: '运营商单位', tagColor: '#24A8BB' },
    { value: '6', label: '非经营单位', tagColor: '#D789D4' },
    { value: '7', label: '其他', tagColor: '#95ABD4' }
  ]
  return riskLevelOptions
}

// 获取企业分类统计数据
export const getEnterpriseCategoryStats = () => {
  const categoryStats = [
    { id: 'all', name: '全部单位单位', count: 1564, color: '#409EFF', icon: 'el-icon-office-building' },
    { id: 'screen', name: '电子屏单位', count: 564, color: '#60B8FF', icon: 'el-icon-monitor' },
    { id: 'security', name: '等保备案单位', count: 364, color: '#44C991', icon: 'el-icon-lock' },
    { id: 'website', name: '网站备案单位', count: 264, color: '#F5BC6C', icon: 'el-icon-link' },
    { id: 'operator', name: '运营商单位', count: 464, color: '#24A8BB', icon: 'el-icon-connection' },
    { id: 'netbar', name: '网吧单位', count: 264, color: '#FB6B2A', icon: 'el-icon-mouse' },
    { id: 'nonbusiness', name: '非经营单位', count: 44, color: '#D789D4', icon: 'el-icon-document' },
    { id: 'other', name: '其他', count: 1564 - 564 - 364 - 264 - 464 - 264 - 44, color: '#95ABD4', icon: 'el-icon-more' }
  ]

  return categoryStats
}
