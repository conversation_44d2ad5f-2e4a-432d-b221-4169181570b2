import Mock from 'mockjs'
// import { path } from '@/api/apiHost.js'
// import { USER_API_URL } from '@/api/modules/user'
import * as util from '@/utils/index'
let orderStatus = ['INIT', 'TODO', 'WAIT', 'RECEIVE', 'ASSIGN', 'ONGO', 'PAUSE', 'DONE']
export const fetch_ppWorkOrderList = () => {
        Mock.mock('/tmes/pp/ppWorkOrder/read/list', {
            "rows": [{
                    "approveStatus": "",
                    "centerCode": "6",
                    "centerName": "2号线",
                    "createBy": "75",
                    "createName": "695前端开发",
                    "createTime": "2023-06-09 15:13:07",
                    "createTime_end": null,
                    "createTime_start": null,
                    "deviceCode": "5",
                    "doHours": "3.0",
                    "doQty": "3",
                    "empCode": "",
                    "empName": "",
                    "enable": "1",
                    "f0": "",
                    "f1": "",
                    "f2": "",
                    "f3": "",
                    "f4": "",
                    "f5": "",
                    "f6": "",
                    "f7": "",
                    "f8": "",
                    "factoryId": "0",
                    "finishTime": null,
                    "flowId": "2",
                    "id": "611",
                    "keyword": "",
                    "materialCode": "111",
                    "materialItera": "",
                    "materialName": "www",
                    "materialVer": "",
                    "orderBy": "",
                    "orderCode": "G230609001",
                    "orderPriority": "3",
                    "orderStatus": "INIT",
                    "orderTime": "2023-06-09 00:00:00",
                    "orderType": "NEW",
                    "planFinish": null,
                    "planStart": null,
                    "ppOrderCode": "100107203-0008",
                    "remark": "1234567890",
                    "requestTime": "2023-06-23 00:00:00",
                    "routeCode": "铣",
                    "routeName": "铣",
                    "startTime": null,
                    "typeName": "新制",
                    "updateBy": "75",
                    "updateName": "695前端开发",
                    "updateTime": "2023-06-09 15:13:07",
                    "useStatus": ""
                },
                {
                    "approveStatus": "",
                    "centerCode": "5",
                    "centerName": "1号线",
                    "createBy": "73",
                    "createName": "一号线",
                    "createTime": "2023-05-31 09:39:00",
                    "createTime_end": null,
                    "createTime_start": null,
                    "deviceCode": "95",
                    "doHours": "1.1",
                    "doQty": "100",
                    "empCode": "",
                    "empName": "",
                    "f0": "",
                    "f1": "",
                    "f2": "",
                    "f3": "",
                    "f4": "",
                    "f5": "",
                    "f6": "100",
                    "f7": "100",
                    "f8": "4",
                    "factoryId": "0",
                    "finishTime": null,
                    "flowId": "20",
                    "id": "581",
                    "keyword": "",
                    "materialCode": "3070100069",
                    "materialItera": "",
                    "materialName": "盒盖",
                    "materialVer": "",
                    "orderBy": "",
                    "orderCode": "100107202-0010",
                    "orderPriority": "3",
                    "orderStatus": "ONGO",
                    "orderTime": "2023-05-31 09:00:00",
                    "orderType": "NEW",
                    "planFinish": null,
                    "planStart": null,
                    "ppOrderCode": "",
                    "remark": "",
                    "requestTime": "2023-06-10 23:00:00",
                    "routeCode": "铣",
                    "routeName": "铣",
                    "startTime": "2023-06-02 16:46:09",
                    "typeName": "新制",
                    "updateBy": "75",
                    "updateName": "695前端开发",
                    "updateTime": "2023-06-08 10:36:44",
                    "useStatus": ""
                },
                {
                    "approveStatus": "",
                    "centerCode": "5",
                    "centerName": "1号线",
                    "createBy": "73",
                    "createName": "一号线",
                    "createTime": "2023-05-29 18:53:18",
                    "createTime_end": null,
                    "createTime_start": null,
                    "deviceCode": "98",
                    "doHours": "1.1",
                    "doQty": "100",
                    "empCode": "",
                    "empName": "",
                    "f0": "",
                    "f1": "",
                    "f2": "",
                    "f3": "",
                    "f4": "",
                    "f5": "",
                    "f6": "100",
                    "f7": "100",
                    "f8": "55",
                    "factoryId": "0",
                    "finishTime": null,
                    "flowId": "40",
                    "id": "579",
                    "keyword": "",
                    "materialCode": "3070100071",
                    "materialItera": "",
                    "materialName": "电磁铁保护罩",
                    "materialVer": "",
                    "orderBy": "",
                    "orderCode": "100107203-0011",
                    "orderPriority": "3",
                    "orderStatus": "ONGO",
                    "orderTime": "2023-05-29 18:50:00",
                    "orderType": "NEW",
                    "planFinish": null,
                    "planStart": null,
                    "ppOrderCode": "",
                    "remark": "",
                    "requestTime": "2023-06-20 18:50:00",
                    "routeCode": "铣",
                    "routeName": "铣",
                    "startTime": "2023-05-30 13:20:07",
                    "typeName": "新制",
                    "updateBy": "73",
                    "updateName": "一号线",
                    "updateTime": "2023-05-30 13:20:07",
                    "useStatus": ""
                },
                {
                    "approveStatus": "",
                    "centerCode": "5",
                    "centerName": "1号线",
                    "createBy": "73",
                    "createName": "一号线",
                    "createTime": "2023-05-25 14:17:09",
                    "createTime_end": null,
                    "createTime_start": null,
                    "deviceCode": "99",
                    "doHours": "1.1",
                    "doQty": "20",
                    "empCode": "",
                    "empName": "",
                    "f0": "",
                    "f1": "",
                    "f2": "",
                    "f3": "",
                    "f4": "",
                    "f5": "",
                    "f6": "20",
                    "f7": "20",
                    "f8": "",
                    "factoryId": "0",
                    "finishTime": null,
                    "flowId": "20",
                    "id": "577",
                    "keyword": "",
                    "materialCode": "3070100069",
                    "materialItera": "",
                    "materialName": "盒盖",
                    "materialVer": "",
                    "orderBy": "",
                    "orderCode": "100107202-006",
                    "orderPriority": "3",
                    "orderStatus": "PLAN",
                    "orderTime": "2023-05-25 14:10:00",
                    "orderType": "NEW",
                    "planFinish": null,
                    "planStart": null,
                    "ppOrderCode": "",
                    "remark": "",
                    "requestTime": "2023-06-10 14:10:00",
                    "routeCode": "铣",
                    "routeName": "铣",
                    "startTime": null,
                    "typeName": "新制",
                    "updateBy": "73",
                    "updateName": "一号线",
                    "updateTime": "2023-05-25 14:17:40",
                    "useStatus": ""
                },
                {
                    "approveStatus": "",
                    "centerCode": "5",
                    "centerName": "1号线",
                    "createBy": "73",
                    "createName": "一号线",
                    "createTime": "2023-05-22 13:08:44",
                    "createTime_end": null,
                    "createTime_start": null,
                    "deviceCode": "101",
                    "doHours": "1.1",
                    "doQty": "100",
                    "empCode": "",
                    "empName": "",
                    "f0": "",
                    "f1": "",
                    "f2": "",
                    "f3": "",
                    "f4": "",
                    "f5": "",
                    "f6": "100",
                    "f7": "100",
                    "f8": "72",
                    "factoryId": "0",
                    "finishTime": null,
                    "flowId": "20",
                    "id": "576",
                    "keyword": "",
                    "materialCode": "3070100071",
                    "materialItera": "",
                    "materialName": "电磁铁保护罩",
                    "materialVer": "",
                    "orderBy": "",
                    "orderCode": "100107203-0010",
                    "orderPriority": "3",
                    "orderStatus": "ONGO",
                    "orderTime": "2023-05-22 13:00:00",
                    "orderType": "NEW",
                    "planFinish": null,
                    "planStart": null,
                    "ppOrderCode": "",
                    "remark": "",
                    "requestTime": "2023-06-10 13:00:00",
                    "routeCode": "铣",
                    "routeName": "铣",
                    "startTime": "2023-05-23 17:09:05",
                    "typeName": "新制",
                    "updateBy": "73",
                    "updateName": "一号线",
                    "updateTime": "2023-05-23 17:09:05",
                    "useStatus": ""
                },
                {
                    "approveStatus": "",
                    "centerCode": "5",
                    "centerName": "1号线",
                    "createBy": "67",
                    "createName": "赵硕",
                    "createTime": "2023-05-11 01:01:14",
                    "createTime_end": null,
                    "createTime_start": null,
                    "deviceCode": "98",
                    "doHours": "1.2",
                    "doQty": "100",
                    "empCode": "",
                    "empName": "",
                    "f0": "",
                    "f1": "",
                    "f2": "",
                    "f3": "",
                    "f4": "",
                    "f5": "",
                    "f6": "100",
                    "f7": "100",
                    "f8": "99",
                    "factoryId": "0",
                    "finishTime": null,
                    "flowId": "40",
                    "id": "575",
                    "keyword": "",
                    "materialCode": "3070100071",
                    "materialItera": "",
                    "materialName": "电磁铁保护罩",
                    "materialVer": "",
                    "orderBy": "",
                    "orderCode": "100107203-000",
                    "orderPriority": "3",
                    "orderStatus": "ONGO",
                    "orderTime": "2023-05-11 08:00:00",
                    "orderType": "NEW",
                    "planFinish": null,
                    "planStart": null,
                    "ppOrderCode": "",
                    "remark": "",
                    "requestTime": "2023-05-20 23:55:00",
                    "routeCode": "铣",
                    "routeName": "铣",
                    "startTime": "2023-05-11 10:13:12",
                    "typeName": "新制",
                    "updateBy": "67",
                    "updateName": "赵硕",
                    "updateTime": "2023-05-11 10:13:12",
                    "useStatus": ""
                },
                {
                    "approveStatus": "",
                    "centerCode": "5",
                    "centerName": "1号线",
                    "createBy": "67",
                    "createName": "赵硕",
                    "createTime": "2023-04-23 19:40:38",
                    "createTime_end": null,
                    "createTime_start": null,
                    "deviceCode": "96",
                    "doHours": "2.0",
                    "doQty": "100",
                    "empCode": "",
                    "empName": "",
                    "f0": "",
                    "f1": "",
                    "f2": "",
                    "f3": "",
                    "f4": "",
                    "f5": "",
                    "f6": "100",
                    "f7": "100",
                    "f8": "37",
                    "factoryId": "0",
                    "finishTime": null,
                    "flowId": "20",
                    "id": "568",
                    "keyword": "",
                    "materialCode": "3070100069",
                    "materialItera": "",
                    "materialName": "盒盖",
                    "materialVer": "",
                    "orderBy": "",
                    "orderCode": "100107202-0007",
                    "orderPriority": "3",
                    "orderStatus": "ONGO",
                    "orderTime": "2023-04-23 20:05:00",
                    "orderType": "NEW",
                    "planFinish": null,
                    "planStart": null,
                    "ppOrderCode": "",
                    "remark": "",
                    "requestTime": "2023-05-02 23:35:00",
                    "routeCode": "铣",
                    "routeName": "铣",
                    "startTime": "2023-04-24 10:02:38",
                    "typeName": "新制",
                    "updateBy": "71",
                    "updateName": "何慧钧",
                    "updateTime": "2023-04-24 10:02:38",
                    "useStatus": ""
                },
                {
                    "approveStatus": "",
                    "centerCode": "5",
                    "centerName": "1号线",
                    "createBy": "67",
                    "createName": "赵硕",
                    "createTime": "2023-04-21 09:00:13",
                    "createTime_end": null,
                    "createTime_start": null,
                    "deviceCode": "97",
                    "doHours": "1.5",
                    "doQty": "100",
                    "empCode": "",
                    "empName": "",
                    "f0": "",
                    "f1": "",
                    "f2": "",
                    "f3": "",
                    "f4": "",
                    "f5": "",
                    "f6": "100",
                    "f7": "100",
                    "f8": "66",
                    "factoryId": "0",
                    "finishTime": null,
                    "flowId": "20",
                    "id": "567",
                    "keyword": "",
                    "materialCode": "3070100071",
                    "materialItera": "",
                    "materialName": "电磁铁保护罩",
                    "materialVer": "",
                    "orderBy": "",
                    "orderCode": "100107203-0009",
                    "orderPriority": "3",
                    "orderStatus": "ONGO",
                    "orderTime": "2023-04-21 10:10:00",
                    "orderType": "NEW",
                    "planFinish": null,
                    "planStart": null,
                    "ppOrderCode": "",
                    "remark": "",
                    "requestTime": "2023-05-04 23:55:00",
                    "routeCode": "铣",
                    "routeName": "铣",
                    "startTime": "2023-04-21 20:49:10",
                    "typeName": "新制",
                    "updateBy": "67",
                    "updateName": "赵硕",
                    "updateTime": "2023-04-21 20:49:10",
                    "useStatus": ""
                },
                {
                    "approveStatus": "",
                    "centerCode": "5",
                    "centerName": "1号线",
                    "createBy": "67",
                    "createName": "赵硕",
                    "createTime": "2023-04-21 08:55:40",
                    "createTime_end": null,
                    "createTime_start": null,
                    "deviceCode": "96",
                    "doHours": "2.0",
                    "doQty": "50",
                    "empCode": "",
                    "empName": "",
                    "f0": "",
                    "f1": "",
                    "f2": "",
                    "f3": "",
                    "f4": "",
                    "f5": "",
                    "f6": "10",
                    "f7": "10",
                    "f8": "10",
                    "factoryId": "0",
                    "finishTime": null,
                    "flowId": "20",
                    "id": "566",
                    "keyword": "",
                    "materialCode": "3070100069",
                    "materialItera": "",
                    "materialName": "盒盖",
                    "materialVer": "",
                    "orderBy": "",
                    "orderCode": "100107202-003",
                    "orderPriority": "3",
                    "orderStatus": "ONGO",
                    "orderTime": "2023-04-21 10:50:00",
                    "orderType": "NEW",
                    "planFinish": null,
                    "planStart": null,
                    "ppOrderCode": "",
                    "remark": "",
                    "requestTime": "2023-04-30 23:50:00",
                    "routeCode": "铣",
                    "routeName": "铣",
                    "startTime": "2023-04-22 08:53:37",
                    "typeName": "新制",
                    "updateBy": "67",
                    "updateName": "赵硕",
                    "updateTime": "2023-04-22 08:53:37",
                    "useStatus": ""
                },
                {
                    "approveStatus": "",
                    "centerCode": "5",
                    "centerName": "1号线",
                    "createBy": "67",
                    "createName": "赵硕",
                    "createTime": "2023-04-20 14:20:58",
                    "createTime_end": null,
                    "createTime_start": null,
                    "deviceCode": "99",
                    "doHours": "1.5",
                    "doQty": "50",
                    "empCode": "",
                    "empName": "",
                    "f0": "",
                    "f1": "",
                    "f2": "",
                    "f3": "",
                    "f4": "",
                    "f5": "",
                    "f6": "50",
                    "f7": "50",
                    "f8": "49",
                    "factoryId": "0",
                    "finishTime": null,
                    "flowId": "20",
                    "id": "564",
                    "keyword": "",
                    "materialCode": "3070100069",
                    "materialItera": "",
                    "materialName": "盒盖",
                    "materialVer": "",
                    "orderBy": "",
                    "orderCode": "100107202-005",
                    "orderPriority": "3",
                    "orderStatus": "ONGO",
                    "orderTime": "2023-04-21 10:05:00",
                    "orderType": "NEW",
                    "planFinish": null,
                    "planStart": null,
                    "ppOrderCode": "",
                    "remark": "",
                    "requestTime": "2023-04-28 23:10:00",
                    "routeCode": "铣",
                    "routeName": "铣",
                    "startTime": "2023-04-21 12:41:27",
                    "typeName": "新制",
                    "updateBy": "67",
                    "updateName": "赵硕",
                    "updateTime": "2023-04-21 12:41:27",
                    "useStatus": ""
                }
            ],
            "current": "1",
            "size": "10",
            "pages": "1",
            "total": "10",
            "code": "200",
            "msg": "请求成功",
            "timestamp": "1689068000561"
        })
    }
    // export default {
    //     fetch_ppWorkOrderList
    // }
export const getOrderGantt1 = () => {
    Mock.mock('/tmes/kpi/readOrderGanttData', {
        'msg': '',
        'code': '200',
        'data': {
            'orderArray': [{
                    'planFinishTime': '2022-12-10 00:00:00',
                    'planStartTime': '2022-12-09 00:00:00',
                    'orderCode': '555'
                },
                {
                    'planFinishTime': '2022-12-02 00:00:00',
                    'planStartTime': '2022-12-01 00:00:00',
                    'orderCode': '111'
                },
                {
                    'planFinishTime': '2022-12-05 00:00:00',
                    'planStartTime': '2022-12-03 00:00:00',
                    'orderCode': '222'
                },
                {
                    'planFinishTime': '2022-12-08 00:00:00',
                    'planStartTime': '2022-12-06 00:00:00',
                    'orderCode': '333'
                },
                {
                    'planFinishTime': '2022-12-09 00:00:00',
                    'planStartTime': '2022-12-06 00:00:00',
                    'orderCode': '444'
                }
            ]
        },
        'timestamp': '1670551924434'
    })
}
export const getOrderGantt = () => {
    Mock.mock('/tmes/kpi/readOrderGanttData', {
        'msg': '',
        'code': '200',
        'data': {
            'orderArray': [{
                    'planFinishTime': '2023-05-20 14:38:06',
                    'planStartTime': '2023-05-18 08:38:06',
                    'orderCode': 'G230515008',
                    'num': '100107203-0008',
                    'materialCode': '3050201015',
                    'materialName': '扣压接头',
                    'planNum': '20',
                    'finishNum': '2'
                },
                {
                    'planFinishTime': '2023-05-16 23:08:06',
                    'planStartTime': '2023-05-15 08:38:06',
                    'orderCode': 'G230515009',
                    'num': '200107205-0006',
                    'materialCode': '2050201026',
                    'materialName': '壳体',
                    'planNum': '30',
                    'finishNum': '12'
                },
                {
                    'planFinishTime': '2023-05-16 16:08:06',
                    'planStartTime': '2023-05-15 08:38:06',
                    'orderCode': 'G230515010',
                    'num': '100107203-0008', // 零件任务编号
                    'materialCode': '3050201015',
                    'materialName': '扣压接头',
                    'planNum': '20',
                    'finishNum': '3'
                },
                {
                    'planFinishTime': '2023-05-16 09:38:06',
                    'planStartTime': '2023-05-15 08:38:06',
                    'orderCode': 'G230515011',
                    'num': '200107205-0006',
                    'materialCode': '2050201026',
                    'materialName': '壳体',
                    'planNum': '30',
                    'finishNum': '15'
                },
                {
                    'planFinishTime': '2023-05-18 12:38:06',
                    'planStartTime': '2023-05-15 08:38:06',
                    'orderCode': 'G230515012',
                    'num': '200107205-0006',
                    'materialCode': '2050201026',
                    'materialName': '壳体',
                    'planNum': '30',
                    'finishNum': '2'
                }
            ]
        },
        'timestamp': '1684307905027'
    })
}

export const queryMainPageMessage = () => {
    Mock.mock('/tmes/admin/messageInfo/queryMainPageMessage', {
        "msg": "",
        "total": "4",
        "code": "200",
        "rows": [{
                "createName": "",
                "createTime": "2023-05-23 08:32:57",
                "factoryId": "0",
                "id": "69",
                "keyword": "",
                "messageFrom": "",
                "messageStatus": "readed",
                "messageTitle": "版本更新2.0.0：首页更新，优化任务排产",
                "orderBy": "",
                "remark": "",
                "sendTime": null,
                "senderName": "",
                "senderNo": "",
                "updateName": "",
                "updateTime": null,
                "userId": "72"
            },
            {
                "createName": "",
                "createTime": "2023-05-17 08:32:51",
                "factoryId": "0",
                "id": "68",
                "keyword": "",
                "messageFrom": "",
                "messageStatus": "new",
                "messageTitle": "测试111",
                "orderBy": "",
                "remark": "",
                "sendTime": null,
                "senderName": "",
                "senderNo": "",
                "updateName": "",
                "updateTime": null,
                "userId": "71"
            },
            {
                "createName": "",
                "createTime": "2023-05-25 08:32:46",
                "factoryId": "0",
                "id": "67",
                "keyword": "",
                "messageFrom": "",
                "messageStatus": "new",
                "messageTitle": "系统维护",
                "orderBy": "",
                "remark": "",
                "sendTime": null,
                "senderName": "",
                "senderNo": "",
                "updateName": "",
                "updateTime": null,
                "userId": "71"
            },
            {
                "createName": "",
                "createTime": "2023-05-24 08:32:42",
                "factoryId": "0",
                "id": "66",
                "keyword": "",
                "messageFrom": "",
                "messageStatus": "new",
                "messageTitle": "版本更新2.0.1：优化任务排产",
                "orderBy": "",
                "remark": "",
                "sendTime": null,
                "senderName": "",
                "senderNo": "",
                "updateName": "",
                "updateTime": null,
                "userId": "71"
            }
        ],
        "timestamp": "1685092431816"
    })
}
export const queryFactoryInfo = () => {
    Mock.mock('/tmes/kpi/queryFactoryInfo', {
        "msg": "",
        "code": "200",
        "data": {
            "deptName": "智能制造车间",
            "factoryName": "河南航天695工厂",
            "lineArray": [{
                    "deviceQty": "7",
                    "usableQty": "7",
                    "lineName": "1号线"
                },
                {
                    "deviceQty": "4",
                    "usableQty": "4",
                    "lineName": "2号线"
                }
            ]
        },
        "timestamp": "1685092876273"
    })
}
export const queryExecutionStatis = () => {
    Mock.mock('/tmes/kpi/queryExecutionStatis', {
        "msg": "",
        "total": "18",
        "code": "200",
        "rows": [{
                "createName": "",
                "createTime": null,
                "finishDate": "2023-04-29",
                "finishQty": "2",
                "keyword": "",
                "orderBy": "",
                "remark": "",
                "updateName": "",
                "updateTime": null
            },
            {
                "createName": "",
                "createTime": null,
                "finishDate": "2023-04-30",
                "finishQty": "1",
                "keyword": "",
                "orderBy": "",
                "remark": "",
                "updateName": "",
                "updateTime": null
            },
            {
                "createName": "",
                "createTime": null,
                "finishDate": "2023-05-02",
                "finishQty": "2",
                "keyword": "",
                "orderBy": "",
                "remark": "",
                "updateName": "",
                "updateTime": null
            },
            {
                "createName": "",
                "createTime": null,
                "finishDate": "2023-05-03",
                "finishQty": "2",
                "keyword": "",
                "orderBy": "",
                "remark": "",
                "updateName": "",
                "updateTime": null
            },
            {
                "createName": "",
                "createTime": null,
                "finishDate": "2023-05-05",
                "finishQty": "2",
                "keyword": "",
                "orderBy": "",
                "remark": "",
                "updateName": "",
                "updateTime": null
            },
            {
                "createName": "",
                "createTime": null,
                "finishDate": "2023-05-08",
                "finishQty": "1",
                "keyword": "",
                "orderBy": "",
                "remark": "",
                "updateName": "",
                "updateTime": null
            },
            {
                "createName": "",
                "createTime": null,
                "finishDate": "2023-05-10",
                "finishQty": "1",
                "keyword": "",
                "orderBy": "",
                "remark": "",
                "updateName": "",
                "updateTime": null
            },
            {
                "createName": "",
                "createTime": null,
                "finishDate": "2023-05-12",
                "finishQty": "2",
                "keyword": "",
                "orderBy": "",
                "remark": "",
                "updateName": "",
                "updateTime": null
            },
            {
                "createName": "",
                "createTime": null,
                "finishDate": "2023-05-14",
                "finishQty": "2",
                "keyword": "",
                "orderBy": "",
                "remark": "",
                "updateName": "",
                "updateTime": null
            },
            {
                "createName": "",
                "createTime": null,
                "finishDate": "2023-05-15",
                "finishQty": "5",
                "keyword": "",
                "orderBy": "",
                "remark": "",
                "updateName": "",
                "updateTime": null
            },
            {
                "createName": "",
                "createTime": null,
                "finishDate": "2023-05-18",
                "finishQty": "1",
                "keyword": "",
                "orderBy": "",
                "remark": "",
                "updateName": "",
                "updateTime": null
            },
            {
                "createName": "",
                "createTime": null,
                "finishDate": "2023-05-19",
                "finishQty": "1",
                "keyword": "",
                "orderBy": "",
                "remark": "",
                "updateName": "",
                "updateTime": null
            },
            {
                "createName": "",
                "createTime": null,
                "finishDate": "2023-05-20",
                "finishQty": "2",
                "keyword": "",
                "orderBy": "",
                "remark": "",
                "updateName": "",
                "updateTime": null
            },
            {
                "createName": "",
                "createTime": null,
                "finishDate": "2023-05-21",
                "finishQty": "2",
                "keyword": "",
                "orderBy": "",
                "remark": "",
                "updateName": "",
                "updateTime": null
            },
            {
                "createName": "",
                "createTime": null,
                "finishDate": "2023-05-22",
                "finishQty": "2",
                "keyword": "",
                "orderBy": "",
                "remark": "",
                "updateName": "",
                "updateTime": null
            },
            {
                "createName": "",
                "createTime": null,
                "finishDate": "2023-05-23",
                "finishQty": "1",
                "keyword": "",
                "orderBy": "",
                "remark": "",
                "updateName": "",
                "updateTime": null
            },
            {
                "createName": "",
                "createTime": null,
                "finishDate": "2023-05-24",
                "finishQty": "5",
                "keyword": "",
                "orderBy": "",
                "remark": "",
                "updateName": "",
                "updateTime": null
            },
            {
                "createName": "",
                "createTime": null,
                "finishDate": "2023-05-26",
                "finishQty": "1",
                "keyword": "",
                "orderBy": "",
                "remark": "",
                "updateName": "",
                "updateTime": null
            }
        ],
        "timestamp": "1685093015617"
    })
}

export const queryMainPageStatis = () => {
    Mock.mock('/tmes/kpi/queryMainPageStatis', {
        "msg": "",
        "code": "200",
        "data": {
            "weekStatis": {
                "fRate": "0.5",
                "planOrder": "20",
                "finishTask": "150",
                "planTask": "300"
            },
            "dayStatis": {
                "fRate": "0.5",
                "planOrder": "20",
                "finishTask": "150",
                "planTask": "300"
            },
            "monthStatis": {
                "fRate": "0.5",
                "planOrder": "20",
                "finishTask": "150",
                "planTask": "300"
            }
        },
        "timestamp": "1685093377878"
    })
}




export const getWorkOrderDetail = (data, para) => {
    Mock.mock(`/tmes/pp/ppWorkOrder/queryAssignListByCode?${util.param(para)}`, {
        "msg": "",
        "code": "200",
        "data": {
            "approveStatus": "",
            "centerCode": "",
            "centerName": "",
            "createName": "",
            "createTime": null,
            "createTime_end": null,
            "createTime_start": null,
            "deviceCode": "",
            "empCode": "",
            "empName": "",
            "f0": "",
            "f1": "",
            "f2": "",
            "f3": "",
            "f4": "",
            "f5": "",
            "f6": "",
            "f7": "",
            "f8": "",
            "factoryId": "0",
            "finishTime": null,
            "keyword": "",
            "listPpAssignOrder": [{
                    "centerCode": "5",
                    "centerName": "1号线",
                    "createName": "",
                    "createTime": "2023-05-15 08:30:34",
                    "deviceCode": "95",
                    "deviceName": "",
                    "doQty": "1",
                    "empCode": "",
                    "empName": "",
                    "f0": "",
                    "f1": "",
                    "f2": "",
                    "f3": "",
                    "f4": "",
                    "f5": "",
                    "f6": "",
                    "f7": "",
                    "f8": "",
                    "factoryId": "0",
                    "finishTime": "2023-05-15 15:48:16",
                    "flowId": "10",
                    "id": "23769",
                    "keyword": "",
                    "materialCode": "3050201015",
                    "materialItera": "",
                    "materialName": "扣压接头",
                    "materialVer": "",
                    "orderBy": "",
                    "orderCode": "G230515008:0010-00",
                    "orderStatus": "DONE",
                    "orderType": "",
                    "planFinishTime": "2023-05-18 17:30:00",
                    "planStartTime": "2023-05-18 15:30:00",
                    "planTime_end": "",
                    "planTime_start": "",
                    "ppOrderCode": "",
                    "ppWorkOrderCode": "G230515008",
                    "ppWorkOrderId": "805",
                    "remark": "",
                    "requestTime": "2023-05-15 08:30:47",
                    "routeCode": "G230515008",
                    "routeName": "铣",
                    "startTime": "2023-05-15 15:47:56",
                    "typeName": "",
                    "updateName": "",
                    "updateTime": "2023-05-15 15:48:16"
                },
                {
                    "centerCode": "5",
                    "centerName": "1号线",
                    "createName": "",
                    "createTime": "2023-05-15 08:30:35",
                    "deviceCode": "95",
                    "deviceName": "",
                    "doQty": "1",
                    "empCode": "",
                    "empName": "",
                    "f0": "",
                    "f1": "",
                    "f2": "",
                    "f3": "",
                    "f4": "",
                    "f5": "",
                    "f6": "",
                    "f7": "",
                    "f8": "",
                    "factoryId": "0",
                    "finishTime": "2023-05-15 16:22:07",
                    "flowId": "10",
                    "id": "23777",
                    "keyword": "",
                    "materialCode": "3050201015",
                    "materialItera": "",
                    "materialName": "扣压接头",
                    "materialVer": "",
                    "orderBy": "",
                    "orderCode": "G230515008:0010-01",
                    "orderStatus": "DONE",
                    "orderType": "",
                    "planFinishTime": "2023-05-18 22:00:00",
                    "planStartTime": "2023-05-18 20:00:00",
                    "planTime_end": "",
                    "planTime_start": "",
                    "ppOrderCode": "",
                    "ppWorkOrderCode": "G230515008",
                    "ppWorkOrderId": "805",
                    "remark": "",
                    "requestTime": "2023-05-15 08:30:48",
                    "routeCode": "G230515008",
                    "routeName": "铣",
                    "startTime": "2023-05-15 16:22:03",
                    "typeName": "",
                    "updateName": "",
                    "updateTime": "2023-05-15 16:22:07"
                },
                {
                    "centerCode": "5",
                    "centerName": "1号线",
                    "createName": "",
                    "createTime": "2023-05-15 08:30:34",
                    "deviceCode": "95",
                    "deviceName": "",
                    "doQty": "1",
                    "empCode": "",
                    "empName": "",
                    "f0": "",
                    "f1": "",
                    "f2": "",
                    "f3": "",
                    "f4": "",
                    "f5": "",
                    "f6": "",
                    "f7": "",
                    "f8": "",
                    "factoryId": "0",
                    "finishTime": "2023-05-15 16:24:14",
                    "flowId": "10",
                    "id": "23764",
                    "keyword": "",
                    "materialCode": "3050201015",
                    "materialItera": "",
                    "materialName": "扣压接头",
                    "materialVer": "",
                    "orderBy": "",
                    "orderCode": "G230515008:0010-02",
                    "orderStatus": "DONE",
                    "orderType": "",
                    "planFinishTime": "2023-05-19 00:00:00",
                    "planStartTime": "2023-05-18 22:00:00",
                    "planTime_end": "",
                    "planTime_start": "",
                    "ppOrderCode": "",
                    "ppWorkOrderCode": "G230515008",
                    "ppWorkOrderId": "805",
                    "remark": "",
                    "requestTime": "2023-05-15 08:30:46",
                    "routeCode": "G230515008",
                    "routeName": "铣",
                    "startTime": "2023-05-15 16:24:09",
                    "typeName": "",
                    "updateName": "",
                    "updateTime": "2023-05-15 16:24:14"
                },
                {
                    "centerCode": "5",
                    "centerName": "1号线",
                    "createName": "",
                    "createTime": "2023-05-15 08:30:33",
                    "deviceCode": "95",
                    "deviceName": "",
                    "doQty": "1",
                    "empCode": "",
                    "empName": "",
                    "f0": "",
                    "f1": "",
                    "f2": "",
                    "f3": "",
                    "f4": "",
                    "f5": "",
                    "f6": "",
                    "f7": "",
                    "f8": "",
                    "factoryId": "0",
                    "finishTime": "2023-05-23 14:35:53",
                    "flowId": "10",
                    "id": "23759",
                    "keyword": "",
                    "materialCode": "3050201015",
                    "materialItera": "",
                    "materialName": "扣压接头",
                    "materialVer": "",
                    "orderBy": "",
                    "orderCode": "G230515008:0010-17",
                    "orderStatus": "DONE",
                    "orderType": "",
                    "planFinishTime": "2023-05-24 15:00:00",
                    "planStartTime": "2023-05-24 13:00:00",
                    "planTime_end": "",
                    "planTime_start": "",
                    "ppOrderCode": "",
                    "ppWorkOrderCode": "G230515008",
                    "ppWorkOrderId": "805",
                    "remark": "",
                    "requestTime": "2023-05-24 14:39:23",
                    "routeCode": "G230515008",
                    "routeName": "铣",
                    "startTime": "2023-05-23 14:35:45",
                    "typeName": "",
                    "updateName": "",
                    "updateTime": "2023-05-24 14:35:53"
                },
                {
                    "centerCode": "5",
                    "centerName": "1号线",
                    "createName": "",
                    "createTime": "2023-05-15 08:30:33",
                    "deviceCode": "95",
                    "deviceName": "",
                    "doQty": "1",
                    "empCode": "",
                    "empName": "",
                    "f0": "",
                    "f1": "",
                    "f2": "",
                    "f3": "",
                    "f4": "",
                    "f5": "",
                    "f6": "",
                    "f7": "",
                    "f8": "",
                    "factoryId": "0",
                    "finishTime": "2023-05-22 14:35:55",
                    "flowId": "10",
                    "id": "23760",
                    "keyword": "",
                    "materialCode": "3050201015",
                    "materialItera": "",
                    "materialName": "扣压接头",
                    "materialVer": "",
                    "orderBy": "",
                    "orderCode": "G230515008:0010-04",
                    "orderStatus": "DONE",
                    "orderType": "",
                    "planFinishTime": "2023-05-24 15:00:00",
                    "planStartTime": "2023-05-24 13:00:00",
                    "planTime_end": "",
                    "planTime_start": "",
                    "ppOrderCode": "",
                    "ppWorkOrderCode": "G230515008",
                    "ppWorkOrderId": "805",
                    "remark": "",
                    "requestTime": "2023-05-24 14:39:26",
                    "routeCode": "G230515008",
                    "routeName": "铣",
                    "startTime": "2023-05-22 14:35:46",
                    "typeName": "",
                    "updateName": "",
                    "updateTime": "2023-05-24 14:35:55"
                },
                {
                    "centerCode": "5",
                    "centerName": "1号线",
                    "createName": "",
                    "createTime": "2023-05-15 08:30:33",
                    "deviceCode": "95",
                    "deviceName": "",
                    "doQty": "1",
                    "empCode": "",
                    "empName": "",
                    "f0": "",
                    "f1": "",
                    "f2": "",
                    "f3": "",
                    "f4": "",
                    "f5": "",
                    "f6": "",
                    "f7": "",
                    "f8": "",
                    "factoryId": "0",
                    "finishTime": "2023-05-21 14:35:56",
                    "flowId": "10",
                    "id": "23761",
                    "keyword": "",
                    "materialCode": "3050201015",
                    "materialItera": "",
                    "materialName": "扣压接头",
                    "materialVer": "",
                    "orderBy": "",
                    "orderCode": "G230515008:0010-09",
                    "orderStatus": "DONE",
                    "orderType": "",
                    "planFinishTime": "2023-05-24 15:00:00",
                    "planStartTime": "2023-05-24 13:00:00",
                    "planTime_end": "",
                    "planTime_start": "",
                    "ppOrderCode": "",
                    "ppWorkOrderCode": "G230515008",
                    "ppWorkOrderId": "805",
                    "remark": "",
                    "requestTime": "2023-05-24 14:39:29",
                    "routeCode": "G230515008",
                    "routeName": "铣",
                    "startTime": "2023-05-22 14:35:46",
                    "typeName": "",
                    "updateName": "",
                    "updateTime": "2023-05-24 14:35:56"
                },
                {
                    "centerCode": "5",
                    "centerName": "1号线",
                    "createName": "",
                    "createTime": "2023-05-15 08:30:33",
                    "deviceCode": "95",
                    "deviceName": "",
                    "doQty": "1",
                    "empCode": "",
                    "empName": "",
                    "f0": "",
                    "f1": "",
                    "f2": "",
                    "f3": "",
                    "f4": "",
                    "f5": "",
                    "f6": "",
                    "f7": "",
                    "f8": "",
                    "factoryId": "0",
                    "finishTime": "2023-05-20 14:35:56",
                    "flowId": "10",
                    "id": "23762",
                    "keyword": "",
                    "materialCode": "3050201015",
                    "materialItera": "",
                    "materialName": "扣压接头",
                    "materialVer": "",
                    "orderBy": "",
                    "orderCode": "G230515008:0010-18",
                    "orderStatus": "DONE",
                    "orderType": "",
                    "planFinishTime": "2023-05-24 15:00:00",
                    "planStartTime": "2023-05-24 13:00:00",
                    "planTime_end": "",
                    "planTime_start": "",
                    "ppOrderCode": "",
                    "ppWorkOrderCode": "G230515008",
                    "ppWorkOrderId": "805",
                    "remark": "",
                    "requestTime": "2023-05-24 14:39:34",
                    "routeCode": "G230515008",
                    "routeName": "铣",
                    "startTime": "2023-05-21 14:35:46",
                    "typeName": "",
                    "updateName": "",
                    "updateTime": "2023-05-24 14:35:56"
                },
                {
                    "centerCode": "5",
                    "centerName": "1号线",
                    "createName": "",
                    "createTime": "2023-05-15 08:30:34",
                    "deviceCode": "95",
                    "deviceName": "",
                    "doQty": "1",
                    "empCode": "",
                    "empName": "",
                    "f0": "",
                    "f1": "",
                    "f2": "",
                    "f3": "",
                    "f4": "",
                    "f5": "",
                    "f6": "",
                    "f7": "",
                    "f8": "",
                    "factoryId": "0",
                    "finishTime": "2023-05-20 14:35:57",
                    "flowId": "10",
                    "id": "23763",
                    "keyword": "",
                    "materialCode": "3050201015",
                    "materialItera": "",
                    "materialName": "扣压接头",
                    "materialVer": "",
                    "orderBy": "",
                    "orderCode": "G230515008:0010-12",
                    "orderStatus": "DONE",
                    "orderType": "",
                    "planFinishTime": "2023-05-24 15:00:00",
                    "planStartTime": "2023-05-24 13:00:00",
                    "planTime_end": "",
                    "planTime_start": "",
                    "ppOrderCode": "",
                    "ppWorkOrderCode": "G230515008",
                    "ppWorkOrderId": "805",
                    "remark": "",
                    "requestTime": "2023-05-24 14:39:37",
                    "routeCode": "G230515008",
                    "routeName": "铣",
                    "startTime": "2023-05-24 14:35:46",
                    "typeName": "",
                    "updateName": "",
                    "updateTime": "2023-05-24 14:35:57"
                },
                {
                    "centerCode": "5",
                    "centerName": "1号线",
                    "createName": "",
                    "createTime": "2023-05-15 08:30:34",
                    "deviceCode": "95",
                    "deviceName": "",
                    "doQty": "1",
                    "empCode": "",
                    "empName": "",
                    "f0": "",
                    "f1": "",
                    "f2": "",
                    "f3": "",
                    "f4": "",
                    "f5": "",
                    "f6": "",
                    "f7": "",
                    "f8": "",
                    "factoryId": "0",
                    "finishTime": "2023-05-14 14:35:58",
                    "flowId": "10",
                    "id": "23765",
                    "keyword": "",
                    "materialCode": "3050201015",
                    "materialItera": "",
                    "materialName": "扣压接头",
                    "materialVer": "",
                    "orderBy": "",
                    "orderCode": "G230515008:0010-14",
                    "orderStatus": "DONE",
                    "orderType": "",
                    "planFinishTime": "2023-05-24 15:00:00",
                    "planStartTime": "2023-05-24 13:00:00",
                    "planTime_end": "",
                    "planTime_start": "",
                    "ppOrderCode": "",
                    "ppWorkOrderCode": "G230515008",
                    "ppWorkOrderId": "805",
                    "remark": "",
                    "requestTime": "2023-05-24 14:39:41",
                    "routeCode": "G230515008",
                    "routeName": "铣",
                    "startTime": "2023-05-24 14:35:47",
                    "typeName": "",
                    "updateName": "",
                    "updateTime": "2023-05-24 14:35:58"
                },
                {
                    "centerCode": "5",
                    "centerName": "1号线",
                    "createName": "",
                    "createTime": "2023-05-15 08:30:34",
                    "deviceCode": "95",
                    "deviceName": "",
                    "doQty": "1",
                    "empCode": "",
                    "empName": "",
                    "f0": "",
                    "f1": "",
                    "f2": "",
                    "f3": "",
                    "f4": "",
                    "f5": "",
                    "f6": "",
                    "f7": "",
                    "f8": "",
                    "factoryId": "0",
                    "finishTime": "2023-05-18 14:35:59",
                    "flowId": "10",
                    "id": "23766",
                    "keyword": "",
                    "materialCode": "3050201015",
                    "materialItera": "",
                    "materialName": "扣压接头",
                    "materialVer": "",
                    "orderBy": "",
                    "orderCode": "G230515008:0010-13",
                    "orderStatus": "DONE",
                    "orderType": "",
                    "planFinishTime": "2023-05-24 15:00:00",
                    "planStartTime": "2023-05-24 13:00:00",
                    "planTime_end": "",
                    "planTime_start": "",
                    "ppOrderCode": "",
                    "ppWorkOrderCode": "G230515008",
                    "ppWorkOrderId": "805",
                    "remark": "",
                    "requestTime": "2023-05-24 14:39:45",
                    "routeCode": "G230515008",
                    "routeName": "铣",
                    "startTime": "2023-05-24 14:35:47",
                    "typeName": "",
                    "updateName": "",
                    "updateTime": "2023-05-24 14:35:59"
                },
                {
                    "centerCode": "5",
                    "centerName": "1号线",
                    "createName": "",
                    "createTime": "2023-05-15 08:30:34",
                    "deviceCode": "95",
                    "deviceName": "",
                    "doQty": "1",
                    "empCode": "",
                    "empName": "",
                    "f0": "",
                    "f1": "",
                    "f2": "",
                    "f3": "",
                    "f4": "",
                    "f5": "",
                    "f6": "",
                    "f7": "",
                    "f8": "",
                    "factoryId": "0",
                    "finishTime": "2023-05-19 14:36:00",
                    "flowId": "10",
                    "id": "23767",
                    "keyword": "",
                    "materialCode": "3050201015",
                    "materialItera": "",
                    "materialName": "扣压接头",
                    "materialVer": "",
                    "orderBy": "",
                    "orderCode": "G230515008:0010-15",
                    "orderStatus": "DONE",
                    "orderType": "",
                    "planFinishTime": "2023-05-24 15:00:00",
                    "planStartTime": "2023-05-24 13:00:00",
                    "planTime_end": "",
                    "planTime_start": "",
                    "ppOrderCode": "",
                    "ppWorkOrderCode": "G230515008",
                    "ppWorkOrderId": "805",
                    "remark": "",
                    "requestTime": "2023-05-24 14:39:49",
                    "routeCode": "G230515008",
                    "routeName": "铣",
                    "startTime": "2023-05-24 14:35:47",
                    "typeName": "",
                    "updateName": "",
                    "updateTime": "2023-05-24 14:36:00"
                },
                {
                    "centerCode": "5",
                    "centerName": "1号线",
                    "createName": "",
                    "createTime": "2023-05-15 08:30:34",
                    "deviceCode": "95",
                    "deviceName": "",
                    "doQty": "1",
                    "empCode": "",
                    "empName": "",
                    "f0": "",
                    "f1": "",
                    "f2": "",
                    "f3": "",
                    "f4": "",
                    "f5": "",
                    "f6": "",
                    "f7": "",
                    "f8": "",
                    "factoryId": "0",
                    "finishTime": "2023-05-05 14:36:00",
                    "flowId": "10",
                    "id": "23768",
                    "keyword": "",
                    "materialCode": "3050201015",
                    "materialItera": "",
                    "materialName": "扣压接头",
                    "materialVer": "",
                    "orderBy": "",
                    "orderCode": "G230515008:0010-07",
                    "orderStatus": "DONE",
                    "orderType": "",
                    "planFinishTime": "2023-05-24 15:00:00",
                    "planStartTime": "2023-05-24 13:00:00",
                    "planTime_end": "",
                    "planTime_start": "",
                    "ppOrderCode": "",
                    "ppWorkOrderCode": "G230515008",
                    "ppWorkOrderId": "805",
                    "remark": "",
                    "requestTime": "2023-05-26 13:15:27",
                    "routeCode": "G230515008",
                    "routeName": "铣",
                    "startTime": "2023-05-24 14:35:47",
                    "typeName": "",
                    "updateName": "",
                    "updateTime": "2023-05-24 14:36:00"
                },
                {
                    "centerCode": "5",
                    "centerName": "1号线",
                    "createName": "",
                    "createTime": "2023-05-15 08:30:34",
                    "deviceCode": "95",
                    "deviceName": "",
                    "doQty": "1",
                    "empCode": "",
                    "empName": "",
                    "f0": "",
                    "f1": "",
                    "f2": "",
                    "f3": "",
                    "f4": "",
                    "f5": "",
                    "f6": "",
                    "f7": "",
                    "f8": "",
                    "factoryId": "0",
                    "finishTime": "2023-05-12 14:36:01",
                    "flowId": "10",
                    "id": "23770",
                    "keyword": "",
                    "materialCode": "3050201015",
                    "materialItera": "",
                    "materialName": "扣压接头",
                    "materialVer": "",
                    "orderBy": "",
                    "orderCode": "G230515008:0010-10",
                    "orderStatus": "DONE",
                    "orderType": "",
                    "planFinishTime": "2023-05-24 15:00:00",
                    "planStartTime": "2023-05-24 13:00:00",
                    "planTime_end": "",
                    "planTime_start": "",
                    "ppOrderCode": "",
                    "ppWorkOrderCode": "G230515008",
                    "ppWorkOrderId": "805",
                    "remark": "",
                    "requestTime": "2023-05-24 14:39:58",
                    "routeCode": "G230515008",
                    "routeName": "铣",
                    "startTime": "2023-05-24 14:35:47",
                    "typeName": "",
                    "updateName": "",
                    "updateTime": "2023-05-24 14:36:01"
                },
                {
                    "centerCode": "5",
                    "centerName": "1号线",
                    "createName": "",
                    "createTime": "2023-05-15 08:30:34",
                    "deviceCode": "95",
                    "deviceName": "",
                    "doQty": "1",
                    "empCode": "",
                    "empName": "",
                    "f0": "",
                    "f1": "",
                    "f2": "",
                    "f3": "",
                    "f4": "",
                    "f5": "",
                    "f6": "",
                    "f7": "",
                    "f8": "",
                    "factoryId": "0",
                    "finishTime": "2023-05-02 14:36:26",
                    "flowId": "10",
                    "id": "23771",
                    "keyword": "",
                    "materialCode": "3050201015",
                    "materialItera": "",
                    "materialName": "扣压接头",
                    "materialVer": "",
                    "orderBy": "",
                    "orderCode": "G230515008:0010-08",
                    "orderStatus": "DONE",
                    "orderType": "",
                    "planFinishTime": "2023-05-24 15:00:00",
                    "planStartTime": "2023-05-24 13:00:00",
                    "planTime_end": "",
                    "planTime_start": "",
                    "ppOrderCode": "",
                    "ppWorkOrderCode": "G230515008",
                    "ppWorkOrderId": "805",
                    "remark": "",
                    "requestTime": "2023-05-26 13:15:23",
                    "routeCode": "G230515008",
                    "routeName": "铣",
                    "startTime": "2023-05-24 14:36:19",
                    "typeName": "",
                    "updateName": "",
                    "updateTime": "2023-05-24 14:36:26"
                },
                {
                    "centerCode": "5",
                    "centerName": "1号线",
                    "createName": "",
                    "createTime": "2023-05-15 08:30:34",
                    "deviceCode": "95",
                    "deviceName": "",
                    "doQty": "1",
                    "empCode": "",
                    "empName": "",
                    "f0": "",
                    "f1": "",
                    "f2": "",
                    "f3": "",
                    "f4": "",
                    "f5": "",
                    "f6": "",
                    "f7": "",
                    "f8": "",
                    "factoryId": "0",
                    "finishTime": "2023-05-26 14:36:27",
                    "flowId": "10",
                    "id": "23772",
                    "keyword": "",
                    "materialCode": "3050201015",
                    "materialItera": "",
                    "materialName": "扣压接头",
                    "materialVer": "",
                    "orderBy": "",
                    "orderCode": "G230515008:0010-05",
                    "orderStatus": "DONE",
                    "orderType": "",
                    "planFinishTime": "2023-05-24 15:00:00",
                    "planStartTime": "2023-05-24 13:00:00",
                    "planTime_end": "",
                    "planTime_start": "",
                    "ppOrderCode": "",
                    "ppWorkOrderCode": "G230515008",
                    "ppWorkOrderId": "805",
                    "remark": "",
                    "requestTime": "2023-05-26 13:15:19",
                    "routeCode": "G230515008",
                    "routeName": "铣",
                    "startTime": "2023-05-24 14:36:20",
                    "typeName": "",
                    "updateName": "",
                    "updateTime": "2023-05-24 14:36:27"
                },
                {
                    "centerCode": "5",
                    "centerName": "1号线",
                    "createName": "",
                    "createTime": "2023-05-15 08:30:35",
                    "deviceCode": "95",
                    "deviceName": "",
                    "doQty": "1",
                    "empCode": "",
                    "empName": "",
                    "f0": "",
                    "f1": "",
                    "f2": "",
                    "f3": "",
                    "f4": "",
                    "f5": "",
                    "f6": "",
                    "f7": "",
                    "f8": "",
                    "factoryId": "0",
                    "finishTime": "2023-05-12 14:36:28",
                    "flowId": "10",
                    "id": "23773",
                    "keyword": "",
                    "materialCode": "3050201015",
                    "materialItera": "",
                    "materialName": "扣压接头",
                    "materialVer": "",
                    "orderBy": "",
                    "orderCode": "G230515008:0010-06",
                    "orderStatus": "DONE",
                    "orderType": "",
                    "planFinishTime": "2023-05-24 15:00:00",
                    "planStartTime": "2023-05-24 13:00:00",
                    "planTime_end": "",
                    "planTime_start": "",
                    "ppOrderCode": "",
                    "ppWorkOrderCode": "G230515008",
                    "ppWorkOrderId": "805",
                    "remark": "",
                    "requestTime": "2023-05-26 13:13:41",
                    "routeCode": "G230515008",
                    "routeName": "铣",
                    "startTime": "2023-05-24 14:36:20",
                    "typeName": "",
                    "updateName": "",
                    "updateTime": "2023-05-24 14:36:28"
                },
                {
                    "centerCode": "5",
                    "centerName": "1号线",
                    "createName": "",
                    "createTime": "2023-05-15 08:30:35",
                    "deviceCode": "95",
                    "deviceName": "",
                    "doQty": "1",
                    "empCode": "",
                    "empName": "",
                    "f0": "",
                    "f1": "",
                    "f2": "",
                    "f3": "",
                    "f4": "",
                    "f5": "",
                    "f6": "",
                    "f7": "",
                    "f8": "",
                    "factoryId": "0",
                    "finishTime": "2023-05-10 14:36:29",
                    "flowId": "10",
                    "id": "23774",
                    "keyword": "",
                    "materialCode": "3050201015",
                    "materialItera": "",
                    "materialName": "扣压接头",
                    "materialVer": "",
                    "orderBy": "",
                    "orderCode": "G230515008:0010-03",
                    "orderStatus": "DONE",
                    "orderType": "",
                    "planFinishTime": "2023-05-24 15:00:00",
                    "planStartTime": "2023-05-24 13:00:00",
                    "planTime_end": "",
                    "planTime_start": "",
                    "ppOrderCode": "",
                    "ppWorkOrderCode": "G230515008",
                    "ppWorkOrderId": "805",
                    "remark": "",
                    "requestTime": "2023-05-26 13:13:46",
                    "routeCode": "G230515008",
                    "routeName": "铣",
                    "startTime": "2023-05-24 14:36:20",
                    "typeName": "",
                    "updateName": "",
                    "updateTime": "2023-05-24 14:36:29"
                },
                {
                    "centerCode": "5",
                    "centerName": "1号线",
                    "createName": "",
                    "createTime": "2023-05-15 08:30:35",
                    "deviceCode": "95",
                    "deviceName": "",
                    "doQty": "1",
                    "empCode": "",
                    "empName": "",
                    "f0": "",
                    "f1": "",
                    "f2": "",
                    "f3": "",
                    "f4": "",
                    "f5": "",
                    "f6": "",
                    "f7": "",
                    "f8": "",
                    "factoryId": "0",
                    "finishTime": "2023-05-02 14:36:29",
                    "flowId": "10",
                    "id": "23775",
                    "keyword": "",
                    "materialCode": "3050201015",
                    "materialItera": "",
                    "materialName": "扣压接头",
                    "materialVer": "",
                    "orderBy": "",
                    "orderCode": "G230515008:0010-11",
                    "orderStatus": "DONE",
                    "orderType": "",
                    "planFinishTime": "2023-05-24 15:00:00",
                    "planStartTime": "2023-05-24 13:00:00",
                    "planTime_end": "",
                    "planTime_start": "",
                    "ppOrderCode": "",
                    "ppWorkOrderCode": "G230515008",
                    "ppWorkOrderId": "805",
                    "remark": "",
                    "requestTime": "2023-05-26 13:13:50",
                    "routeCode": "G230515008",
                    "routeName": "铣",
                    "startTime": "2023-05-24 14:36:20",
                    "typeName": "",
                    "updateName": "",
                    "updateTime": "2023-05-24 14:36:29"
                },
                {
                    "centerCode": "5",
                    "centerName": "1号线",
                    "createName": "",
                    "createTime": "2023-05-15 08:30:35",
                    "deviceCode": "95",
                    "deviceName": "",
                    "doQty": "1",
                    "empCode": "",
                    "empName": "",
                    "f0": "",
                    "f1": "",
                    "f2": "",
                    "f3": "",
                    "f4": "",
                    "f5": "",
                    "f6": "",
                    "f7": "",
                    "f8": "",
                    "factoryId": "0",
                    "finishTime": "2023-05-03 14:36:30",
                    "flowId": "10",
                    "id": "23776",
                    "keyword": "",
                    "materialCode": "3050201015",
                    "materialItera": "",
                    "materialName": "扣压接头",
                    "materialVer": "",
                    "orderBy": "",
                    "orderCode": "G230515008:0010-19",
                    "orderStatus": "DONE",
                    "orderType": "",
                    "planFinishTime": "2023-05-24 15:00:00",
                    "planStartTime": "2023-05-24 13:00:00",
                    "planTime_end": "",
                    "planTime_start": "",
                    "ppOrderCode": "",
                    "ppWorkOrderCode": "G230515008",
                    "ppWorkOrderId": "805",
                    "remark": "",
                    "requestTime": "2023-05-26 13:13:57",
                    "routeCode": "G230515008",
                    "routeName": "铣",
                    "startTime": "2023-05-24 14:36:20",
                    "typeName": "",
                    "updateName": "",
                    "updateTime": "2023-05-24 14:36:30"
                },
                {
                    "centerCode": "5",
                    "centerName": "1号线",
                    "createName": "",
                    "createTime": "2023-05-15 08:30:35",
                    "deviceCode": "95",
                    "deviceName": "",
                    "doQty": "1",
                    "empCode": "",
                    "empName": "",
                    "f0": "",
                    "f1": "",
                    "f2": "",
                    "f3": "",
                    "f4": "",
                    "f5": "",
                    "f6": "",
                    "f7": "",
                    "f8": "",
                    "factoryId": "0",
                    "finishTime": "2023-05-08 14:36:31",
                    "flowId": "10",
                    "id": "23778",
                    "keyword": "",
                    "materialCode": "3050201015",
                    "materialItera": "",
                    "materialName": "扣压接头",
                    "materialVer": "",
                    "orderBy": "",
                    "orderCode": "G230515008:0010-16",
                    "orderStatus": "DONE",
                    "orderType": "",
                    "planFinishTime": "2023-05-24 15:00:00",
                    "planStartTime": "2023-05-24 13:00:00",
                    "planTime_end": "",
                    "planTime_start": "",
                    "ppOrderCode": "",
                    "ppWorkOrderCode": "G230515008",
                    "ppWorkOrderId": "805",
                    "remark": "",
                    "requestTime": "2023-05-26 13:14:39",
                    "routeCode": "G230515008",
                    "routeName": "铣",
                    "startTime": "2023-05-24 14:36:21",
                    "typeName": "",
                    "updateName": "",
                    "updateTime": "2023-05-24 14:36:31"
                }
            ],
            "materialCode": "",
            "materialItera": "",
            "materialName": "",
            "materialVer": "",
            "orderBy": "",
            "orderCode": "",
            "orderStatus": "",
            "orderTime": null,
            "orderType": "",
            "planFinish": null,
            "planStart": null,
            "ppOrderCode": "",
            "remark": "",
            "requestTime": null,
            "routeCode": "",
            "routeName": "",
            "startTime": null,
            "typeName": "",
            "updateName": "",
            "updateTime": null,
            "useStatus": ""
        },
        "timestamp": "1685093722450"
    })
}


export const getResourceGantt = () => {
    Mock.mock('/tmes/kpi/readDeviceGanttData', {
        "msg": "",
        "code": "200",
        "data": {
            "deviceArray": [{
                    "lineCode": "5",
                    "planFinishTime": "2023-06-13 22:19:38",
                    "planStartTime": "2023-05-29 17:36:54",
                    "lineName": "1号线",
                    "deviceCode": "95",
                    "deviceName": "DMU 75"
                },
                {
                    "lineCode": "5",
                    "planFinishTime": "2023-05-31 16:49:38",
                    "planStartTime": "2023-05-29 17:36:54",
                    "lineName": "1号线",
                    "deviceCode": "96",
                    "deviceName": "96"
                },
                {
                    "lineCode": "5",
                    "planFinishTime": "2023-05-31 16:49:38",
                    "planStartTime": "2023-05-29 17:36:54",
                    "lineName": "1号线",
                    "deviceCode": "97",
                    "deviceName": "97"
                },
                {
                    "lineCode": "5",
                    "planFinishTime": "2023-05-16 09:38:06",
                    "planStartTime": "2023-05-29 17:36:54",
                    "lineName": "1号线",
                    "deviceCode": "98",
                    "deviceName": "98"
                },
                {
                    "lineCode": "5",
                    "planFinishTime": "2023-05-29 17:36:54",
                    "planStartTime": "2023-05-29 17:36:54",
                    "lineName": "1号线",
                    "deviceCode": "99",
                    "deviceName": "99"
                },
                {
                    "lineCode": "5",
                    "planFinishTime": "2023-05-25 20:54:02",
                    "planStartTime": "2023-05-29 17:36:54",
                    "lineName": "1号线",
                    "deviceCode": "100",
                    "deviceName": "100"
                },
                {
                    "lineCode": "5",
                    "planFinishTime": "2023-05-16 09:38:06",
                    "planStartTime": "2023-05-29 17:36:54",
                    "lineName": "1号线",
                    "deviceCode": "101",
                    "deviceName": "101"
                },
                {
                    "lineCode": "6",
                    "planFinishTime": "2023-05-29 17:36:54",
                    "planStartTime": "2023-05-29 17:36:54",
                    "lineName": "2号线",
                    "deviceCode": "4",
                    "deviceName": "DMU 100"
                },
                {
                    "lineCode": "6",
                    "planFinishTime": "2023-05-27 04:24:02",
                    "planStartTime": "2023-05-29 17:36:54",
                    "lineName": "2号线",
                    "deviceCode": "5",
                    "deviceName": "DMU 100"
                },
                {
                    "lineCode": "6",
                    "planFinishTime": "2023-05-27 04:24:02",
                    "planStartTime": "2023-05-29 17:36:54",
                    "lineName": "2号线",
                    "deviceCode": "6",
                    "deviceName": "DMU 100"
                },
                {
                    "lineCode": "6",
                    "planFinishTime": "2023-05-29 17:36:54",
                    "planStartTime": "2023-05-29 17:36:54",
                    "lineName": "2号线",
                    "deviceCode": "7",
                    "deviceName": "DMU 65"
                }
            ]
        },
        "timestamp": "1685353014165"
    })
}

export const getMessageInfoDetail = () => {
        Mock.mock('/tmes/admin/messageInfo/detail', {
            "msg": "",
            "code": "200",
            "data": {
                "weekStatis": {
                    "fRate": "0",
                    "planOrder": "6",
                    "finishTask": "4",
                    "planTask": "156"
                },
                "dayStatis": {
                    "fRate": "0",
                    "planOrder": "5",
                    "finishTask": "4",
                    "planTask": "43"
                },
                "monthStatis": {
                    "fRate": "0",
                    "planOrder": "10",
                    "finishTask": "4",
                    "planTask": "428"
                }
            },
            "timestamp": "1685950123150"
        })
    }
    // centerCode: "5"
    // factoryId: "0"
export const fetch_ProgressLine = () => {
    Mock.mock('/tmes/pp/ppAssignOrder/read/list', {
        "msg": "",
        "current": "1",
        "total": "201",
        "pages": "21",
        "code": "200",
        "size": "10",
        "rows": [{
                "centerCode": "5",
                "centerName": "1号线",
                "createName": "",
                "createTime": "2023-08-10 08:51:57",
                "deviceCode": "95",
                "deviceName": "",
                "doQty": "1",
                "empCode": "",
                "empName": "",
                "f0": "",
                "f1": "",
                "f2": "",
                "f3": "",
                "f4": "",
                "f5": "",
                "f6": "",
                "f7": "",
                "f8": "",
                "factoryId": "0",
                "finishTime": null,
                "flowId": "10",
                "id": "18926",
                "keyword": "",
                "materialCode": "11580015",
                "materialItera": "",
                "materialName": "壳体",
                "materialVer": "",
                "orderBy": "",
                "orderCode": "G230810001:0010-01",
                "orderStatus": "ONGO",
                "orderType": "NEW",
                "planFinishTime": "2023-08-10 09:51:56",
                "planStartTime": "2023-08-10 08:51:56",
                "planTime_end": "",
                "planTime_start": "",
                "ppOrderCode": "116000166",
                "ppWorkOrderCode": "G230810001",
                "ppWorkOrderId": "685",
                "remark": "",
                "requestTime": "2023-08-11 08:41:22",
                "routeCode": "G230810001",
                "routeName": "供料加工",
                "startTime": "2023-08-11 08:46:44",
                "typeName": "",
                "updateName": "",
                "updateTime": null
            },
            {
                "centerCode": "5",
                "centerName": "1号线",
                "createName": "",
                "createTime": "2023-08-10 08:51:57",
                "deviceCode": "95",
                "deviceName": "",
                "doQty": "1",
                "empCode": "",
                "empName": "",
                "f0": "",
                "f1": "",
                "f2": "",
                "f3": "",
                "f4": "",
                "f5": "",
                "f6": "",
                "f7": "",
                "f8": "",
                "factoryId": "0",
                "finishTime": null,
                "flowId": "10",
                "id": "18928",
                "keyword": "",
                "materialCode": "11580015",
                "materialItera": "",
                "materialName": "壳体",
                "materialVer": "",
                "orderBy": "",
                "orderCode": "G230810001:0010-03",
                "orderStatus": "ONGO",
                "orderType": "NEW",
                "planFinishTime": "2023-08-10 10:51:56",
                "planStartTime": "2023-08-10 09:51:56",
                "planTime_end": "",
                "planTime_start": "",
                "ppOrderCode": "116000166",
                "ppWorkOrderCode": "G230810001",
                "ppWorkOrderId": "685",
                "remark": "",
                "requestTime": "2023-08-18 12:03:05",
                "routeCode": "G230810001",
                "routeName": "供料加工",
                "startTime": "2023-08-18 12:03:52",
                "typeName": "",
                "updateName": "",
                "updateTime": null
            },
            {
                "centerCode": "5",
                "centerName": "1号线",
                "createName": "",
                "createTime": "2023-08-10 08:51:57",
                "deviceCode": "97",
                "deviceName": "",
                "doQty": "1",
                "empCode": "",
                "empName": "",
                "f0": "",
                "f1": "",
                "f2": "",
                "f3": "",
                "f4": "",
                "f5": "",
                "f6": "",
                "f7": "",
                "f8": "",
                "factoryId": "0",
                "finishTime": null,
                "flowId": "10",
                "id": "18929",
                "keyword": "",
                "materialCode": "11580015",
                "materialItera": "",
                "materialName": "壳体",
                "materialVer": "",
                "orderBy": "",
                "orderCode": "G230810001:0010-04",
                "orderStatus": "ONGO",
                "orderType": "NEW",
                "planFinishTime": "2023-08-10 10:51:56",
                "planStartTime": "2023-08-10 09:51:56",
                "planTime_end": "",
                "planTime_start": "",
                "ppOrderCode": "116000166",
                "ppWorkOrderCode": "G230810001",
                "ppWorkOrderId": "685",
                "remark": "",
                "requestTime": "2023-08-18 12:04:00",
                "routeCode": "G230810001",
                "routeName": "供料加工",
                "startTime": "2023-08-18 12:04:44",
                "typeName": "",
                "updateName": "",
                "updateTime": null
            },
        ],
        "timestamp": "1693298352042"
    })
}
export const fetch_ProgressList = () => {
    Mock.mock('/tmes/pp/ppAssignOrder/getAssignOrderDetail', {
        "msg": "",
        "code": "200",
        "data": {
            "centerCode": "5",
            "centerName": "1号线",
            "createName": "",
            "createTime": "2023-08-10 08:51:57",
            "deviceCode": "95",
            "deviceName": "",
            "doQty": "1",
            "empCode": "",
            "empName": "",
            "f0": "",
            "f1": "",
            "f2": "",
            "f3": "",
            "f4": "",
            "f5": "",
            "f6": "",
            "f7": "",
            "f8": "",
            "factoryId": "0",
            "finishTime": null,
            "flowId": "10",
            "id": "18926",
            "keyword": "",
            "listPpOperateOrder": [{
                    "createBy": "0",
                    "createName": "管控系统",
                    "createTime": "2023-08-11 09:00:19",
                    "duration": "0.12",
                    "empCode": "",
                    "empName": "",
                    "factoryId": "0",
                    "id": "18375",
                    "keyword": "",
                    "operateQty": "1",
                    "orderBy": "",
                    "orderCode": "",
                    "orderType": "ROBOT2",
                    "preId": "18374",
                    "queueId": "7",
                    "remark": "",
                    "sourceCode": "",
                    "sourceId": "18926",
                    "sourceTable": "pp_assign_order",
                    "updateName": "",
                    "updateTime": null
                },
                {
                    "createBy": "0",
                    "createName": "管控系统",
                    "createTime": "2023-08-11 08:53:02",
                    "duration": "0.01",
                    "empCode": "",
                    "empName": "",
                    "factoryId": "0",
                    "id": "18374",
                    "keyword": "",
                    "operateQty": "1",
                    "orderBy": "",
                    "orderCode": "",
                    "orderType": "ROBOT1",
                    "preId": "18373",
                    "queueId": "6",
                    "remark": "",
                    "sourceCode": "",
                    "sourceId": "18926",
                    "sourceTable": "pp_assign_order",
                    "updateName": "",
                    "updateTime": null
                },
                {
                    "createBy": "0",
                    "createName": "管控系统",
                    "createTime": "2023-08-11 08:52:30",
                    "duration": "0.04",
                    "empCode": "",
                    "empName": "",
                    "factoryId": "0",
                    "id": "18373",
                    "keyword": "",
                    "operateQty": "1",
                    "orderBy": "",
                    "orderCode": "",
                    "orderType": "AGV1",
                    "preId": "18372",
                    "queueId": "5",
                    "remark": "",
                    "sourceCode": "",
                    "sourceId": "18926",
                    "sourceTable": "pp_assign_order",
                    "updateName": "",
                    "updateTime": null
                },
                {
                    "createBy": "0",
                    "createName": "管控系统",
                    "createTime": "2023-08-11 08:50:08",
                    "duration": "0.05",
                    "empCode": "",
                    "empName": "",
                    "factoryId": "0",
                    "id": "18372",
                    "keyword": "",
                    "operateQty": "1",
                    "orderBy": "",
                    "orderCode": "",
                    "orderType": "AGV2",
                    "preId": "18371",
                    "queueId": "4",
                    "remark": "",
                    "sourceCode": "",
                    "sourceId": "18926",
                    "sourceTable": "pp_assign_order",
                    "updateName": "",
                    "updateTime": null
                },
                {
                    "createBy": "0",
                    "createName": "管控系统",
                    "createTime": "2023-08-11 08:46:57",
                    "duration": "0.0",
                    "empCode": "",
                    "empName": "",
                    "factoryId": "0",
                    "id": "18371",
                    "keyword": "",
                    "operateQty": "1",
                    "orderBy": "",
                    "orderCode": "",
                    "orderType": "AGV1",
                    "preId": "18370",
                    "queueId": "3",
                    "remark": "",
                    "sourceCode": "",
                    "sourceId": "18926",
                    "sourceTable": "pp_assign_order",
                    "updateName": "",
                    "updateTime": null
                },
                {
                    "createBy": "71",
                    "createName": "MES6",
                    "createTime": "2023-08-11 08:46:44",
                    "duration": "0.09",
                    "empCode": "",
                    "empName": "",
                    "factoryId": "0",
                    "id": "18370",
                    "keyword": "",
                    "operateQty": "1",
                    "orderBy": "",
                    "orderCode": "",
                    "orderType": "ST",
                    "preId": "18367",
                    "queueId": "2",
                    "remark": "",
                    "sourceCode": "",
                    "sourceId": "18926",
                    "sourceTable": "pp_assign_order",
                    "updateName": "",
                    "updateTime": null
                },
                {
                    "createBy": "71",
                    "createName": "MES6",
                    "createTime": "2023-08-11 08:41:22",
                    "empCode": "",
                    "empName": "",
                    "factoryId": "0",
                    "id": "18367",
                    "keyword": "",
                    "operateQty": "1",
                    "orderBy": "",
                    "orderCode": "",
                    "orderType": "RD",
                    "queueId": "1",
                    "remark": "",
                    "sourceCode": "",
                    "sourceId": "18926",
                    "sourceTable": "pp_assign_order",
                    "updateName": "",
                    "updateTime": null
                }
            ],
            "materialCode": "11580015",
            "materialItera": "",
            "materialName": "壳体",
            "materialVer": "",
            "orderBy": "",
            "orderCode": "G230810001:0010-01",
            "orderStatus": "ONGO",
            "orderType": "",
            "planFinishTime": "2023-08-10 09:51:56",
            "planStartTime": "2023-08-10 08:51:56",
            "planTime_end": "",
            "planTime_start": "",
            "ppOrderCode": "116000166",
            "ppWorkOrderCode": "G230810001",
            "ppWorkOrderId": "685",
            "remark": "",
            "requestTime": "2023-08-11 08:41:22",
            "routeCode": "G230810001",
            "routeName": "供料加工",
            "startTime": "2023-08-11 08:46:44",
            "typeName": "",
            "updateName": "",
            "updateTime": "2023-08-11 08:46:44"
        },
        "timestamp": "1693298417447"
    })
}