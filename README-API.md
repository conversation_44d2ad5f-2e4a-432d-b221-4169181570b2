# API 接口说明

## 概述

本项目使用了两种 API 调用方式：

1. **开发环境**：使用 mock 拦截器模拟 API 响应
2. **生产环境**：连接到真实后端 API

## Mock API

在开发环境中，所有 API 请求都会被 `src/utils/mockInterceptor.js` 拦截并返回模拟数据。这样可以在后端 API 尚未准备好的情况下进行前端开发。

## 切换到真实后端 API

当后端 API 准备好后，只需要进行以下步骤即可切换到真实后端 API：

### 方法一：注释掉 mock 拦截器（推荐）

1. 在 `src/main.js` 文件中注释掉 mock 拦截器的引入：

```javascript
// 引入mock拦截器 - 开发环境使用，生产环境前请注释掉此行
// import '@/utils/mockInterceptor'
```

2. 确保 `.env.development` 或 `.env.production` 文件中的 `VUE_APP_BASE_API` 配置正确指向后端 API 地址：

```
# 开发环境
VUE_APP_BASE_API = '/dev-api'  # 或者其他真实后端API地址

# 生产环境
VUE_APP_BASE_API = '/prod-api'  # 或者其他真实后端API地址
```

### 方法二：使用环境变量控制（可选）

如果您希望更灵活地控制是否使用 mock 数据，可以添加一个环境变量：

1. 在 `.env.development` 文件中添加：

```
VUE_APP_USE_MOCK = 'true'
```

2. 在 `.env.production` 文件中添加：

```
VUE_APP_USE_MOCK = 'false'
```

3. 然后在 `src/main.js` 中修改引入方式：

```javascript
// 根据环境变量决定是否使用mock拦截器
if (process.env.VUE_APP_USE_MOCK === 'true') {
  require('@/utils/mockInterceptor')
}
```

## 代理配置

如果需要，可以在 `vue.config.js` 中配置代理，以解决开发环境中的跨域问题：

```javascript
module.exports = {
  devServer: {
    proxy: {
      '/dev-api': {
        target: 'http://your-backend-api-url',
        changeOrigin: true,
        pathRewrite: {
          '^/dev-api': ''
        }
      }
    }
  }
}
```

## API 接口文件

所有 API 接口都定义在 `src/api/` 目录下的文件中，按功能模块进行组织：

- `src/api/enterprise.js` - 企业管理相关接口
- `src/api/login.js` - 登录认证相关接口
- 其他模块...

## 注意事项

1. API 路径需要与后端 API 路径保持一致
2. 在生产环境构建前，确保已注释掉 mock 拦截器的引入
3. 如果后端 API 的数据结构与 mock 数据结构不一致，需要相应调整前端代码

## 调试技巧

1. 在浏览器控制台中，可以看到以下日志：
   - `[API] 当前环境: development, API基础URL: /dev-api` - 表示当前 API 配置
   - `[Mock] Mock拦截器已启用，API请求将返回模拟数据` - 表示 mock 拦截器已启用

2. 如果您看不到 `[Mock]` 日志，说明 mock 拦截器未启用，API 请求将发送到真实后端

3. 您可以在浏览器的网络面板中查看 API 请求的详细信息
